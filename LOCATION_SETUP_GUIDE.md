# Location Feature Setup Guide

## Overview
This guide will help you implement the "Fetch My Location" feature on the PDP page using the recommended packages that work properly with React Native and Expo.

## Changes Made

### 1. Package Updates
- **Removed**: `@react-native-community/geolocation` (causing linking issues)
- **Added**: 
  - `react-native-get-location`: ^4.0.1
  - `react-native-android-location-enabler`: ^1.2.2

### 2. Permission Configuration
Updated `package.json` with proper iOS and Android permissions:

**iOS Permissions (Info.plist)**:
- `NSLocationWhenInUseUsageDescription`
- `NSLocationAlwaysUsageDescription`

**Android Permissions**:
- `ACCESS_FINE_LOCATION`
- `ACCESS_COARSE_LOCATION`
- `FOREGROUND_SERVICE`

### 3. LocationService.js Updates
- Replaced `@react-native-community/geolocation` with `react-native-get-location`
- Added Android location enabler integration
- Enhanced error handling with user-friendly alerts
- Added comprehensive logging for debugging

## Installation Steps

### Step 1: Install Dependencies
The packages have already been added to both package.json files. Run:
```bash
# Install dependencies in root
npm install

# Install dependencies in theme package
cd packages/jockey-mobile
npm install

# Go back to root
cd ../..
```

### Step 2: Clean and Rebuild (REQUIRED)
Since we've changed location packages and added permissions, you MUST take a new build:

```bash
# Clean the project
npx react-native clean

# For iOS - Install pods
cd ios && pod install && cd ..

# Rebuild the app (choose one)
npx react-native run-ios
# OR
npx react-native run-android
```

**Important**: The old `@react-native-community/geolocation` package has been removed from both package.json files and replaced with the new packages. This requires a complete rebuild.

## How It Works

### User Flow
1. User clicks "Fetch My Location" button on PDP page
2. App checks if location permission is granted
3. If not granted, requests permission with clear explanation
4. On Android, prompts user to enable location services if disabled
5. Gets current GPS coordinates
6. Reverse geocodes coordinates to get pincode
7. Auto-fills pincode and triggers EDD API
8. Shows appropriate success/error messages

### Key Features
- **Smart Permission Handling**: Only requests permission when needed
- **Android Location Enabler**: Automatically prompts users to enable GPS
- **Error Handling**: User-friendly error messages for different scenarios
- **Fallback Support**: Uses multiple geocoding services for reliability
- **Auto-storage**: Saves pincode for future use

## Testing

### Test Cases
1. **Permission Denied**: Verify proper error message
2. **Location Disabled**: Check Android location enabler prompt
3. **GPS Unavailable**: Test indoor/poor signal scenarios
4. **Network Issues**: Test geocoding fallback
5. **Valid Location**: Verify pincode extraction and EDD trigger

### Debug Logs
The updated LocationService includes comprehensive logging:
- Permission status checks
- Location fetch progress
- Geocoding results
- Error details

## Troubleshooting

### Common Issues

1. **App Crashes on Location Request**
   - Ensure you've rebuilt the app after installing new packages
   - Check that permissions are properly configured in package.json

2. **Permission Dialog Not Showing**
   - Verify iOS Info.plist permissions are set
   - Check Android permissions in package.json

3. **Location Services Not Enabling on Android**
   - Ensure `react-native-android-location-enabler` is properly installed
   - Check that the package is linked correctly

4. **Geocoding Fails**
   - Check network connectivity
   - Verify Google Maps API key if configured
   - Fallback service should work without API key

### Build Requirements
- **iOS**: Requires new build after adding Info.plist permissions
- **Android**: Requires new build after adding permissions
- **Expo**: Permissions are handled automatically through package.json configuration

## Next Steps
1. Install the packages as described above
2. Take a new build (required for permission changes)
3. Test on both iOS and Android devices
4. Verify the feature works in different scenarios (permission denied, location disabled, etc.)

The implementation is now ready and should resolve the linking issues you were experiencing with the previous geolocation package.
