{"name": "@babel/types", "version": "7.22.15", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "dependencies": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "^7.22.15", "@babel/parser": "^7.22.15", "glob": "^7.2.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}