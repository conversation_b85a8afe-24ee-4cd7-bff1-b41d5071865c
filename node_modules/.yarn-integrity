{"systemParams": "darwin-arm64-131", "modulesFolders": ["node_modules", "node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@appmaker-packages/theme-jockey-mobile@0.1.481", "@appmaker-xyz/app-config@0.3.0", "@appmaker-xyz/app-config@0.3.0", "@appmaker-xyz/core@0.4.36-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/core@0.4.36-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/files@1.0.0", "@appmaker-xyz/files@1.0.0", "@appmaker-xyz/plugins@0.2.87", "@appmaker-xyz/plugins@0.2.87", "@appmaker-xyz/react-native@0.4.46-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/react-native@0.4.46-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/remote-bundle@0.0.6-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/remote-bundle@0.0.6-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/shopify-core-theme@1.2.13-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/shopify-core-theme@1.2.13-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/shopify@0.3.68-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/shopify@0.3.68-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/themes@0.2.30", "@appmaker-xyz/themes@0.2.30", "@appmaker-xyz/ui@0.2.33-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/ui@0.2.33-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/uikit@0.2.49-expo-v2-build-test-25-07-b21607e.0", "@appmaker-xyz/uikit@0.2.49-expo-v2-build-test-25-07-b21607e.0", "@babel/core@^7.20.0", "@babel/preset-react@^7.14.5", "@babel/preset-react@^7.14.5", "@craco/craco@^6.3.0", "@craco/craco@^6.3.0", "@fluentui/react-context-selector@^0.53.4", "@fluentui/react-context-selector@^0.53.4", "@hookform/error-message@^2.0.1", "@hookform/error-message@^2.0.1", "@hookform/resolvers@^3.3.1", "@hookform/resolvers@^3.3.1", "@invertase/react-native-apple-authentication@2.2.1", "@invertase/react-native-apple-authentication@2.2.1", "@miblanchard/react-native-slider@^2.2.0", "@miblanchard/react-native-slider@^2.2.0", "@native-html/iframe-plugin@^1.1.2", "@native-html/iframe-plugin@^1.1.2", "@notifee/react-native@^7.7.1", "@notifee/react-native@^7.7.1", "@ptomasroos/react-native-multi-slider@^2.2.2", "@ptomasroos/react-native-multi-slider@^2.2.2", "@react-native-async-storage/async-storage@1.19.3", "@react-native-async-storage/async-storage@1.19.3", "@react-native-clipboard/clipboard@^1.11.2", "@react-native-clipboard/clipboard@^1.11.2", "@react-native-community/blur@^4.4.1", "@react-native-community/blur@^4.4.1", "@react-native-community/blur@^4.4.1", "@react-native-community/datetimepicker@7.2.0", "@react-native-community/datetimepicker@7.2.0", "@react-native-community/geolocation@^3.4.0", "@react-native-community/geolocation@^3.4.0", "@react-native-community/picker@^1.8.1", "@react-native-community/picker@^1.8.1", "@react-native-community/push-notification-ios@^1.11.0", "@react-native-community/push-notification-ios@^1.11.0", "@react-native-firebase/analytics@^19.2.2", "@react-native-firebase/analytics@^19.2.2", "@react-native-firebase/app@^19.2.2", "@react-native-firebase/app@^19.2.2", "@react-native-firebase/auth@^19.2.2", "@react-native-firebase/auth@^19.2.2", "@react-native-firebase/crashlytics@^19.2.2", "@react-native-firebase/crashlytics@^19.2.2", "@react-native-firebase/dynamic-links@^19.2.2", "@react-native-firebase/dynamic-links@^19.2.2", "@react-native-firebase/installations@^19.2.2", "@react-native-firebase/installations@^19.2.2", "@react-native-firebase/messaging@^19.2.2", "@react-native-firebase/messaging@^19.2.2", "@react-native-firebase/perf@^19.2.2", "@react-native-firebase/perf@^19.2.2", "@react-native-firebase/remote-config@^19.2.2", "@react-native-firebase/remote-config@^19.2.2", "@react-native-google-signin/google-signin@^10.0.1", "@react-native-google-signin/google-signin@^10.0.1", "@react-native-masked-view/masked-view@^0.2.9", "@react-native-masked-view/masked-view@^0.2.9", "@react-navigation/bottom-tabs@^6.5.8", "@react-navigation/bottom-tabs@^6.5.8", "@react-navigation/drawer@^6.6.3", "@react-navigation/drawer@^6.6.3", "@react-navigation/elements@^1.3.18", "@react-navigation/elements@^1.3.18", "@react-navigation/material-top-tabs@^6.6.14", "@react-navigation/native@^6.1.7", "@react-navigation/native@^6.1.7", "@react-navigation/stack@^6.3.17", "@react-navigation/stack@^6.3.17", "@shopify/checkout-sheet-kit@^3.2.0", "@shopify/checkout-sheet-kit@^3.2.0", "@shopify/flash-list@1.6.3", "@shopify/flash-list@1.6.3", "@tanstack/react-query@^4.28.0", "@tanstack/react-query@^4.28.0", "@twotalltotems/react-native-otp-input@^1.3.11", "@types/react-native@~0.69.1", "@types/react@~18.0.0", "@wordpress/hooks@^3.1.1", "@wordpress/hooks@^3.1.1", "abortcontroller-polyfill@^1.7.3", "abortcontroller-polyfill@^1.7.3", "axios@^1.3.5", "axios@^1.3.5", "base-64@^0.1.0", "base-64@^0.1.0", "buffer@^6.0.3", "buffer@^6.0.3", "color@^2.0.0", "color@^2.0.0", "contrast@^1.0.1", "contrast@^1.0.1", "dayjs@^1.11.2", "dayjs@^1.11.2", "del@^2.2.2", "del@^2.2.2", "ejs-browser@^3.2.2", "ejs-browser@^3.2.2", "ejs@^3.1.6", "ejs@^3.1.6", "esbuild@^0.19.3", "events@^3.3.0", "events@^3.3.0", "expo-build-properties@~0.8.3", "expo-build-properties@~0.8.3", "expo-constants@~14.4.2", "expo-constants@~14.4.2", "expo-module-scripts@^3.1.0", "expo-secure-store@^14.0.0", "expo-secure-store@^14.0.0", "expo-splash-screen@~0.20.4", "expo-splash-screen@~0.20.4", "expo-status-bar@~1.6.0", "expo-status-bar@~1.6.0", "expo@^49.0.0", "expo@^49.0.0", "flowed-st@^1.0.5", "flowed-st@^1.0.5", "global@^4.3.2", "global@^4.3.2", "google-auth-library@^0.10.0", "google-auth-library@^0.10.0", "googleapis@^19.0.0", "googleapis@^19.0.0", "graphql@^16.8.1", "graphql@^16.8.1", "html-entities@^2.3.3", "html-entities@^2.3.3", "i18next@^22.5.1", "i18next@^22.5.1", "immer@^9.0.6", "immer@^9.0.6", "intl-pluralrules@^2.0.1", "intl-pluralrules@^2.0.1", "intl@^1.2.5", "intl@^1.2.5", "json-to-graphql-query@^2.1.0", "json-to-graphql-query@^2.1.0", "lerna@^6.6.1", "lodash@^4.17.15", "lodash@^4.17.15", "lottie-react-native@5.1.6", "lottie-react-native@5.1.6", "md5@^2.2.1", "md5@^2.2.1", "moment@^2.19.1", "moment@^2.19.1", "parse@^1.10.1", "parse@^1.10.1", "prop-types@^15.7.2", "prop-types@^15.7.2", "qs@^6.10.1", "qs@^6.10.1", "query-string@^6.13.5", "query-string@^6.13.5", "react-dom@18.2.0", "react-dom@18.2.0", "react-hook-form@^7.46.1", "react-hook-form@^7.46.1", "react-i18next@^12.3.1", "react-i18next@^12.3.1", "react-mixin@^3.0.5", "react-mixin@^3.0.5", "react-native-animated-pulse@^1.0.1", "react-native-animated-pulse@^1.0.1", "react-native-auto-height-image@^3.2.4", "react-native-auto-height-image@^3.2.4", "react-native-autoheight-webview@^1.6.4", "react-native-autoheight-webview@^1.6.4", "react-native-base64@^0.2.1", "react-native-base64@^0.2.1", "react-native-checkbox-field@git+https://github.com/Appmaker-xyz/react-native-checkbox-field.git", "react-native-checkbox-field@git+https://github.com/Appmaker-xyz/react-native-checkbox-field.git", "react-native-circular-progress@1.4.0", "react-native-collapsible@^1.6.2", "react-native-countdown-component@^2.7.1", "react-native-countdown-component@^2.7.1", "react-native-debug-stylesheet@^0.1.1", "react-native-debug-stylesheet@^0.1.1", "react-native-default-preference@^1.4.4", "react-native-default-preference@^1.4.4", "react-native-deprecated-custom-components@^0.1.0", "react-native-deprecated-custom-components@^0.1.0", "react-native-drawer@^2.5.0", "react-native-drawer@^2.5.0", "react-native-dropdown-select-list@^2.0.2", "react-native-dropdown-select-list@^2.0.2", "react-native-element-dropdown@^1.8.12", "react-native-element-dropdown@^1.8.12", "react-native-event-listeners@^1.0.7", "react-native-event-listeners@^1.0.7", "react-native-exception-handler@^2.9.0", "react-native-exception-handler@^2.9.0", "react-native-fast-image@^8.6.3", "react-native-fast-image@^8.6.3", "react-native-fbsdk-next@^12.1.0", "react-native-fbsdk-next@^12.1.0", "react-native-gesture-handler@2.12.1", "react-native-gesture-handler@2.12.1", "react-native-i18n@2.0.14", "react-native-i18n@2.0.14", "react-native-image-zoom-viewer@^3.0.1", "react-native-image-zoom-viewer@^3.0.1", "react-native-in-app-review@^4.3.3", "react-native-in-app-review@^4.3.3", "react-native-json-tree@^1.3.0", "react-native-json-tree@^1.3.0", "react-native-linear-gradient@^2.6.2", "react-native-linear-gradient@^2.6.2", "react-native-localize@^2.2.6", "react-native-localize@^2.2.6", "react-native-material-ripple@^0.9.1", "react-native-message-bar@^1.6.0", "react-native-message-bar@^1.6.0", "react-native-mmkv@2.12.2", "react-native-mmkv@2.12.2", "react-native-modal-datetime-picker@^15.0.1", "react-native-modal-datetime-picker@^15.0.1", "react-native-modal@^13.0.1", "react-native-modal@^13.0.1", "react-native-modalbox@^1.7.1", "react-native-modalbox@^1.7.1", "react-native-otp-verify@^1.1.6", "react-native-otp-verify@^1.1.6", "react-native-otp-verify@^1.1.6", "react-native-pager-view@6.2.0", "react-native-pager-view@6.2.0", "react-native-permissions@^4.1.1", "react-native-permissions@^4.1.1", "react-native-phone-number-input@^2.1.0", "react-native-phone-number-input@^2.1.0", "react-native-raw-bottom-sheet@^2.2.0", "react-native-raw-bottom-sheet@^2.2.0", "react-native-reanimated-carousel@^3.5.1", "react-native-reanimated@~3.3.0", "react-native-reanimated@~3.3.0", "react-native-render-html@^5.1.0", "react-native-render-html@^5.1.0", "react-native-responsive-fontsize@^0.5.1", "react-native-responsive-fontsize@^0.5.1", "react-native-responsive-screen@^1.4.2", "react-native-restart@^0.0.27", "react-native-restart@^0.0.27", "react-native-safe-area-context@4.6.3", "react-native-safe-area-context@4.6.3", "react-native-screens@~3.22.0", "react-native-screens@~3.22.0", "react-native-select-input-ios@^2.0.5", "react-native-select-input-ios@^2.0.5", "react-native-sha256@^1.4.7", "react-native-sha256@^1.4.7", "react-native-shimmer-placeholder@^2.0.9", "react-native-shimmer-placeholder@^2.0.9", "react-native-simple-store@^2.0.2", "react-native-simple-store@^2.0.2", "react-native-skeleton-placeholder@^5.2.4", "react-native-skeleton-placeholder@^5.2.4", "react-native-snackbar@^2.6.2", "react-native-snackbar@^2.6.2", "react-native-snap-carousel@3.9.1", "react-native-snapmint3@^1.0.49", "react-native-svg-transformer@^1.0.0", "react-native-svg@13.9.0", "react-native-svg@13.9.0", "react-native-swiper-flatlist@^3.0.16", "react-native-swiper-flatlist@^3.0.16", "react-native-swiper@^1.6.0", "react-native-swiper@^1.6.0", "react-native-tab-view@^3.5.2", "react-native-tab-view@^3.5.2", "react-native-tracking-transparency@^0.1.1", "react-native-tracking-transparency@^0.1.1", "react-native-unistyles@^1.0.0-rc.1", "react-native-unistyles@^1.0.0-rc.1", "react-native-vector-icons@^6.6.0", "react-native-vector-icons@^6.6.0", "react-native-video@^5.2.1", "react-native-video@^5.2.1", "react-native-web@~0.19.6", "react-native-web@~0.19.6", "react-native-webview@13.2.2", "react-native-webview@13.2.2", "react-native@0.72.1", "react-native@0.72.1", "react-query-native-devtools@^4.0.0", "react-query-native-devtools@^4.0.0", "react-redux@^5.0.4", "react-redux@^5.0.4", "react-scripts@^4.0.3", "react-scripts@^4.0.3", "react-timer-mixin@^0.13.3", "react-timer-mixin@^0.13.3", "react-toastify@^8.0.3", "react-toastify@^8.0.3", "react@18.2.0", "react@18.2.0", "recyclerlistview@^4.1.3", "recyclerlistview@^4.1.3", "redux-logger@^2.10.2", "redux-logger@^2.10.2", "redux-persist@^3.5.0", "redux-persist@^3.5.0", "redux-thunk@^2.2.0", "redux-thunk@^2.2.0", "redux@^3.7.2", "redux@^3.7.2", "release-it@^15.11.0", "reselect@^2.5.4", "reselect@^2.5.4", "simpler-state@^1.0.3", "simpler-state@^1.0.3", "striptags@^3.2.0", "striptags@^3.2.0", "toggle-switch-react-native@^3.3.0", "typescript@^5.1.3", "unescape@^1.0.1", "unescape@^1.0.1", "url@^0.11.0", "url@^0.11.0", "use-immer@^0.8.1", "use-immer@^0.8.1", "yup@^0.32.11", "yup@^0.32.11", "zod@^3.20.6", "zod@^3.20.6", "zustand@^3.5.12", "zustand@^3.5.12"], "lockfileEntries": {"@aashutoshrathi/word-wrap@^1.2.3": "https://registry.npmjs.org/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "@appmaker-xyz/app-config@0.3.0": "https://flash.appmaker.xyz/@appmaker-xyz/app-config/-/app-config-0.3.0.tgz", "@appmaker-xyz/core@0.4.36-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/core/-/core-0.4.36-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/files@1.0.0": "https://flash.appmaker.xyz/@appmaker-xyz/files/-/files-1.0.0.tgz", "@appmaker-xyz/plugins@0.2.87": "https://flash.appmaker.xyz/@appmaker-xyz/plugins/-/plugins-0.2.87.tgz", "@appmaker-xyz/react-native@0.4.46-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/react-native/-/react-native-0.4.46-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/remote-bundle@0.0.6-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/remote-bundle/-/remote-bundle-0.0.6-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/shopify-core-theme@1.2.13-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/shopify-core-theme/-/shopify-core-theme-1.2.13-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/shopify@0.3.68-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/shopify/-/shopify-0.3.68-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/themes@0.2.30": "https://flash.appmaker.xyz/@appmaker-xyz/themes/-/themes-0.2.30.tgz", "@appmaker-xyz/ui@0.2.33-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/ui/-/ui-0.2.33-expo-v2-build-test-25-07-b21607e.0.tgz", "@appmaker-xyz/uikit@0.2.49-expo-v2-build-test-25-07-b21607e.0": "https://flash.appmaker.xyz/@appmaker-xyz/uikit/-/uikit-0.2.49-expo-v2-build-test-25-07-b21607e.0.tgz", "@babel/cli@^7.1.2": "https://registry.npmjs.org/@babel/cli/-/cli-7.23.4.tgz", "@babel/code-frame@7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "@babel/code-frame@7.12.11": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.13.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.5.tgz", "@babel/code-frame@^7.18.6": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.22.13": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.13.tgz", "@babel/code-frame@^7.22.5": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.5.tgz", "@babel/code-frame@^7.5.5": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.13.tgz", "@babel/code-frame@~7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "@babel/compat-data@^7.17.7": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.0.tgz", "@babel/compat-data@^7.20.5": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.0.tgz", "@babel/compat-data@^7.22.5": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.5.tgz", "@babel/compat-data@^7.22.6": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz", "@babel/compat-data@^7.22.9": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz", "@babel/core@7.12.3": "https://registry.npmjs.org/@babel/core/-/core-7.12.3.tgz", "@babel/core@^7.1.0": "https://registry.npmjs.org/@babel/core/-/core-7.22.15.tgz", "@babel/core@^7.11.6": "https://registry.npmjs.org/@babel/core/-/core-7.22.17.tgz", "@babel/core@^7.12.3": "https://registry.npmjs.org/@babel/core/-/core-7.22.15.tgz", "@babel/core@^7.13.16": "https://registry.npmjs.org/@babel/core/-/core-7.22.5.tgz", "@babel/core@^7.16.0": "https://registry.npmjs.org/@babel/core/-/core-7.22.15.tgz", "@babel/core@^7.19.6": "https://registry.npmjs.org/@babel/core/-/core-7.22.5.tgz", "@babel/core@^7.20.0": "https://registry.npmjs.org/@babel/core/-/core-7.21.3.tgz", "@babel/core@^7.7.5": "https://registry.npmjs.org/@babel/core/-/core-7.22.15.tgz", "@babel/core@^7.8.4": "https://registry.npmjs.org/@babel/core/-/core-7.22.15.tgz", "@babel/generator@^7.12.1": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.15.tgz", "@babel/generator@^7.20.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.5.tgz", "@babel/generator@^7.21.3": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.3.tgz", "@babel/generator@^7.22.15": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.15.tgz", "@babel/generator@^7.22.5": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.5.tgz", "@babel/generator@^7.7.2": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.6.tgz", "@babel/helper-annotate-as-pure@^7.18.6": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "@babel/helper-annotate-as-pure@^7.22.5": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz", "@babel/helper-builder-binary-assignment-operator-visitor@^7.22.5": "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.22.5.tgz", "@babel/helper-compilation-targets@^7.17.7": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz", "@babel/helper-compilation-targets@^7.18.9": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz", "@babel/helper-compilation-targets@^7.20.7": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz", "@babel/helper-compilation-targets@^7.22.15": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.15.tgz", "@babel/helper-compilation-targets@^7.22.5": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.5.tgz", "@babel/helper-compilation-targets@^7.22.6": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.15.tgz", "@babel/helper-create-class-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.0.tgz", "@babel/helper-create-class-features-plugin@^7.21.0": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.0.tgz", "@babel/helper-create-class-features-plugin@^7.22.11": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.15.tgz", "@babel/helper-create-class-features-plugin@^7.22.15": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.15.tgz", "@babel/helper-create-class-features-plugin@^7.22.5": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.5.tgz", "@babel/helper-create-regexp-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.0.tgz", "@babel/helper-create-regexp-features-plugin@^7.22.5": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.5.tgz", "@babel/helper-define-polyfill-provider@^0.3.3": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz", "@babel/helper-define-polyfill-provider@^0.4.0": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.0.tgz", "@babel/helper-define-polyfill-provider@^0.4.2": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.2.tgz", "@babel/helper-environment-visitor@^7.18.9": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "@babel/helper-environment-visitor@^7.22.5": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "@babel/helper-function-name@^7.18.9": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "@babel/helper-function-name@^7.19.0": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "@babel/helper-function-name@^7.21.0": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "@babel/helper-function-name@^7.22.5": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "@babel/helper-hoist-variables@^7.18.6": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "@babel/helper-hoist-variables@^7.22.5": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "@babel/helper-member-expression-to-functions@^7.20.7": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.0.tgz", "@babel/helper-member-expression-to-functions@^7.21.0": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.0.tgz", "@babel/helper-member-expression-to-functions@^7.22.15": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.22.15.tgz", "@babel/helper-member-expression-to-functions@^7.22.5": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.22.5.tgz", "@babel/helper-module-imports@^7.0.0": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "@babel/helper-module-imports@^7.18.6": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-imports@^7.22.15": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "@babel/helper-module-imports@^7.22.5": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "@babel/helper-module-transforms@^7.12.1": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.15.tgz", "@babel/helper-module-transforms@^7.21.2": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.2.tgz", "@babel/helper-module-transforms@^7.22.15": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.15.tgz", "@babel/helper-module-transforms@^7.22.17": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.17.tgz", "@babel/helper-module-transforms@^7.22.5": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.5.tgz", "@babel/helper-module-transforms@^7.22.9": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.15.tgz", "@babel/helper-optimise-call-expression@^7.18.6": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz", "@babel/helper-optimise-call-expression@^7.22.5": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.16.7": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.18.9": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.19.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.20.2": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.22.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.8.3": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-remap-async-to-generator@^7.18.9": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz", "@babel/helper-remap-async-to-generator@^7.22.5": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.5.tgz", "@babel/helper-remap-async-to-generator@^7.22.9": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.9.tgz", "@babel/helper-replace-supers@^7.20.7": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.20.7.tgz", "@babel/helper-replace-supers@^7.22.5": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.5.tgz", "@babel/helper-replace-supers@^7.22.9": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.9.tgz", "@babel/helper-simple-access@^7.20.2": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "@babel/helper-simple-access@^7.22.5": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.20.0": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.22.5": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz", "@babel/helper-split-export-declaration@^7.18.6": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "@babel/helper-split-export-declaration@^7.22.5": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.5.tgz", "@babel/helper-split-export-declaration@^7.22.6": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "@babel/helper-string-parser@^7.19.4": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "@babel/helper-string-parser@^7.22.5": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "@babel/helper-string-parser@^7.23.4": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz", "@babel/helper-validator-identifier@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-identifier@^7.19.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-identifier@^7.22.15": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.15.tgz", "@babel/helper-validator-identifier@^7.22.20": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz", "@babel/helper-validator-identifier@^7.22.5": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "@babel/helper-validator-option@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "@babel/helper-validator-option@^7.22.15": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.15.tgz", "@babel/helper-validator-option@^7.22.5": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "@babel/helper-wrap-function@^7.18.9": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz", "@babel/helper-wrap-function@^7.22.5": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.5.tgz", "@babel/helper-wrap-function@^7.22.9": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.10.tgz", "@babel/helpers@^7.12.1": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.15.tgz", "@babel/helpers@^7.21.0": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.21.0.tgz", "@babel/helpers@^7.22.15": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.15.tgz", "@babel/helpers@^7.22.5": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.5.tgz", "@babel/highlight@^7.10.4": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/highlight@^7.18.6": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/highlight@^7.22.13": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.13.tgz", "@babel/highlight@^7.22.5": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.5.tgz", "@babel/parser@^7.1.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "@babel/parser@^7.12.3": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "@babel/parser@^7.13.16": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.5.tgz", "@babel/parser@^7.14.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "@babel/parser@^7.20.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.5.tgz", "@babel/parser@^7.20.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.3.tgz", "@babel/parser@^7.21.3": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.3.tgz", "@babel/parser@^7.22.15": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "@babel/parser@^7.22.16": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.16.tgz", "@babel/parser@^7.22.5": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.5.tgz", "@babel/parser@^7.7.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.22.15": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.15.tgz", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.22.5": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.5.tgz", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.22.15": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.22.15.tgz", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.22.5": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.22.5.tgz", "@babel/plugin-proposal-async-generator-functions@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz", "@babel/plugin-proposal-class-properties@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-class-properties@^7.13.0": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-class-properties@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-class-properties@^7.18.0": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-decorators@^7.12.9": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.0.tgz", "@babel/plugin-proposal-decorators@^7.16.4": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.15.tgz", "@babel/plugin-proposal-export-default-from@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.18.10.tgz", "@babel/plugin-proposal-export-namespace-from@^7.18.9": "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.13.8": "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.18.0": "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-numeric-separator@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz", "@babel/plugin-proposal-numeric-separator@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz", "@babel/plugin-proposal-object-rest-spread@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "@babel/plugin-proposal-object-rest-spread@^7.12.13": "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "@babel/plugin-proposal-object-rest-spread@^7.20.0": "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "@babel/plugin-proposal-optional-catch-binding@^7.0.0": "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz", "@babel/plugin-proposal-optional-chaining@^7.13.12": "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "@babel/plugin-proposal-optional-chaining@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "@babel/plugin-proposal-optional-chaining@^7.20.0": "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "@babel/plugin-proposal-private-methods@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz", "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "@babel/plugin-proposal-unicode-property-regex@^7.4.4": "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-properties@^7.12.13": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-properties@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-static-block@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "@babel/plugin-syntax-decorators@^7.21.0": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.21.0.tgz", "@babel/plugin-syntax-decorators@^7.22.10": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.22.10.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.0": "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-export-default-from@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.18.6.tgz", "@babel/plugin-syntax-export-default-from@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.18.6.tgz", "@babel/plugin-syntax-export-namespace-from@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "@babel/plugin-syntax-flow@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.22.5.tgz", "@babel/plugin-syntax-flow@^7.12.1": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.22.5.tgz", "@babel/plugin-syntax-flow@^7.18.0": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.22.5.tgz", "@babel/plugin-syntax-flow@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.18.6.tgz", "@babel/plugin-syntax-flow@^7.22.5": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.22.5.tgz", "@babel/plugin-syntax-import-assertions@^7.22.5": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.22.5.tgz", "@babel/plugin-syntax-import-attributes@^7.22.5": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.22.5.tgz", "@babel/plugin-syntax-import-meta@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-import-meta@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "@babel/plugin-syntax-jsx@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz", "@babel/plugin-syntax-jsx@^7.22.5": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "@babel/plugin-syntax-jsx@^7.7.2": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.23.3.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-numeric-separator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.0.0": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-private-property-in-object@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.20.0": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.20.0.tgz", "@babel/plugin-syntax-typescript@^7.22.5": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.22.5.tgz", "@babel/plugin-syntax-typescript@^7.7.2": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.23.3.tgz", "@babel/plugin-syntax-unicode-sets-regex@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "@babel/plugin-transform-arrow-functions@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.20.7.tgz", "@babel/plugin-transform-arrow-functions@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.22.5.tgz", "@babel/plugin-transform-async-generator-functions@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.15.tgz", "@babel/plugin-transform-async-generator-functions@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.5.tgz", "@babel/plugin-transform-async-to-generator@^7.20.0": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.22.5.tgz", "@babel/plugin-transform-async-to-generator@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.22.5.tgz", "@babel/plugin-transform-block-scoped-functions@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.22.5.tgz", "@babel/plugin-transform-block-scoped-functions@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.22.5.tgz", "@babel/plugin-transform-block-scoping@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz", "@babel/plugin-transform-block-scoping@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.15.tgz", "@babel/plugin-transform-block-scoping@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.5.tgz", "@babel/plugin-transform-class-properties@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.22.5.tgz", "@babel/plugin-transform-class-static-block@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.11.tgz", "@babel/plugin-transform-class-static-block@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.5.tgz", "@babel/plugin-transform-classes@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz", "@babel/plugin-transform-classes@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.15.tgz", "@babel/plugin-transform-classes@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.5.tgz", "@babel/plugin-transform-computed-properties@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.20.7.tgz", "@babel/plugin-transform-computed-properties@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.22.5.tgz", "@babel/plugin-transform-destructuring@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.21.3.tgz", "@babel/plugin-transform-destructuring@^7.20.0": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.22.5.tgz", "@babel/plugin-transform-destructuring@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.22.15.tgz", "@babel/plugin-transform-destructuring@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.22.5.tgz", "@babel/plugin-transform-dotall-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.22.5.tgz", "@babel/plugin-transform-dotall-regex@^7.4.4": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz", "@babel/plugin-transform-duplicate-keys@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.22.5.tgz", "@babel/plugin-transform-dynamic-import@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.11.tgz", "@babel/plugin-transform-dynamic-import@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.5.tgz", "@babel/plugin-transform-exponentiation-operator@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.22.5.tgz", "@babel/plugin-transform-export-namespace-from@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.11.tgz", "@babel/plugin-transform-export-namespace-from@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.5.tgz", "@babel/plugin-transform-flow-strip-types@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.21.0.tgz", "@babel/plugin-transform-flow-strip-types@^7.16.0": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.22.5.tgz", "@babel/plugin-transform-flow-strip-types@^7.20.0": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.22.5.tgz", "@babel/plugin-transform-flow-strip-types@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.22.5.tgz", "@babel/plugin-transform-for-of@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.0.tgz", "@babel/plugin-transform-for-of@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.22.15.tgz", "@babel/plugin-transform-for-of@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.22.5.tgz", "@babel/plugin-transform-function-name@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz", "@babel/plugin-transform-function-name@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.22.5.tgz", "@babel/plugin-transform-json-strings@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.22.11.tgz", "@babel/plugin-transform-json-strings@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.22.5.tgz", "@babel/plugin-transform-literals@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz", "@babel/plugin-transform-literals@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.22.5.tgz", "@babel/plugin-transform-logical-assignment-operators@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.11.tgz", "@babel/plugin-transform-logical-assignment-operators@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.5.tgz", "@babel/plugin-transform-member-expression-literals@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.22.5.tgz", "@babel/plugin-transform-member-expression-literals@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.22.5.tgz", "@babel/plugin-transform-modules-amd@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.22.5.tgz", "@babel/plugin-transform-modules-commonjs@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.2.tgz", "@babel/plugin-transform-modules-commonjs@^7.13.8": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.22.5.tgz", "@babel/plugin-transform-modules-commonjs@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.22.15.tgz", "@babel/plugin-transform-modules-commonjs@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.22.5.tgz", "@babel/plugin-transform-modules-systemjs@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.11.tgz", "@babel/plugin-transform-modules-systemjs@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.5.tgz", "@babel/plugin-transform-modules-umd@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.22.5.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz", "@babel/plugin-transform-new-target@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.22.5.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.11.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.5.tgz", "@babel/plugin-transform-numeric-separator@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.11.tgz", "@babel/plugin-transform-numeric-separator@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.5.tgz", "@babel/plugin-transform-object-assign@^7.16.7": "https://registry.npmjs.org/@babel/plugin-transform-object-assign/-/plugin-transform-object-assign-7.22.5.tgz", "@babel/plugin-transform-object-rest-spread@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.15.tgz", "@babel/plugin-transform-object-rest-spread@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.5.tgz", "@babel/plugin-transform-object-super@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.22.5.tgz", "@babel/plugin-transform-object-super@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.22.5.tgz", "@babel/plugin-transform-optional-catch-binding@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.11.tgz", "@babel/plugin-transform-optional-catch-binding@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.5.tgz", "@babel/plugin-transform-optional-chaining@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.15.tgz", "@babel/plugin-transform-optional-chaining@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.5.tgz", "@babel/plugin-transform-parameters@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz", "@babel/plugin-transform-parameters@^7.20.7": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz", "@babel/plugin-transform-parameters@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.15.tgz", "@babel/plugin-transform-parameters@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.5.tgz", "@babel/plugin-transform-private-methods@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.22.5.tgz", "@babel/plugin-transform-private-property-in-object@^7.22.11": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.11.tgz", "@babel/plugin-transform-private-property-in-object@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.5.tgz", "@babel/plugin-transform-property-literals@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.22.5.tgz", "@babel/plugin-transform-property-literals@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.22.5.tgz", "@babel/plugin-transform-react-constant-elements@^7.12.1": "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.22.5.tgz", "@babel/plugin-transform-react-display-name@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.18.6.tgz", "@babel/plugin-transform-react-display-name@^7.16.0": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.22.5.tgz", "@babel/plugin-transform-react-display-name@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.22.5.tgz", "@babel/plugin-transform-react-jsx-development@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.22.5.tgz", "@babel/plugin-transform-react-jsx-self@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.0.tgz", "@babel/plugin-transform-react-jsx-source@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.19.6.tgz", "@babel/plugin-transform-react-jsx@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.0.tgz", "@babel/plugin-transform-react-jsx@^7.12.17": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.0.tgz", "@babel/plugin-transform-react-jsx@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.15.tgz", "@babel/plugin-transform-react-jsx@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.15.tgz", "@babel/plugin-transform-react-pure-annotations@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.22.5.tgz", "@babel/plugin-transform-regenerator@^7.22.10": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.22.10.tgz", "@babel/plugin-transform-regenerator@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.22.5.tgz", "@babel/plugin-transform-reserved-words@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.22.5.tgz", "@babel/plugin-transform-runtime@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.21.0.tgz", "@babel/plugin-transform-runtime@^7.16.4": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.22.15.tgz", "@babel/plugin-transform-shorthand-properties@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz", "@babel/plugin-transform-shorthand-properties@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.22.5.tgz", "@babel/plugin-transform-spread@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz", "@babel/plugin-transform-spread@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.22.5.tgz", "@babel/plugin-transform-sticky-regex@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz", "@babel/plugin-transform-sticky-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.22.5.tgz", "@babel/plugin-transform-template-literals@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz", "@babel/plugin-transform-template-literals@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.22.5.tgz", "@babel/plugin-transform-typeof-symbol@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.22.5.tgz", "@babel/plugin-transform-typescript@^7.22.15": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.15.tgz", "@babel/plugin-transform-typescript@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.5.tgz", "@babel/plugin-transform-typescript@^7.5.0": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.3.tgz", "@babel/plugin-transform-unicode-escapes@^7.22.10": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.22.10.tgz", "@babel/plugin-transform-unicode-escapes@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.22.5.tgz", "@babel/plugin-transform-unicode-property-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.22.5.tgz", "@babel/plugin-transform-unicode-regex@^7.0.0": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz", "@babel/plugin-transform-unicode-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.22.5.tgz", "@babel/plugin-transform-unicode-sets-regex@^7.22.5": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.22.5.tgz", "@babel/preset-env@^7.12.1": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.22.15.tgz", "@babel/preset-env@^7.16.4": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.22.15.tgz", "@babel/preset-env@^7.20.0": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.22.5.tgz", "@babel/preset-env@^7.8.4": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.22.15.tgz", "@babel/preset-flow@^7.13.13": "https://registry.npmjs.org/@babel/preset-flow/-/preset-flow-7.22.5.tgz", "@babel/preset-modules@0.1.6-no-external-plugins": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "@babel/preset-modules@^0.1.5": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz", "@babel/preset-react@^7.12.5": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.15.tgz", "@babel/preset-react@^7.14.5": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.15.tgz", "@babel/preset-react@^7.16.0": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.15.tgz", "@babel/preset-typescript@^7.13.0": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.5.tgz", "@babel/preset-typescript@^7.16.0": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.15.tgz", "@babel/preset-typescript@^7.16.7": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.5.tgz", "@babel/register@^7.13.16": "https://registry.npmjs.org/@babel/register/-/register-7.22.5.tgz", "@babel/regjsgen@^0.8.0": "https://registry.npmjs.org/@babel/regjsgen/-/regjsgen-0.8.0.tgz", "@babel/runtime@^7.0.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.1.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.12.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.13.10": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.15.4": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.16.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.16.3": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.18.6": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.20.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.20.6": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.5.tgz", "@babel/runtime@^7.20.7": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.5.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.22.15.tgz", "@babel/runtime@^7.8.4": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.21.0.tgz", "@babel/template@^7.0.0": "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz", "@babel/template@^7.10.4": "https://registry.npmjs.org/@babel/template/-/template-7.22.15.tgz", "@babel/template@^7.18.10": "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz", "@babel/template@^7.20.7": "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz", "@babel/template@^7.22.15": "https://registry.npmjs.org/@babel/template/-/template-7.22.15.tgz", "@babel/template@^7.22.5": "https://registry.npmjs.org/@babel/template/-/template-7.22.5.tgz", "@babel/template@^7.3.3": "https://registry.npmjs.org/@babel/template/-/template-7.22.15.tgz", "@babel/traverse@^7.1.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.15.tgz", "@babel/traverse@^7.12.1": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.15.tgz", "@babel/traverse@^7.20.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.5.tgz", "@babel/traverse@^7.20.5": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.3.tgz", "@babel/traverse@^7.20.7": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.3.tgz", "@babel/traverse@^7.21.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.3.tgz", "@babel/traverse@^7.21.2": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.3.tgz", "@babel/traverse@^7.21.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.3.tgz", "@babel/traverse@^7.22.15": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.15.tgz", "@babel/traverse@^7.22.17": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.17.tgz", "@babel/traverse@^7.22.5": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.5.tgz", "@babel/traverse@^7.7.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.15.tgz", "@babel/types@^7.0.0": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.12.1": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.12.6": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.18.6": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.18.9": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.20.0": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.20.2": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.20.5": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.20.7": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.21.0": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.21.2": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.21.3": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.22.10": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.22.15": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.22.17": "https://registry.npmjs.org/@babel/types/-/types-7.22.17.tgz", "@babel/types@^7.22.5": "https://registry.npmjs.org/@babel/types/-/types-7.22.5.tgz", "@babel/types@^7.23.6": "https://registry.npmjs.org/@babel/types/-/types-7.23.9.tgz", "@babel/types@^7.3.3": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@babel/types@^7.4.4": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "@babel/types@^7.7.0": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "@callstack/react-theme-provider@3.0.3": "https://registry.npmjs.org/@callstack/react-theme-provider/-/react-theme-provider-3.0.3.tgz", "@cnakazawa/watch@^1.0.3": "https://registry.npmjs.org/@cnakazawa/watch/-/watch-1.0.4.tgz", "@craco/craco@^6.3.0": "https://registry.npmjs.org/@craco/craco/-/craco-6.4.5.tgz", "@cspotcode/source-map-support@^0.8.0": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "@csstools/convert-colors@^1.4.0": "https://registry.npmjs.org/@csstools/convert-colors/-/convert-colors-1.4.0.tgz", "@csstools/normalize.css@^10.1.0": "https://registry.npmjs.org/@csstools/normalize.css/-/normalize.css-10.1.0.tgz", "@egjs/hammerjs@^2.0.17": "https://registry.npmjs.org/@egjs/hammerjs/-/hammerjs-2.0.17.tgz", "@esbuild/android-arm64@0.19.3": "https://registry.yarnpkg.com/@esbuild/android-arm64/-/android-arm64-0.19.3.tgz#91a3b1b4a68c01ffdd5d8ffffb0a83178a366ae0", "@esbuild/android-arm@0.19.3": "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.19.3.tgz#08bd09f2ebc312422f4e94ae954821f9cf37b39e", "@esbuild/android-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/android-x64/-/android-x64-0.19.3.tgz#b1dffec99ed5505fc57561e8758b449dba4924fe", "@esbuild/darwin-arm64@0.19.3": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.19.3.tgz", "@esbuild/darwin-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/darwin-x64/-/darwin-x64-0.19.3.tgz#ebe99f35049180023bb37999bddbe306b076a484", "@esbuild/freebsd-arm64@0.19.3": "https://registry.yarnpkg.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.3.tgz#cf8b58ba5173440ea6124a3d0278bfe4ce181c20", "@esbuild/freebsd-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/freebsd-x64/-/freebsd-x64-0.19.3.tgz#3f283099810ef1b8468cd1a9400c042e3f12e2a7", "@esbuild/linux-arm64@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-arm64/-/linux-arm64-0.19.3.tgz#a8b3aa69653ac504a51aa73739fb06de3a04d1ff", "@esbuild/linux-arm@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-arm/-/linux-arm-0.19.3.tgz#ff6a2f68d4fc3ab46f614bca667a1a81ed6eea26", "@esbuild/linux-ia32@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-ia32/-/linux-ia32-0.19.3.tgz#5813baf70e406304e8931b200e39d0293b488073", "@esbuild/linux-loong64@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.19.3.tgz#21110f29b5e31dc865c7253fde8a2003f7e8b6fd", "@esbuild/linux-mips64el@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-mips64el/-/linux-mips64el-0.19.3.tgz#4530fc416651eadeb1acc27003c00eac769eb8fd", "@esbuild/linux-ppc64@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.3.tgz#facf910b0d397e391b37b01a1b4f6e363b04e56b", "@esbuild/linux-riscv64@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-riscv64/-/linux-riscv64-0.19.3.tgz#4a67abe97a495430d5867340982f5424a64f2aac", "@esbuild/linux-s390x@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-s390x/-/linux-s390x-0.19.3.tgz#c5fb47474b9f816d81876c119dbccadf671cc5f6", "@esbuild/linux-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/linux-x64/-/linux-x64-0.19.3.tgz#f22d659969ab78dc422f1df8d9a79bc1e7b12ee3", "@esbuild/netbsd-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/netbsd-x64/-/netbsd-x64-0.19.3.tgz#e9b046934996991f46b8c1cadac815aa45f84fd4", "@esbuild/openbsd-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/openbsd-x64/-/openbsd-x64-0.19.3.tgz#b287ef4841fc1067bbbd9a60549e8f9cf1b7ee3a", "@esbuild/sunos-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/sunos-x64/-/sunos-x64-0.19.3.tgz#b2b8ba7d27907c7245f6e57dc62f3b88693f84b0", "@esbuild/win32-arm64@0.19.3": "https://registry.yarnpkg.com/@esbuild/win32-arm64/-/win32-arm64-0.19.3.tgz#1974c8c180c9add4962235662c569fcc4c8f43dd", "@esbuild/win32-ia32@0.19.3": "https://registry.yarnpkg.com/@esbuild/win32-ia32/-/win32-ia32-0.19.3.tgz#b02cc2dd8b6aed042069680f01f45fdfd3de5bc4", "@esbuild/win32-x64@0.19.3": "https://registry.yarnpkg.com/@esbuild/win32-x64/-/win32-x64-0.19.3.tgz#e5036be529f757e58d9a7771f2f1b14782986a74", "@eslint-community/eslint-utils@^4.4.0": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz", "@eslint-community/regexpp@^4.5.1": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.0.tgz", "@eslint/eslintrc@^0.4.3": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.3.tgz", "@expo/bunyan@4.0.0": "https://registry.npmjs.org/@expo/bunyan/-/bunyan-4.0.0.tgz", "@expo/bunyan@^4.0.0": "https://registry.npmjs.org/@expo/bunyan/-/bunyan-4.0.0.tgz", "@expo/cli@0.10.9": "https://registry.npmjs.org/@expo/cli/-/cli-0.10.9.tgz", "@expo/code-signing-certificates@0.0.5": "https://registry.npmjs.org/@expo/code-signing-certificates/-/code-signing-certificates-0.0.5.tgz", "@expo/config-plugins@7.2.5": "https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-7.2.5.tgz", "@expo/config-plugins@~7.2.0": "https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-7.2.3.tgz", "@expo/config-plugins@~7.8.0": "https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-7.8.0.tgz", "@expo/config-types@^49.0.0-alpha.1": "https://registry.npmjs.org/@expo/config-types/-/config-types-49.0.0.tgz", "@expo/config-types@^50.0.0-alpha.1": "https://registry.npmjs.org/@expo/config-types/-/config-types-50.0.0.tgz", "@expo/config@8.1.2": "https://registry.npmjs.org/@expo/config/-/config-8.1.2.tgz", "@expo/config@~8.1.0": "https://registry.npmjs.org/@expo/config/-/config-8.1.2.tgz", "@expo/config@~8.5.0": "https://registry.npmjs.org/@expo/config/-/config-8.5.0.tgz", "@expo/dev-server@0.5.4": "https://registry.npmjs.org/@expo/dev-server/-/dev-server-0.5.4.tgz", "@expo/devcert@^1.0.0": "https://registry.npmjs.org/@expo/devcert/-/devcert-1.1.0.tgz", "@expo/env@0.0.4": "https://registry.npmjs.org/@expo/env/-/env-0.0.4.tgz", "@expo/env@0.0.5": "https://registry.npmjs.org/@expo/env/-/env-0.0.5.tgz", "@expo/fingerprint@^0.6.0": "https://registry.npmjs.org/@expo/fingerprint/-/fingerprint-0.6.0.tgz", "@expo/image-utils@0.3.22": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.22.tgz", "@expo/json-file@^8.2.37": "https://registry.npmjs.org/@expo/json-file/-/json-file-8.2.37.tgz", "@expo/json-file@~8.2.37": "https://registry.npmjs.org/@expo/json-file/-/json-file-8.2.37.tgz", "@expo/json-file@~8.3.0": "https://registry.npmjs.org/@expo/json-file/-/json-file-8.3.0.tgz", "@expo/metro-config@~0.10.0": "https://registry.npmjs.org/@expo/metro-config/-/metro-config-0.10.5.tgz", "@expo/npm-proofread@^1.0.1": "https://registry.npmjs.org/@expo/npm-proofread/-/npm-proofread-1.0.1.tgz", "@expo/osascript@2.0.33": "https://registry.npmjs.org/@expo/osascript/-/osascript-2.0.33.tgz", "@expo/osascript@^2.0.31": "https://registry.npmjs.org/@expo/osascript/-/osascript-2.0.33.tgz", "@expo/package-manager@~1.0.0": "https://registry.npmjs.org/@expo/package-manager/-/package-manager-1.0.1.tgz", "@expo/plist@^0.0.20": "https://registry.npmjs.org/@expo/plist/-/plist-0.0.20.tgz", "@expo/plist@^0.1.0": "https://registry.npmjs.org/@expo/plist/-/plist-0.1.0.tgz", "@expo/prebuild-config@6.2.6": "https://registry.npmjs.org/@expo/prebuild-config/-/prebuild-config-6.2.6.tgz", "@expo/rudder-sdk-node@1.1.1": "https://registry.npmjs.org/@expo/rudder-sdk-node/-/rudder-sdk-node-1.1.1.tgz", "@expo/sdk-runtime-versions@^1.0.0": "https://registry.npmjs.org/@expo/sdk-runtime-versions/-/sdk-runtime-versions-1.0.0.tgz", "@expo/spawn-async@1.5.0": "https://registry.npmjs.org/@expo/spawn-async/-/spawn-async-1.5.0.tgz", "@expo/spawn-async@^1.5.0": "https://registry.npmjs.org/@expo/spawn-async/-/spawn-async-1.7.0.tgz", "@expo/vector-icons@^13.0.0": "https://registry.npmjs.org/@expo/vector-icons/-/vector-icons-13.0.0.tgz", "@expo/xcpretty@^4.2.1": "https://registry.npmjs.org/@expo/xcpretty/-/xcpretty-4.2.2.tgz", "@fluentui/react-context-selector@^0.53.4": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-0.53.4.tgz", "@formidable-webview/webshell@^2.3.0": "https://registry.npmjs.org/@formidable-webview/webshell/-/webshell-2.6.0.tgz", "@gar/promisify@^1.0.1": "https://registry.npmjs.org/@gar/promisify/-/promisify-1.1.3.tgz", "@gar/promisify@^1.1.3": "https://registry.npmjs.org/@gar/promisify/-/promisify-1.1.3.tgz", "@graphql-typed-document-node/core@^3.1.0": "https://registry.npmjs.org/@graphql-typed-document-node/core/-/core-3.1.2.tgz", "@hapi/address@2.x.x": "https://registry.npmjs.org/@hapi/address/-/address-2.1.4.tgz", "@hapi/bourne@1.x.x": "https://registry.npmjs.org/@hapi/bourne/-/bourne-1.3.2.tgz", "@hapi/hoek@8.x.x": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.5.1.tgz", "@hapi/hoek@^8.3.0": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.5.1.tgz", "@hapi/hoek@^9.0.0": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/joi@^15.1.0": "https://registry.npmjs.org/@hapi/joi/-/joi-15.1.1.tgz", "@hapi/topo@3.x.x": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.6.tgz", "@hapi/topo@^5.0.0": "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz", "@hookform/error-message@^2.0.1": "https://registry.npmjs.org/@hookform/error-message/-/error-message-2.0.1.tgz", "@hookform/resolvers@^3.3.1": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.1.tgz", "@humanwhocodes/config-array@^0.5.0": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.5.0.tgz", "@humanwhocodes/object-schema@^1.2.0": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "@hutson/parse-repository-url@^3.0.0": "https://registry.npmjs.org/@hutson/parse-repository-url/-/parse-repository-url-3.0.2.tgz", "@iarna/toml@2.2.5": "https://registry.npmjs.org/@iarna/toml/-/toml-2.2.5.tgz", "@invertase/react-native-apple-authentication@2.2.1": "https://registry.npmjs.org/@invertase/react-native-apple-authentication/-/react-native-apple-authentication-2.2.1.tgz", "@isaacs/string-locale-compare@^1.1.0": "https://registry.npmjs.org/@isaacs/string-locale-compare/-/string-locale-compare-1.1.0.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "@jest/console@^26.6.2": "https://registry.npmjs.org/@jest/console/-/console-26.6.2.tgz", "@jest/console@^29.7.0": "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz", "@jest/core@^26.6.0": "https://registry.npmjs.org/@jest/core/-/core-26.6.3.tgz", "@jest/core@^26.6.3": "https://registry.npmjs.org/@jest/core/-/core-26.6.3.tgz", "@jest/core@^29.7.0": "https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz", "@jest/create-cache-key-function@^29.2.1": "https://registry.npmjs.org/@jest/create-cache-key-function/-/create-cache-key-function-29.5.0.tgz", "@jest/environment@^26.6.0": "https://registry.npmjs.org/@jest/environment/-/environment-26.6.2.tgz", "@jest/environment@^26.6.2": "https://registry.npmjs.org/@jest/environment/-/environment-26.6.2.tgz", "@jest/environment@^29.5.0": "https://registry.npmjs.org/@jest/environment/-/environment-29.5.0.tgz", "@jest/environment@^29.7.0": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz", "@jest/expect-utils@^29.7.0": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "@jest/expect@^29.7.0": "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz", "@jest/fake-timers@^26.6.2": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.6.2.tgz", "@jest/fake-timers@^29.5.0": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.5.0.tgz", "@jest/fake-timers@^29.7.0": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "@jest/globals@^26.6.2": "https://registry.npmjs.org/@jest/globals/-/globals-26.6.2.tgz", "@jest/globals@^29.7.0": "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz", "@jest/reporters@^26.6.2": "https://registry.npmjs.org/@jest/reporters/-/reporters-26.6.2.tgz", "@jest/reporters@^29.7.0": "https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz", "@jest/schemas@^29.4.3": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.4.3.tgz", "@jest/schemas@^29.6.3": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "@jest/source-map@^26.6.2": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.6.2.tgz", "@jest/source-map@^29.6.3": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz", "@jest/test-result@^26.6.0": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.2.tgz", "@jest/test-result@^26.6.2": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.2.tgz", "@jest/test-result@^29.7.0": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz", "@jest/test-sequencer@^26.6.3": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.3.tgz", "@jest/test-sequencer@^29.7.0": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "@jest/transform@^26.6.2": "https://registry.npmjs.org/@jest/transform/-/transform-26.6.2.tgz", "@jest/transform@^29.7.0": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz", "@jest/types@^26.6.0": "https://registry.npmjs.org/@jest/types/-/types-26.6.2.tgz", "@jest/types@^26.6.2": "https://registry.npmjs.org/@jest/types/-/types-26.6.2.tgz", "@jest/types@^27.5.1": "https://registry.npmjs.org/@jest/types/-/types-27.5.1.tgz", "@jest/types@^29.5.0": "https://registry.npmjs.org/@jest/types/-/types-29.5.0.tgz", "@jest/types@^29.6.3": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz", "@jridgewell/gen-mapping@^0.1.0": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "@jridgewell/resolve-uri@3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "@jridgewell/resolve-uri@^3.0.3": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz", "@jridgewell/set-array@^1.0.0": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/set-array@^1.0.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.4.tgz", "@jridgewell/sourcemap-codec@1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "@jridgewell/trace-mapping@0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "@jridgewell/trace-mapping@^0.3.12": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.22.tgz", "@jridgewell/trace-mapping@^0.3.17": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "@jridgewell/trace-mapping@^0.3.18": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.19.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "@lerna/child-process@6.6.1": "https://registry.npmjs.org/@lerna/child-process/-/child-process-6.6.1.tgz", "@lerna/create@6.6.1": "https://registry.npmjs.org/@lerna/create/-/create-6.6.1.tgz", "@lerna/legacy-package-management@6.6.1": "https://registry.npmjs.org/@lerna/legacy-package-management/-/legacy-package-management-6.6.1.tgz", "@miblanchard/react-native-slider@^2.2.0": "https://registry.npmjs.org/@miblanchard/react-native-slider/-/react-native-slider-2.3.1.tgz", "@miblanchard/react-native-slider@^2.3.1": "https://registry.npmjs.org/@miblanchard/react-native-slider/-/react-native-slider-2.3.1.tgz", "@native-html/iframe-plugin@^1.1.2": "https://registry.npmjs.org/@native-html/iframe-plugin/-/iframe-plugin-1.1.2.tgz", "@nicolo-ribaudo/chokidar-2@2.1.8-no-fsevents.3": "https://registry.npmjs.org/@nicolo-ribaudo/chokidar-2/-/chokidar-2-2.1.8-no-fsevents.3.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@notifee/react-native@^7.7.1": "https://registry.npmjs.org/@notifee/react-native/-/react-native-7.7.1.tgz", "@npmcli/arborist@6.2.3": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.3.tgz", "@npmcli/fs@^1.0.0": "https://registry.npmjs.org/@npmcli/fs/-/fs-1.1.1.tgz", "@npmcli/fs@^2.1.0": "https://registry.npmjs.org/@npmcli/fs/-/fs-2.1.2.tgz", "@npmcli/fs@^3.1.0": "https://registry.npmjs.org/@npmcli/fs/-/fs-3.1.0.tgz", "@npmcli/git@^3.0.0": "https://registry.npmjs.org/@npmcli/git/-/git-3.0.2.tgz", "@npmcli/git@^4.0.0": "https://registry.npmjs.org/@npmcli/git/-/git-4.0.4.tgz", "@npmcli/installed-package-contents@^1.0.7": "https://registry.npmjs.org/@npmcli/installed-package-contents/-/installed-package-contents-1.0.7.tgz", "@npmcli/installed-package-contents@^2.0.0": "https://registry.npmjs.org/@npmcli/installed-package-contents/-/installed-package-contents-2.0.2.tgz", "@npmcli/installed-package-contents@^2.0.1": "https://registry.npmjs.org/@npmcli/installed-package-contents/-/installed-package-contents-2.0.2.tgz", "@npmcli/map-workspaces@^3.0.2": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.3.tgz", "@npmcli/metavuln-calculator@^5.0.0": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-5.0.0.tgz", "@npmcli/move-file@^1.0.1": "https://registry.npmjs.org/@npmcli/move-file/-/move-file-1.1.2.tgz", "@npmcli/move-file@^2.0.0": "https://registry.npmjs.org/@npmcli/move-file/-/move-file-2.0.1.tgz", "@npmcli/name-from-folder@^2.0.0": "https://registry.npmjs.org/@npmcli/name-from-folder/-/name-from-folder-2.0.0.tgz", "@npmcli/node-gyp@^2.0.0": "https://registry.npmjs.org/@npmcli/node-gyp/-/node-gyp-2.0.0.tgz", "@npmcli/node-gyp@^3.0.0": "https://registry.npmjs.org/@npmcli/node-gyp/-/node-gyp-3.0.0.tgz", "@npmcli/package-json@^3.0.0": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-3.0.0.tgz", "@npmcli/promise-spawn@^3.0.0": "https://registry.npmjs.org/@npmcli/promise-spawn/-/promise-spawn-3.0.0.tgz", "@npmcli/promise-spawn@^6.0.0": "https://registry.npmjs.org/@npmcli/promise-spawn/-/promise-spawn-6.0.2.tgz", "@npmcli/promise-spawn@^6.0.1": "https://registry.npmjs.org/@npmcli/promise-spawn/-/promise-spawn-6.0.2.tgz", "@npmcli/query@^3.0.0": "https://registry.npmjs.org/@npmcli/query/-/query-3.0.0.tgz", "@npmcli/run-script@4.1.7": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.7.tgz", "@npmcli/run-script@^4.1.0": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.2.1.tgz", "@npmcli/run-script@^6.0.0": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-6.0.0.tgz", "@nrwl/cli@15.9.2": "https://registry.npmjs.org/@nrwl/cli/-/cli-15.9.2.tgz", "@nrwl/devkit@>=15.5.2 < 16": "https://registry.npmjs.org/@nrwl/devkit/-/devkit-15.9.2.tgz", "@nrwl/nx-darwin-arm64@15.9.2": "https://registry.npmjs.org/@nrwl/nx-darwin-arm64/-/nx-darwin-arm64-15.9.2.tgz", "@nrwl/nx-darwin-x64@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-darwin-x64/-/nx-darwin-x64-15.9.2.tgz#3f77bd90dbabf4782d81f773cfb2739a443e595f", "@nrwl/nx-linux-arm-gnueabihf@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-linux-arm-gnueabihf/-/nx-linux-arm-gnueabihf-15.9.2.tgz#3374a5a1692b222ce18f2213a47b4d68fb509e70", "@nrwl/nx-linux-arm64-gnu@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-linux-arm64-gnu/-/nx-linux-arm64-gnu-15.9.2.tgz#e3ec95c6ee3285c77422886cf4cbec1f04804460", "@nrwl/nx-linux-arm64-musl@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-linux-arm64-musl/-/nx-linux-arm64-musl-15.9.2.tgz#72ce601d256083ded7380c598f1b3eb4dc2a3472", "@nrwl/nx-linux-x64-gnu@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-linux-x64-gnu/-/nx-linux-x64-gnu-15.9.2.tgz#2da6bb50cd80d699310e91c7331baa6cfc8ce197", "@nrwl/nx-linux-x64-musl@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-linux-x64-musl/-/nx-linux-x64-musl-15.9.2.tgz#39b3bda5868a53b722f1d42700dce71c5ff3f6b9", "@nrwl/nx-win32-arm64-msvc@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-win32-arm64-msvc/-/nx-win32-arm64-msvc-15.9.2.tgz#bc350be5cb7d0bfa6c2c5ced40c5af163a457a2c", "@nrwl/nx-win32-x64-msvc@15.9.2": "https://registry.yarnpkg.com/@nrwl/nx-win32-x64-msvc/-/nx-win32-x64-msvc-15.9.2.tgz#3e46c3f7af196bdbf0deb336ec4f9448c54e4a9f", "@nrwl/tao@15.9.2": "https://registry.npmjs.org/@nrwl/tao/-/tao-15.9.2.tgz", "@octokit/auth-token@^3.0.0": "https://registry.npmjs.org/@octokit/auth-token/-/auth-token-3.0.3.tgz", "@octokit/core@^4.0.0": "https://registry.npmjs.org/@octokit/core/-/core-4.2.0.tgz", "@octokit/core@^4.2.1": "https://registry.npmjs.org/@octokit/core/-/core-4.2.4.tgz", "@octokit/endpoint@^7.0.0": "https://registry.npmjs.org/@octokit/endpoint/-/endpoint-7.0.5.tgz", "@octokit/graphql@^5.0.0": "https://registry.npmjs.org/@octokit/graphql/-/graphql-5.0.5.tgz", "@octokit/openapi-types@^12.11.0": "https://registry.npmjs.org/@octokit/openapi-types/-/openapi-types-12.11.0.tgz", "@octokit/openapi-types@^14.0.0": "https://registry.npmjs.org/@octokit/openapi-types/-/openapi-types-14.0.0.tgz", "@octokit/openapi-types@^16.0.0": "https://registry.npmjs.org/@octokit/openapi-types/-/openapi-types-16.0.0.tgz", "@octokit/openapi-types@^18.0.0": "https://registry.npmjs.org/@octokit/openapi-types/-/openapi-types-18.0.0.tgz", "@octokit/plugin-enterprise-rest@6.0.1": "https://registry.npmjs.org/@octokit/plugin-enterprise-rest/-/plugin-enterprise-rest-6.0.1.tgz", "@octokit/plugin-paginate-rest@^3.0.0": "https://registry.npmjs.org/@octokit/plugin-paginate-rest/-/plugin-paginate-rest-3.1.0.tgz", "@octokit/plugin-paginate-rest@^6.1.2": "https://registry.npmjs.org/@octokit/plugin-paginate-rest/-/plugin-paginate-rest-6.1.2.tgz", "@octokit/plugin-request-log@^1.0.4": "https://registry.npmjs.org/@octokit/plugin-request-log/-/plugin-request-log-1.0.4.tgz", "@octokit/plugin-rest-endpoint-methods@^6.0.0": "https://registry.npmjs.org/@octokit/plugin-rest-endpoint-methods/-/plugin-rest-endpoint-methods-6.8.1.tgz", "@octokit/plugin-rest-endpoint-methods@^7.1.2": "https://registry.npmjs.org/@octokit/plugin-rest-endpoint-methods/-/plugin-rest-endpoint-methods-7.2.3.tgz", "@octokit/request-error@^3.0.0": "https://registry.npmjs.org/@octokit/request-error/-/request-error-3.0.3.tgz", "@octokit/request@^6.0.0": "https://registry.npmjs.org/@octokit/request/-/request-6.2.3.tgz", "@octokit/rest@19.0.11": "https://registry.npmjs.org/@octokit/rest/-/rest-19.0.11.tgz", "@octokit/rest@19.0.3": "https://registry.npmjs.org/@octokit/rest/-/rest-19.0.3.tgz", "@octokit/tsconfig@^1.0.2": "https://registry.npmjs.org/@octokit/tsconfig/-/tsconfig-1.0.2.tgz", "@octokit/types@^10.0.0": "https://registry.npmjs.org/@octokit/types/-/types-10.0.0.tgz", "@octokit/types@^6.41.0": "https://registry.npmjs.org/@octokit/types/-/types-6.41.0.tgz", "@octokit/types@^8.1.1": "https://registry.npmjs.org/@octokit/types/-/types-8.2.1.tgz", "@octokit/types@^9.0.0": "https://registry.npmjs.org/@octokit/types/-/types-9.0.0.tgz", "@octokit/types@^9.2.3": "https://registry.npmjs.org/@octokit/types/-/types-9.3.2.tgz", "@parcel/watcher@2.0.4": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.4.tgz", "@pkgr/utils@^2.4.2": "https://registry.npmjs.org/@pkgr/utils/-/utils-2.4.2.tgz", "@pmmmwh/react-refresh-webpack-plugin@0.4.3": "https://registry.npmjs.org/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.4.3.tgz", "@pnpm/config.env-replace@^1.1.0": "https://registry.npmjs.org/@pnpm/config.env-replace/-/config.env-replace-1.1.0.tgz", "@pnpm/network.ca-file@^1.0.1": "https://registry.npmjs.org/@pnpm/network.ca-file/-/network.ca-file-1.0.2.tgz", "@pnpm/npm-conf@^2.1.0": "https://registry.npmjs.org/@pnpm/npm-conf/-/npm-conf-2.2.2.tgz", "@ptomasroos/react-native-multi-slider@^2.2.2": "https://registry.npmjs.org/@ptomasroos/react-native-multi-slider/-/react-native-multi-slider-2.2.2.tgz", "@react-native-async-storage/async-storage@1.19.3": "https://registry.npmjs.org/@react-native-async-storage/async-storage/-/async-storage-1.19.3.tgz", "@react-native-clipboard/clipboard@^1.11.2": "https://registry.npmjs.org/@react-native-clipboard/clipboard/-/clipboard-1.11.2.tgz", "@react-native-community/async-storage@1.3.3": "https://registry.npmjs.org/@react-native-community/async-storage/-/async-storage-1.3.3.tgz", "@react-native-community/blur@^4.4.1": "https://registry.npmjs.org/@react-native-community/blur/-/blur-4.4.1.tgz", "@react-native-community/cli-clean@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-clean/-/cli-clean-11.3.3.tgz", "@react-native-community/cli-clean@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-clean/-/cli-clean-11.3.6.tgz", "@react-native-community/cli-config@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-config/-/cli-config-11.3.3.tgz", "@react-native-community/cli-config@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-config/-/cli-config-11.3.6.tgz", "@react-native-community/cli-debugger-ui@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-debugger-ui/-/cli-debugger-ui-11.3.3.tgz", "@react-native-community/cli-debugger-ui@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-debugger-ui/-/cli-debugger-ui-11.3.6.tgz", "@react-native-community/cli-doctor@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-doctor/-/cli-doctor-11.3.3.tgz", "@react-native-community/cli-doctor@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-doctor/-/cli-doctor-11.3.6.tgz", "@react-native-community/cli-hermes@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-hermes/-/cli-hermes-11.3.3.tgz", "@react-native-community/cli-hermes@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-hermes/-/cli-hermes-11.3.6.tgz", "@react-native-community/cli-platform-android@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-platform-android/-/cli-platform-android-11.3.3.tgz", "@react-native-community/cli-platform-android@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-platform-android/-/cli-platform-android-11.3.6.tgz", "@react-native-community/cli-platform-ios@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-platform-ios/-/cli-platform-ios-11.3.3.tgz", "@react-native-community/cli-platform-ios@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-platform-ios/-/cli-platform-ios-11.3.6.tgz", "@react-native-community/cli-plugin-metro@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-plugin-metro/-/cli-plugin-metro-11.3.3.tgz", "@react-native-community/cli-plugin-metro@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-plugin-metro/-/cli-plugin-metro-11.3.6.tgz", "@react-native-community/cli-server-api@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-server-api/-/cli-server-api-11.3.3.tgz", "@react-native-community/cli-server-api@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-server-api/-/cli-server-api-11.3.6.tgz", "@react-native-community/cli-tools@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-tools/-/cli-tools-11.3.3.tgz", "@react-native-community/cli-tools@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-tools/-/cli-tools-11.3.6.tgz", "@react-native-community/cli-types@11.3.3": "https://registry.npmjs.org/@react-native-community/cli-types/-/cli-types-11.3.3.tgz", "@react-native-community/cli-types@11.3.6": "https://registry.npmjs.org/@react-native-community/cli-types/-/cli-types-11.3.6.tgz", "@react-native-community/cli@11.3.3": "https://registry.npmjs.org/@react-native-community/cli/-/cli-11.3.3.tgz", "@react-native-community/cli@11.3.6": "https://registry.npmjs.org/@react-native-community/cli/-/cli-11.3.6.tgz", "@react-native-community/clipboard@^1.2.2": "https://registry.npmjs.org/@react-native-community/clipboard/-/clipboard-1.5.1.tgz", "@react-native-community/datetimepicker@7.2.0": "https://registry.npmjs.org/@react-native-community/datetimepicker/-/datetimepicker-7.2.0.tgz", "@react-native-community/geolocation@^3.4.0": "https://registry.yarnpkg.com/@react-native-community/geolocation/-/geolocation-3.4.0.tgz#8b6ee024a71cf94526ab796af1e9ae140684802c", "@react-native-community/picker@^1.8.1": "https://registry.npmjs.org/@react-native-community/picker/-/picker-1.8.1.tgz", "@react-native-community/push-notification-ios@^1.11.0": "https://registry.npmjs.org/@react-native-community/push-notification-ios/-/push-notification-ios-1.11.0.tgz", "@react-native-firebase/analytics@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/analytics/-/analytics-19.3.0.tgz", "@react-native-firebase/app@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/app/-/app-19.3.0.tgz", "@react-native-firebase/auth@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/auth/-/auth-19.3.0.tgz", "@react-native-firebase/crashlytics@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/crashlytics/-/crashlytics-19.3.0.tgz", "@react-native-firebase/dynamic-links@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/dynamic-links/-/dynamic-links-19.3.0.tgz", "@react-native-firebase/installations@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/installations/-/installations-19.3.0.tgz", "@react-native-firebase/messaging@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/messaging/-/messaging-19.3.0.tgz", "@react-native-firebase/perf@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/perf/-/perf-19.3.0.tgz", "@react-native-firebase/remote-config@^19.2.2": "https://registry.npmjs.org/@react-native-firebase/remote-config/-/remote-config-19.3.0.tgz", "@react-native-google-signin/google-signin@^10.0.1": "https://registry.npmjs.org/@react-native-google-signin/google-signin/-/google-signin-10.0.1.tgz", "@react-native-masked-view/masked-view@^0.2.9": "https://registry.npmjs.org/@react-native-masked-view/masked-view/-/masked-view-0.2.9.tgz", "@react-native/assets-registry@^0.72.0": "https://registry.npmjs.org/@react-native/assets-registry/-/assets-registry-0.72.0.tgz", "@react-native/codegen@^0.72.6": "https://registry.npmjs.org/@react-native/codegen/-/codegen-0.72.6.tgz", "@react-native/gradle-plugin@^0.72.11": "https://registry.npmjs.org/@react-native/gradle-plugin/-/gradle-plugin-0.72.11.tgz", "@react-native/js-polyfills@^0.72.1": "https://registry.npmjs.org/@react-native/js-polyfills/-/js-polyfills-0.72.1.tgz", "@react-native/normalize-color@*": "https://registry.npmjs.org/@react-native/normalize-color/-/normalize-color-2.1.0.tgz", "@react-native/normalize-color@^2.0.0": "https://registry.npmjs.org/@react-native/normalize-color/-/normalize-color-2.1.0.tgz", "@react-native/normalize-color@^2.1.0": "https://registry.npmjs.org/@react-native/normalize-color/-/normalize-color-2.1.0.tgz", "@react-native/normalize-colors@*": "https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.73.0.tgz", "@react-native/normalize-colors@^0.72.0": "https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.72.0.tgz", "@react-native/virtualized-lists@^0.72.6": "https://registry.npmjs.org/@react-native/virtualized-lists/-/virtualized-lists-0.72.6.tgz", "@react-native/virtualized-lists@^0.72.8": "https://registry.npmjs.org/@react-native/virtualized-lists/-/virtualized-lists-0.72.8.tgz", "@react-navigation/bottom-tabs@^6.5.8": "https://registry.npmjs.org/@react-navigation/bottom-tabs/-/bottom-tabs-6.5.8.tgz", "@react-navigation/core@^6.4.9": "https://registry.npmjs.org/@react-navigation/core/-/core-6.4.9.tgz", "@react-navigation/drawer@^6.6.3": "https://registry.npmjs.org/@react-navigation/drawer/-/drawer-6.6.3.tgz", "@react-navigation/elements@^1.3.18": "https://registry.npmjs.org/@react-navigation/elements/-/elements-1.3.18.tgz", "@react-navigation/material-top-tabs@^6.6.14": "https://registry.npmjs.org/@react-navigation/material-top-tabs/-/material-top-tabs-6.6.14.tgz", "@react-navigation/native@^6.1.7": "https://registry.npmjs.org/@react-navigation/native/-/native-6.1.7.tgz", "@react-navigation/routers@^6.1.9": "https://registry.npmjs.org/@react-navigation/routers/-/routers-6.1.9.tgz", "@react-navigation/stack@^6.3.17": "https://registry.npmjs.org/@react-navigation/stack/-/stack-6.3.17.tgz", "@rollup/plugin-node-resolve@^7.1.1": "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-7.1.3.tgz", "@rollup/plugin-replace@^2.3.1": "https://registry.npmjs.org/@rollup/plugin-replace/-/plugin-replace-2.4.2.tgz", "@rollup/plugin-typescript@^11.1.2": "https://registry.npmjs.org/@rollup/plugin-typescript/-/plugin-typescript-11.1.6.tgz", "@rollup/pluginutils@^3.0.8": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz", "@rollup/pluginutils@^3.1.0": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz", "@rollup/pluginutils@^5.1.0": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.0.tgz", "@segment/loosely-validate-event@^2.0.0": "https://registry.npmjs.org/@segment/loosely-validate-event/-/loosely-validate-event-2.0.0.tgz", "@shopify/checkout-sheet-kit@^3.2.0": "https://registry.npmjs.org/@shopify/checkout-sheet-kit/-/checkout-sheet-kit-3.2.0.tgz", "@shopify/flash-list@1.6.3": "https://registry.npmjs.org/@shopify/flash-list/-/flash-list-1.6.3.tgz", "@sideway/address@^4.1.3": "https://registry.npmjs.org/@sideway/address/-/address-4.1.4.tgz", "@sideway/formula@^3.0.1": "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz", "@sideway/pinpoint@^2.0.0": "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "@sigstore/protobuf-specs@^0.1.0": "https://registry.npmjs.org/@sigstore/protobuf-specs/-/protobuf-specs-0.1.0.tgz", "@sinclair/typebox@^0.25.16": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.25.24.tgz", "@sinclair/typebox@^0.27.8": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz", "@sindresorhus/is@^5.2.0": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.6.0.tgz", "@sinonjs/commons@^1.7.0": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz", "@sinonjs/commons@^3.0.0": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.0.tgz", "@sinonjs/fake-timers@^10.0.2": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "@sinonjs/fake-timers@^6.0.1": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-6.0.1.tgz", "@surma/rollup-plugin-off-main-thread@^1.1.1": "https://registry.npmjs.org/@surma/rollup-plugin-off-main-thread/-/rollup-plugin-off-main-thread-1.4.2.tgz", "@svgr/babel-plugin-add-jsx-attribute@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-5.4.0.tgz", "@svgr/babel-plugin-add-jsx-attribute@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-6.5.1.tgz", "@svgr/babel-plugin-remove-jsx-attribute@*": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-8.0.0.tgz", "@svgr/babel-plugin-remove-jsx-attribute@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-5.4.0.tgz", "@svgr/babel-plugin-remove-jsx-empty-expression@*": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-8.0.0.tgz", "@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz", "@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1": "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz", "@svgr/babel-plugin-replace-jsx-attribute-value@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-6.5.1.tgz", "@svgr/babel-plugin-svg-dynamic-title@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-5.4.0.tgz", "@svgr/babel-plugin-svg-dynamic-title@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-6.5.1.tgz", "@svgr/babel-plugin-svg-em-dimensions@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-5.4.0.tgz", "@svgr/babel-plugin-svg-em-dimensions@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-6.5.1.tgz", "@svgr/babel-plugin-transform-react-native-svg@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-5.4.0.tgz", "@svgr/babel-plugin-transform-react-native-svg@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-6.5.1.tgz", "@svgr/babel-plugin-transform-svg-component@^5.5.0": "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-5.5.0.tgz", "@svgr/babel-plugin-transform-svg-component@^6.5.1": "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-6.5.1.tgz", "@svgr/babel-preset@^5.5.0": "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-5.5.0.tgz", "@svgr/babel-preset@^6.5.1": "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-6.5.1.tgz", "@svgr/core@^5.5.0": "https://registry.npmjs.org/@svgr/core/-/core-5.5.0.tgz", "@svgr/core@^6.1.2": "https://registry.npmjs.org/@svgr/core/-/core-6.5.1.tgz", "@svgr/hast-util-to-babel-ast@^5.5.0": "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-5.5.0.tgz", "@svgr/hast-util-to-babel-ast@^6.5.1": "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-6.5.1.tgz", "@svgr/plugin-jsx@^5.5.0": "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-5.5.0.tgz", "@svgr/plugin-jsx@^6.5.1": "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-6.5.1.tgz", "@svgr/plugin-svgo@^5.5.0": "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-5.5.0.tgz", "@svgr/plugin-svgo@^6.1.2": "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-6.5.1.tgz", "@svgr/webpack@5.5.0": "https://registry.npmjs.org/@svgr/webpack/-/webpack-5.5.0.tgz", "@szmarczak/http-timer@^5.0.1": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-5.0.1.tgz", "@tanstack/query-core@4.29.11": "https://registry.npmjs.org/@tanstack/query-core/-/query-core-4.29.11.tgz", "@tanstack/react-query@^4.28.0": "https://registry.npmjs.org/@tanstack/react-query/-/react-query-4.29.12.tgz", "@testing-library/react-hooks@^7.0.1": "https://registry.npmjs.org/@testing-library/react-hooks/-/react-hooks-7.0.2.tgz", "@tootallnate/once@1": "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz", "@tootallnate/once@2": "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz", "@trysound/sax@0.2.0": "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz", "@tsconfig/node10@^1.0.7": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.9.tgz", "@tsconfig/node12@^1.0.7": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "@tsconfig/node14@^1.0.0": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "@tsconfig/node14@^1.0.3": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "@tsconfig/node16@^1.0.2": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "@tufjs/models@1.0.1": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.1.tgz", "@twotalltotems/react-native-otp-input@^1.3.11": "https://registry.npmjs.org/@twotalltotems/react-native-otp-input/-/react-native-otp-input-1.3.11.tgz", "@types/babel__core@^7.0.0": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.1.tgz", "@types/babel__core@^7.1.14": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.1.tgz", "@types/babel__core@^7.1.7": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.1.tgz", "@types/babel__generator@*": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz", "@types/babel__template@*": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz", "@types/babel__traverse@*": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "@types/babel__traverse@^7.0.4": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "@types/base16@^1.0.2": "https://registry.npmjs.org/@types/base16/-/base16-1.0.5.tgz", "@types/eslint@^7.29.0": "https://registry.npmjs.org/@types/eslint/-/eslint-7.29.0.tgz", "@types/estree@*": "https://registry.npmjs.org/@types/estree/-/estree-1.0.1.tgz", "@types/estree@0.0.39": "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz", "@types/estree@^1.0.0": "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz", "@types/glob@^7.1.1": "https://registry.npmjs.org/@types/glob/-/glob-7.2.0.tgz", "@types/graceful-fs@^4.1.2": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.6.tgz", "@types/graceful-fs@^4.1.3": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.6.tgz", "@types/hammerjs@^2.0.36": "https://registry.npmjs.org/@types/hammerjs/-/hammerjs-2.0.41.tgz", "@types/hoist-non-react-statics@^3.3.1": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz", "@types/html-minifier-terser@^5.0.0": "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-5.1.2.tgz", "@types/http-cache-semantics@^4.0.1": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "@types/istanbul-lib-report@*": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "@types/jest@^29.2.1": "https://registry.npmjs.org/@types/jest/-/jest-29.5.11.tgz", "@types/jsdom@^20.0.0": "https://registry.npmjs.org/@types/jsdom/-/jsdom-20.0.1.tgz", "@types/json-schema@*": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "@types/json-schema@^7.0.12": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.3": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "@types/json-schema@^7.0.5": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "@types/json-schema@^7.0.7": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "@types/json-schema@^7.0.8": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "@types/json5@^0.0.29": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "@types/lodash@^4.14.175": "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.197.tgz", "@types/lodash@^4.14.178": "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.16.tgz", "@types/minimatch@*": "https://registry.npmjs.org/@types/minimatch/-/minimatch-5.1.2.tgz", "@types/minimatch@^3.0.3": "https://registry.npmjs.org/@types/minimatch/-/minimatch-3.0.5.tgz", "@types/minimist@^1.2.0": "https://registry.npmjs.org/@types/minimist/-/minimist-1.2.2.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-18.15.3.tgz", "@types/normalize-package-data@^2.4.0": "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz", "@types/parse-json@^4.0.0": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz", "@types/prettier@^2.0.0": "https://registry.npmjs.org/@types/prettier/-/prettier-2.7.3.tgz", "@types/prop-types@*": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "@types/prop-types@^15.7.3": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "@types/q@^1.5.1": "https://registry.npmjs.org/@types/q/-/q-1.5.6.tgz", "@types/react-dom@>=16.9.0": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.17.tgz", "@types/react-native@~0.69.1": "https://registry.npmjs.org/@types/react-native/-/react-native-0.69.21.tgz", "@types/react-test-renderer@>=16.9.0": "https://registry.npmjs.org/@types/react-test-renderer/-/react-test-renderer-18.0.7.tgz", "@types/react@*": "https://registry.npmjs.org/@types/react/-/react-18.2.11.tgz", "@types/react@>=16.9.0": "https://registry.npmjs.org/@types/react/-/react-18.2.43.tgz", "@types/react@~18.0.0": "https://registry.npmjs.org/@types/react/-/react-18.0.38.tgz", "@types/resolve@0.0.8": "https://registry.npmjs.org/@types/resolve/-/resolve-0.0.8.tgz", "@types/scheduler@*": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.3.tgz", "@types/scheduler@^0.16.1": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.3.tgz", "@types/semver@^7.5.0": "https://registry.npmjs.org/@types/semver/-/semver-7.5.6.tgz", "@types/source-list-map@*": "https://registry.npmjs.org/@types/source-list-map/-/source-list-map-0.1.2.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz", "@types/tapable@^1": "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.8.tgz", "@types/tapable@^1.0.5": "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.8.tgz", "@types/tough-cookie@*": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz", "@types/uglify-js@*": "https://registry.npmjs.org/@types/uglify-js/-/uglify-js-3.17.2.tgz", "@types/webpack-sources@*": "https://registry.npmjs.org/@types/webpack-sources/-/webpack-sources-3.2.0.tgz", "@types/webpack@^4.41.8": "https://registry.npmjs.org/@types/webpack/-/webpack-4.41.33.tgz", "@types/yargs-parser@*": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.0.tgz", "@types/yargs@^15.0.0": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.15.tgz", "@types/yargs@^16.0.0": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.5.tgz", "@types/yargs@^17.0.8": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.24.tgz", "@typescript-eslint/eslint-plugin@^4.5.0": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-4.33.0.tgz", "@typescript-eslint/eslint-plugin@^6.0.0": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.14.0.tgz", "@typescript-eslint/experimental-utils@4.33.0": "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-4.33.0.tgz", "@typescript-eslint/experimental-utils@^3.10.1": "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-3.10.1.tgz", "@typescript-eslint/experimental-utils@^4.0.1": "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-4.33.0.tgz", "@typescript-eslint/parser@^4.5.0": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-4.33.0.tgz", "@typescript-eslint/parser@^6.0.0": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.14.0.tgz", "@typescript-eslint/scope-manager@4.33.0": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-4.33.0.tgz", "@typescript-eslint/scope-manager@6.14.0": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.14.0.tgz", "@typescript-eslint/type-utils@6.14.0": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-6.14.0.tgz", "@typescript-eslint/types@3.10.1": "https://registry.npmjs.org/@typescript-eslint/types/-/types-3.10.1.tgz", "@typescript-eslint/types@4.33.0": "https://registry.npmjs.org/@typescript-eslint/types/-/types-4.33.0.tgz", "@typescript-eslint/types@6.14.0": "https://registry.npmjs.org/@typescript-eslint/types/-/types-6.14.0.tgz", "@typescript-eslint/typescript-estree@3.10.1": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-3.10.1.tgz", "@typescript-eslint/typescript-estree@4.33.0": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-4.33.0.tgz", "@typescript-eslint/typescript-estree@6.14.0": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.14.0.tgz", "@typescript-eslint/utils@6.14.0": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-6.14.0.tgz", "@typescript-eslint/visitor-keys@3.10.1": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-3.10.1.tgz", "@typescript-eslint/visitor-keys@4.33.0": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-4.33.0.tgz", "@typescript-eslint/visitor-keys@6.14.0": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.14.0.tgz", "@urql/core@2.3.6": "https://registry.npmjs.org/@urql/core/-/core-2.3.6.tgz", "@urql/core@>=2.3.1": "https://registry.npmjs.org/@urql/core/-/core-3.2.2.tgz", "@urql/exchange-retry@0.3.0": "https://registry.npmjs.org/@urql/exchange-retry/-/exchange-retry-0.3.0.tgz", "@webassemblyjs/ast@1.9.0": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.0.tgz", "@webassemblyjs/floating-point-hex-parser@1.9.0": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "@webassemblyjs/helper-api-error@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "@webassemblyjs/helper-buffer@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz", "@webassemblyjs/helper-code-frame@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz", "@webassemblyjs/helper-fsm@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "@webassemblyjs/helper-module-context@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz", "@webassemblyjs/helper-wasm-bytecode@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "@webassemblyjs/helper-wasm-section@1.9.0": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "@webassemblyjs/ieee754@1.9.0": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "@webassemblyjs/leb128@1.9.0": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.9.0.tgz", "@webassemblyjs/utf8@1.9.0": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "@webassemblyjs/wasm-edit@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "@webassemblyjs/wasm-gen@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "@webassemblyjs/wasm-opt@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "@webassemblyjs/wasm-parser@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "@webassemblyjs/wast-parser@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz", "@webassemblyjs/wast-printer@1.9.0": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "@wordpress/hooks@^3.1.1": "https://registry.npmjs.org/@wordpress/hooks/-/hooks-3.35.0.tgz", "@xmldom/xmldom@^0.8.8": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "@xmldom/xmldom@~0.7.7": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.7.11.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "@yarnpkg/lockfile@^1.1.0": "https://registry.npmjs.org/@yarnpkg/lockfile/-/lockfile-1.1.0.tgz", "@yarnpkg/parsers@^3.0.0-rc.18": "https://registry.npmjs.org/@yarnpkg/parsers/-/parsers-3.0.0-rc.42.tgz", "@zkochan/js-yaml@0.0.6": "https://registry.npmjs.org/@zkochan/js-yaml/-/js-yaml-0.0.6.tgz", "JSONStream@^1.0.4": "https://registry.npmjs.org/JSONStream/-/JSONStream-1.3.5.tgz", "abab@^2.0.3": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "abab@^2.0.5": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "abab@^2.0.6": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "abbrev@^1.0.0": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "abbrev@^2.0.0": "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz", "abort-controller@^3.0.0": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "abortcontroller-polyfill@^1.7.3": "https://registry.npmjs.org/abortcontroller-polyfill/-/abortcontroller-polyfill-1.7.5.tgz", "accepts@^1.3.7": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@^1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.4": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.5": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.7": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn-globals@^6.0.0": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz", "acorn-globals@^7.0.0": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.1.tgz", "acorn-jsx@^5.3.1": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^7.1.1": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz", "acorn-walk@^8.0.2": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.1.tgz", "acorn-walk@^8.1.1": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz", "acorn-walk@^8.2.0": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz", "acorn@^6.4.1": "https://registry.npmjs.org/acorn/-/acorn-6.4.2.tgz", "acorn@^7.1.0": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "acorn@^7.1.1": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "acorn@^7.4.0": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "acorn@^8.1.0": "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz", "acorn@^8.2.4": "https://registry.npmjs.org/acorn/-/acorn-8.10.0.tgz", "acorn@^8.4.1": "https://registry.npmjs.org/acorn/-/acorn-8.10.0.tgz", "acorn@^8.7.0": "https://registry.npmjs.org/acorn/-/acorn-8.10.0.tgz", "acorn@^8.8.1": "https://registry.npmjs.org/acorn/-/acorn-8.10.0.tgz", "acorn@^8.8.2": "https://registry.npmjs.org/acorn/-/acorn-8.9.0.tgz", "add-stream@^1.0.0": "https://registry.npmjs.org/add-stream/-/add-stream-1.0.0.tgz", "address@1.1.2": "https://registry.npmjs.org/address/-/address-1.1.2.tgz", "address@^1.0.1": "https://registry.npmjs.org/address/-/address-1.2.2.tgz", "adjust-sourcemap-loader@3.0.0": "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-3.0.0.tgz", "agent-base@6": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "agent-base@^6.0.2": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "agent-base@^7.0.2": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.0.tgz", "agent-base@^7.1.0": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.0.tgz", "agentkeepalive@^4.2.1": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.3.0.tgz", "aggregate-error@^3.0.0": "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz", "ajv-errors@^1.0.0": "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.1.tgz", "ajv-keywords@^3.1.0": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^3.4.1": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^3.5.2": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv@^6.1.0": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.10.0": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.10.2": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.3": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.1": "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz", "ajv@^8.11.0": "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz", "alphanum-sort@^1.0.0": "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz", "anser@^1.4.9": "https://registry.npmjs.org/anser/-/anser-1.4.10.tgz", "ansi-align@^3.0.1": "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.1.tgz", "ansi-colors@^3.0.0": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-3.2.4.tgz", "ansi-colors@^4.1.1": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz", "ansi-escapes@^3.1.0": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz", "ansi-escapes@^4.2.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.0": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.2": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^6.0.0": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.2.0.tgz", "ansi-fragments@^0.2.1": "https://registry.npmjs.org/ansi-fragments/-/ansi-fragments-0.2.1.tgz", "ansi-html@0.0.7": "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz", "ansi-html@^0.0.7": "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz", "ansi-regex@^2.0.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "ansi-regex@^4.1.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz", "ansi-regex@^5.0.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz", "ansi-styles@^3.2.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "anymatch@^2.0.0": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "anymatch@^3.0.0": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "anymatch@^3.0.3": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "appdirsjs@^1.2.4": "https://registry.npmjs.org/appdirsjs/-/appdirsjs-1.2.7.tgz", "application-config-path@^0.1.0": "https://registry.npmjs.org/application-config-path/-/application-config-path-0.1.1.tgz", "aproba@^1.0.3 || ^2.0.0": "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz", "aproba@^1.1.1": "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz", "aproba@^2.0.0": "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz", "are-we-there-yet@^3.0.0": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz", "are-we-there-yet@^4.0.0": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.0.tgz", "arg@4.1.0": "https://registry.npmjs.org/arg/-/arg-4.1.0.tgz", "arg@^4.1.0": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "argparse@^1.0.7": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "aria-query@^5.1.3": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "arity-n@^1.0.4": "https://registry.npmjs.org/arity-n/-/arity-n-1.0.4.tgz", "arr-diff@^4.0.0": "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz", "arr-flatten@^1.1.0": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "arr-union@^3.1.0": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "array-buffer-byte-length@^1.0.0": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz", "array-differ@^3.0.0": "https://registry.npmjs.org/array-differ/-/array-differ-3.0.0.tgz", "array-flatten@1.1.1": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "array-flatten@^2.1.0": "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz", "array-ify@^1.0.0": "https://registry.npmjs.org/array-ify/-/array-ify-1.0.0.tgz", "array-includes@^3.1.6": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.7.tgz", "array-includes@^3.1.7": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.7.tgz", "array-union@^1.0.1": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "array-uniq@^1.0.1": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "array-unique@^0.3.2": "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz", "array.prototype.findlastindex@^1.2.2": "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.3.tgz", "array.prototype.findlastindex@^1.2.3": "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.3.tgz", "array.prototype.flat@^1.3.1": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz", "array.prototype.flat@^1.3.2": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz", "array.prototype.flatmap@^1.3.1": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.1.tgz", "array.prototype.flatmap@^1.3.2": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz", "array.prototype.map@^1.0.5": "https://registry.npmjs.org/array.prototype.map/-/array.prototype.map-1.0.6.tgz", "array.prototype.reduce@^1.0.6": "https://registry.npmjs.org/array.prototype.reduce/-/array.prototype.reduce-1.0.6.tgz", "array.prototype.tosorted@^1.1.1": "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.1.tgz", "arraybuffer.prototype.slice@^1.0.1": "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.2.tgz", "arrify@^1.0.0": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "arrify@^1.0.1": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "arrify@^2.0.1": "https://registry.npmjs.org/arrify/-/arrify-2.0.1.tgz", "asap@~2.0.3": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "asap@~2.0.6": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "asn1.js@^5.2.0": "https://registry.npmjs.org/asn1.js/-/asn1.js-5.4.1.tgz", "asn1@~0.2.3": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "assert-plus@1.0.0": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "assert-plus@^1.0.0": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "assert@^1.1.1": "https://registry.npmjs.org/assert/-/assert-1.5.0.tgz", "assign-symbols@^1.0.0": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "ast-types-flow@^0.0.7": "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz", "ast-types@0.15.2": "https://registry.npmjs.org/ast-types/-/ast-types-0.15.2.tgz", "ast-types@^0.13.4": "https://registry.npmjs.org/ast-types/-/ast-types-0.13.4.tgz", "astral-regex@^1.0.0": "https://registry.npmjs.org/astral-regex/-/astral-regex-1.0.0.tgz", "astral-regex@^2.0.0": "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz", "async-each@^1.0.1": "https://registry.npmjs.org/async-each/-/async-each-1.0.6.tgz", "async-limiter@~1.0.0": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz", "async-retry@1.3.3": "https://registry.npmjs.org/async-retry/-/async-retry-1.3.3.tgz", "async@^2.6.4": "https://registry.npmjs.org/async/-/async-2.6.4.tgz", "async@^3.2.2": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "async@^3.2.3": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "async@~2.3.0": "https://registry.npmjs.org/async/-/async-2.3.0.tgz", "asynciterator.prototype@^1.0.0": "https://registry.npmjs.org/asynciterator.prototype/-/asynciterator.prototype-1.0.0.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "atob@^2.1.2": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "autoprefixer@^9.6.1": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.8.tgz", "available-typed-arrays@^1.0.5": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "awesome-json2json@^0.6.0": "https://registry.npmjs.org/awesome-json2json/-/awesome-json2json-0.6.0.tgz", "aws-sign2@~0.7.0": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "aws4@^1.8.0": "https://registry.npmjs.org/aws4/-/aws4-1.12.0.tgz", "axe-core@^4.6.2": "https://registry.npmjs.org/axe-core/-/axe-core-4.7.2.tgz", "axios@^1.0.0": "https://registry.npmjs.org/axios/-/axios-1.3.5.tgz", "axios@^1.3.4": "https://registry.npmjs.org/axios/-/axios-1.6.7.tgz", "axios@^1.3.5": "https://registry.npmjs.org/axios/-/axios-1.4.0.tgz", "axobject-query@^3.1.1": "https://registry.npmjs.org/axobject-query/-/axobject-query-3.2.1.tgz", "babel-core@^7.0.0-bridge.0": "https://registry.npmjs.org/babel-core/-/babel-core-7.0.0-bridge.0.tgz", "babel-eslint@^10.1.0": "https://registry.npmjs.org/babel-eslint/-/babel-eslint-10.1.0.tgz", "babel-extract-comments@^1.0.0": "https://registry.npmjs.org/babel-extract-comments/-/babel-extract-comments-1.0.0.tgz", "babel-jest@^26.6.0": "https://registry.npmjs.org/babel-jest/-/babel-jest-26.6.3.tgz", "babel-jest@^26.6.3": "https://registry.npmjs.org/babel-jest/-/babel-jest-26.6.3.tgz", "babel-jest@^29.2.1": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz", "babel-jest@^29.7.0": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz", "babel-loader@8.1.0": "https://registry.npmjs.org/babel-loader/-/babel-loader-8.1.0.tgz", "babel-plugin-istanbul@^6.0.0": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-istanbul@^6.1.1": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^26.6.2": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.6.2.tgz", "babel-plugin-jest-hoist@^29.6.3": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "babel-plugin-macros@^3.1.0": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz", "babel-plugin-module-resolver@^5.0.0": "https://registry.npmjs.org/babel-plugin-module-resolver/-/babel-plugin-module-resolver-5.0.0.tgz", "babel-plugin-named-asset-import@^0.3.7": "https://registry.npmjs.org/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.8.tgz", "babel-plugin-polyfill-corejs2@^0.3.3": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz", "babel-plugin-polyfill-corejs2@^0.4.3": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.3.tgz", "babel-plugin-polyfill-corejs2@^0.4.5": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.5.tgz", "babel-plugin-polyfill-corejs3@^0.6.0": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz", "babel-plugin-polyfill-corejs3@^0.8.1": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.1.tgz", "babel-plugin-polyfill-corejs3@^0.8.3": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.3.tgz", "babel-plugin-polyfill-regenerator@^0.4.1": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz", "babel-plugin-polyfill-regenerator@^0.5.0": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.0.tgz", "babel-plugin-polyfill-regenerator@^0.5.2": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.2.tgz", "babel-plugin-react-native-web@~0.18.10": "https://registry.npmjs.org/babel-plugin-react-native-web/-/babel-plugin-react-native-web-0.18.12.tgz", "babel-plugin-syntax-object-rest-spread@^6.8.0": "https://registry.npmjs.org/babel-plugin-syntax-object-rest-spread/-/babel-plugin-syntax-object-rest-spread-6.13.0.tgz", "babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0": "https://registry.npmjs.org/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz", "babel-plugin-transform-flow-enums@^0.0.2": "https://registry.npmjs.org/babel-plugin-transform-flow-enums/-/babel-plugin-transform-flow-enums-0.0.2.tgz", "babel-plugin-transform-object-rest-spread@^6.26.0": "https://registry.npmjs.org/babel-plugin-transform-object-rest-spread/-/babel-plugin-transform-object-rest-spread-6.26.0.tgz", "babel-plugin-transform-react-remove-prop-types@^0.4.24": "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz", "babel-preset-expo@~9.5.0": "https://registry.npmjs.org/babel-preset-expo/-/babel-preset-expo-9.5.0.tgz", "babel-preset-expo@~9.6.0": "https://registry.npmjs.org/babel-preset-expo/-/babel-preset-expo-9.6.2.tgz", "babel-preset-fbjs@^3.4.0": "https://registry.npmjs.org/babel-preset-fbjs/-/babel-preset-fbjs-3.4.0.tgz", "babel-preset-jest@^26.6.2": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.6.2.tgz", "babel-preset-jest@^29.6.3": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "babel-preset-react-app@^10.0.0": "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz", "babel-runtime@^6.11.6": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "babel-runtime@^6.26.0": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "babylon@^6.18.0": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base-64@^0.1.0": "https://registry.npmjs.org/base-64/-/base-64-0.1.0.tgz", "base16@^1.0.0": "https://registry.npmjs.org/base16/-/base16-1.0.0.tgz", "base64-js@^1.0.2": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.1.2": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.2.3": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.5.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base@^0.11.1": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "basic-ftp@^5.0.2": "https://registry.npmjs.org/basic-ftp/-/basic-ftp-5.0.3.tgz", "batch@0.6.1": "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz", "bcrypt-pbkdf@^1.0.0": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "before-after-hook@^2.2.0": "https://registry.npmjs.org/before-after-hook/-/before-after-hook-2.2.3.tgz", "better-opn@~3.0.2": "https://registry.npmjs.org/better-opn/-/better-opn-3.0.2.tgz", "bfj@^7.0.2": "https://registry.npmjs.org/bfj/-/bfj-7.0.2.tgz", "big-integer@1.6.x": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.51.tgz", "big-integer@^1.6.44": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.51.tgz", "big.js@^5.2.2": "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz", "bin-links@^4.0.1": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.1.tgz", "binary-extensions@^1.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz", "bindings@^1.5.0": "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz", "bl@^4.0.3": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "bl@^4.1.0": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "bl@^5.0.0": "https://registry.npmjs.org/bl/-/bl-5.1.0.tgz", "bluebird@^3.5.5": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "blueimp-md5@^2.10.0": "https://registry.npmjs.org/blueimp-md5/-/blueimp-md5-2.19.0.tgz", "bn.js@^4.0.0": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^4.1.0": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^4.11.9": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^5.0.0": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "bn.js@^5.1.1": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "body-parser@1.20.1": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz", "body-parser@^1.20.1": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "bonjour@^3.5.0": "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz", "boolbase@^1.0.0": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "boolbase@~1.0.0": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "boxen@^7.0.0": "https://registry.npmjs.org/boxen/-/boxen-7.1.1.tgz", "bplist-creator@0.1.0": "https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.1.0.tgz", "bplist-parser@0.3.1": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.1.tgz", "bplist-parser@^0.2.0": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.2.0.tgz", "bplist-parser@^0.3.1": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.2.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "brace-expansion@^2.0.1": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "braces@^2.3.1": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "braces@^2.3.2": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "braces@^3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "brorand@^1.0.1": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "brorand@^1.1.0": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "browser-process-hrtime@^1.0.0": "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz", "browserify-aes@^1.0.0": "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "browserify-aes@^1.0.4": "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "browserify-cipher@^1.0.0": "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "browserify-des@^1.0.0": "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz", "browserify-rsa@^4.0.0": "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "browserify-rsa@^4.0.1": "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "browserify-sign@^4.0.0": "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.2.1.tgz", "browserify-zlib@^0.2.0": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "browserslist@4.14.2": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.2.tgz", "browserslist@^4.0.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "browserslist@^4.12.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "browserslist@^4.21.10": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "browserslist@^4.21.3": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz", "browserslist@^4.21.5": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz", "browserslist@^4.21.9": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "browserslist@^4.6.2": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "browserslist@^4.6.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "bs-logger@0.x": "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz", "bser@2.1.1": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "buffer-alloc-unsafe@^1.1.0": "https://registry.npmjs.org/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz", "buffer-alloc@^1.1.0": "https://registry.npmjs.org/buffer-alloc/-/buffer-alloc-1.2.0.tgz", "buffer-equal-constant-time@1.0.1": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "buffer-fill@^1.0.0": "https://registry.npmjs.org/buffer-fill/-/buffer-fill-1.0.0.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer-indexof@^1.0.0": "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz", "buffer-xor@^1.0.3": "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz", "buffer@^4.3.0": "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz", "buffer@^5.5.0": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "buffer@^6.0.3": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "builtin-modules@^3.1.0": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.3.0.tgz", "builtin-status-codes@^3.0.0": "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "builtins@^1.0.3": "https://registry.npmjs.org/builtins/-/builtins-1.0.3.tgz", "builtins@^5.0.0": "https://registry.npmjs.org/builtins/-/builtins-5.0.1.tgz", "bundle-name@^3.0.0": "https://registry.npmjs.org/bundle-name/-/bundle-name-3.0.0.tgz", "byte-size@7.0.0": "https://registry.npmjs.org/byte-size/-/byte-size-7.0.0.tgz", "bytes@3.0.0": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "cacache@^12.0.2": "https://registry.npmjs.org/cacache/-/cacache-12.0.4.tgz", "cacache@^15.0.5": "https://registry.npmjs.org/cacache/-/cacache-15.3.0.tgz", "cacache@^15.3.0": "https://registry.npmjs.org/cacache/-/cacache-15.3.0.tgz", "cacache@^16.0.0": "https://registry.npmjs.org/cacache/-/cacache-16.1.3.tgz", "cacache@^16.1.0": "https://registry.npmjs.org/cacache/-/cacache-16.1.3.tgz", "cacache@^17.0.0": "https://registry.npmjs.org/cacache/-/cacache-17.0.5.tgz", "cacache@^17.0.4": "https://registry.npmjs.org/cacache/-/cacache-17.0.5.tgz", "cache-base@^1.0.1": "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz", "cacheable-lookup@^7.0.0": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-7.0.0.tgz", "cacheable-request@^10.2.8": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.13.tgz", "call-bind@^1.0.0": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "call-bind@^1.0.2": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "caller-callsite@^2.0.0": "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz", "caller-path@^2.0.0": "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz", "callsites@^2.0.0": "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz", "callsites@^3.0.0": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "camel-case@^4.1.1": "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz", "camelcase-keys@^6.2.2": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-6.2.2.tgz", "camelcase@5.3.1": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^5.0.0": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^5.3.1": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.0.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "camelcase@^6.1.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "camelcase@^6.2.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "camelcase@^7.0.1": "https://registry.npmjs.org/camelcase/-/camelcase-7.0.1.tgz", "caniuse-api@^3.0.0": "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz", "caniuse-lite@^1.0.0": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001527.tgz", "caniuse-lite@^1.0.30000981": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001527.tgz", "caniuse-lite@^1.0.30001109": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001527.tgz", "caniuse-lite@^1.0.30001125": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001527.tgz", "caniuse-lite@^1.0.30001449": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001467.tgz", "caniuse-lite@^1.0.30001517": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001527.tgz", "capture-exit@^2.0.0": "https://registry.npmjs.org/capture-exit/-/capture-exit-2.0.0.tgz", "case-sensitive-paths-webpack-plugin@2.3.0": "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.3.0.tgz", "caseless@~0.12.0": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "chalk@2.4.2": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.0.tgz", "chalk@5.2.0": "https://registry.npmjs.org/chalk/-/chalk-5.2.0.tgz", "chalk@^2.0.0": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^2.0.1": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^2.4.1": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.0.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.1": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^5.0.0": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "chalk@^5.0.1": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "chalk@^5.2.0": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "char-regex@^1.0.2": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "char-regex@^2.0.0": "https://registry.npmjs.org/char-regex/-/char-regex-2.0.1.tgz", "character-entities-html4@^1.0.0": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-1.1.4.tgz", "character-entities-legacy@^1.0.0": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-1.1.4.tgz", "chardet@^0.7.0": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz", "charenc@0.0.2": "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz", "charenc@~0.0.1": "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz", "check-types@^11.1.1": "https://registry.npmjs.org/check-types/-/check-types-11.2.2.tgz", "chokidar@^2.1.8": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz", "chokidar@^3.4.0": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "chokidar@^3.4.1": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "chownr@^1.1.1": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz", "chownr@^2.0.0": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "ci-info@^2.0.0": "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz", "ci-info@^3.2.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.8.0.tgz", "ci-info@^3.3.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.8.0.tgz", "cipher-base@^1.0.0": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "cipher-base@^1.0.1": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "cipher-base@^1.0.3": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "cjs-module-lexer@^0.6.0": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.6.0.tgz", "cjs-module-lexer@^1.0.0": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz", "class-utils@^0.3.5": "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz", "clean-css@^4.2.3": "https://registry.npmjs.org/clean-css/-/clean-css-4.2.4.tgz", "clean-stack@^2.0.0": "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz", "cli-boxes@^3.0.0": "https://registry.npmjs.org/cli-boxes/-/cli-boxes-3.0.0.tgz", "cli-cursor@3.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-cursor@^2.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "cli-cursor@^3.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-cursor@^4.0.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-4.0.0.tgz", "cli-spinners@2.6.1": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.6.1.tgz", "cli-spinners@^2.0.0": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.7.0.tgz", "cli-spinners@^2.5.0": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.7.0.tgz", "cli-spinners@^2.6.1": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.1.tgz", "cli-width@^3.0.0": "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz", "cli-width@^4.0.0": "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz", "cliui@^5.0.0": "https://registry.npmjs.org/cliui/-/cliui-5.0.0.tgz", "cliui@^6.0.0": "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz", "cliui@^7.0.2": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "cliui@^8.0.1": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "clone-deep@4.0.1": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "clone-deep@^2.0.1": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.2.tgz", "clone-deep@^4.0.1": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "clone@^1.0.2": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "clone@^2.1.2": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "clsx@^1.1.1": "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz", "cmd-shim@5.0.0": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-5.0.0.tgz", "cmd-shim@^6.0.0": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-6.0.1.tgz", "co@^4.6.0": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "coa@^2.0.2": "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "collection-visit@^1.0.0": "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^1.9.1": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^1.9.3": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "color-name@^1.0.0": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "color-string@^1.5.2": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "color-string@^1.6.0": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "color-string@^1.9.0": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "color-support@^1.1.3": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "color@^2.0.0": "https://registry.npmjs.org/color/-/color-2.0.1.tgz", "color@^3.0.0": "https://registry.npmjs.org/color/-/color-3.2.1.tgz", "color@^3.2.1": "https://registry.npmjs.org/color/-/color-3.2.1.tgz", "color@^4.2.3": "https://registry.npmjs.org/color/-/color-4.2.3.tgz", "colorette@^1.0.7": "https://registry.npmjs.org/colorette/-/colorette-1.4.0.tgz", "columnify@1.6.0": "https://registry.npmjs.org/columnify/-/columnify-1.6.0.tgz", "combined-stream@^1.0.6": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "combined-stream@~1.0.6": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "command-exists@^1.2.4": "https://registry.npmjs.org/command-exists/-/command-exists-1.2.9.tgz", "command-exists@^1.2.8": "https://registry.npmjs.org/command-exists/-/command-exists-1.2.9.tgz", "commander@^2.19.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^4.0.0": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^4.0.1": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^4.1.1": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^7.2.0": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "commander@^9.4.1": "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz", "commander@~2.13.0": "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz", "common-ancestor-path@^1.0.1": "https://registry.npmjs.org/common-ancestor-path/-/common-ancestor-path-1.0.1.tgz", "common-tags@^1.8.0": "https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz", "commondir@^1.0.1": "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz", "compare-func@^2.0.0": "https://registry.npmjs.org/compare-func/-/compare-func-2.0.0.tgz", "compare-versions@^3.4.0": "https://registry.npmjs.org/compare-versions/-/compare-versions-3.6.0.tgz", "component-emitter@^1.2.1": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz", "component-type@^1.2.1": "https://registry.npmjs.org/component-type/-/component-type-1.2.1.tgz", "compose-function@3.0.3": "https://registry.npmjs.org/compose-function/-/compose-function-3.0.3.tgz", "compressible@~2.0.16": "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz", "compression@^1.7.1": "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz", "compression@^1.7.4": "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "concat-stream@^1.5.0": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "concat-stream@^2.0.0": "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz", "config-chain@1.1.12": "https://registry.npmjs.org/config-chain/-/config-chain-1.1.12.tgz", "config-chain@^1.1.11": "https://registry.npmjs.org/config-chain/-/config-chain-1.1.13.tgz", "configstore@^6.0.0": "https://registry.npmjs.org/configstore/-/configstore-6.0.0.tgz", "confusing-browser-globals@^1.0.10": "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz", "connect-history-api-fallback@^1.6.0": "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz", "connect@^3.6.5": "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz", "connect@^3.7.0": "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz", "console-browserify@^1.1.0": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.2.0.tgz", "console-control-strings@^1.1.0": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "constants-browserify@^1.0.0": "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz", "content-disposition@0.5.4": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@~1.0.4": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "content-type@~1.0.5": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "contrast@^1.0.1": "https://registry.npmjs.org/contrast/-/contrast-1.0.1.tgz", "conventional-changelog-angular@5.0.12": "https://registry.npmjs.org/conventional-changelog-angular/-/conventional-changelog-angular-5.0.12.tgz", "conventional-changelog-core@4.2.4": "https://registry.npmjs.org/conventional-changelog-core/-/conventional-changelog-core-4.2.4.tgz", "conventional-changelog-preset-loader@^2.3.4": "https://registry.npmjs.org/conventional-changelog-preset-loader/-/conventional-changelog-preset-loader-2.3.4.tgz", "conventional-changelog-writer@^5.0.0": "https://registry.npmjs.org/conventional-changelog-writer/-/conventional-changelog-writer-5.0.1.tgz", "conventional-commits-filter@^2.0.7": "https://registry.npmjs.org/conventional-commits-filter/-/conventional-commits-filter-2.0.7.tgz", "conventional-commits-parser@^3.2.0": "https://registry.npmjs.org/conventional-commits-parser/-/conventional-commits-parser-3.2.4.tgz", "conventional-recommended-bump@6.1.0": "https://registry.npmjs.org/conventional-recommended-bump/-/conventional-recommended-bump-6.1.0.tgz", "convert-source-map@1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.7.0.tgz", "convert-source-map@^0.3.3": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-0.3.5.tgz", "convert-source-map@^1.4.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^1.6.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie-signature@1.0.6": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.5.0": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "copy-concurrently@^1.0.0": "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "copy-descriptor@^0.1.0": "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "core-js-compat@^3.25.1": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.29.1.tgz", "core-js-compat@^3.30.1": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.31.0.tgz", "core-js-compat@^3.30.2": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.31.0.tgz", "core-js-compat@^3.31.0": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.32.1.tgz", "core-js@^1.0.0": "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz", "core-js@^2.4.0": "https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz", "core-js@^3.6.5": "https://registry.npmjs.org/core-js/-/core-js-3.32.1.tgz", "core-util-is@1.0.2": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "core-util-is@~1.0.0": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "cosmiconfig-typescript-loader@^1.0.0": "https://registry.npmjs.org/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-1.0.9.tgz", "cosmiconfig@7.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.0.tgz", "cosmiconfig@8.1.3": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.3.tgz", "cosmiconfig@^5.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "cosmiconfig@^5.0.5": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "cosmiconfig@^5.1.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "cosmiconfig@^7": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "cosmiconfig@^7.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "cosmiconfig@^7.0.1": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "create-ecdh@^4.0.0": "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.4.tgz", "create-hash@^1.1.0": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "create-hash@^1.1.2": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "create-hash@^1.2.0": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "create-hmac@^1.1.0": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "create-hmac@^1.1.4": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "create-hmac@^1.1.7": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "create-jest@^29.7.0": "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz", "create-react-class@15.6.0": "https://registry.npmjs.org/create-react-class/-/create-react-class-15.6.0.tgz", "create-react-class@^15.6.0": "https://registry.npmjs.org/create-react-class/-/create-react-class-15.7.0.tgz", "create-require@^1.1.0": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "cross-fetch@^3.1.5": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.5.tgz", "cross-spawn@7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^6.0.0": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz", "cross-spawn@^6.0.5": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz", "cross-spawn@^7.0.0": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.2": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "crypt@0.0.2": "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz", "crypt@~0.0.1": "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz", "crypto-browserify@^3.11.0": "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "crypto-random-string@^1.0.0": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-1.0.0.tgz", "crypto-random-string@^2.0.0": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz", "crypto-random-string@^4.0.0": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-4.0.0.tgz", "css-blank-pseudo@^0.1.4": "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-0.1.4.tgz", "css-color-names@0.0.4": "https://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz", "css-color-names@^0.0.4": "https://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz", "css-declaration-sorter@^4.0.1": "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz", "css-has-pseudo@^0.10.0": "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-0.10.0.tgz", "css-in-js-utils@^3.1.0": "https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz", "css-loader@4.3.0": "https://registry.npmjs.org/css-loader/-/css-loader-4.3.0.tgz", "css-prefers-color-scheme@^3.1.1": "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-3.1.1.tgz", "css-select-base-adapter@^0.1.1": "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz", "css-select@^2.0.0": "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz", "css-select@^4.1.3": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "css-select@^5.1.0": "https://registry.npmjs.org/css-select/-/css-select-5.1.0.tgz", "css-tree@1.0.0-alpha.37": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz", "css-tree@^1.1.2": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-tree@^1.1.3": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-what@^3.2.1": "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz", "css-what@^6.0.1": "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz", "css-what@^6.1.0": "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz", "css@^2.0.0": "https://registry.npmjs.org/css/-/css-2.2.4.tgz", "cssdb@^4.4.0": "https://registry.npmjs.org/cssdb/-/cssdb-4.4.0.tgz", "cssesc@^2.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-2.0.0.tgz", "cssesc@^3.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "cssnano-preset-default@^4.0.8": "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-4.0.8.tgz", "cssnano-util-get-arguments@^4.0.0": "https://registry.npmjs.org/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz", "cssnano-util-get-match@^4.0.0": "https://registry.npmjs.org/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz", "cssnano-util-raw-cache@^4.0.1": "https://registry.npmjs.org/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz", "cssnano-util-same-parent@^4.0.0": "https://registry.npmjs.org/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz", "cssnano@^4.1.10": "https://registry.npmjs.org/cssnano/-/cssnano-4.1.11.tgz", "csso@^4.0.2": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "csso@^4.2.0": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "cssom@^0.4.4": "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz", "cssom@^0.5.0": "https://registry.npmjs.org/cssom/-/cssom-0.5.0.tgz", "cssom@~0.3.6": "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz", "cssstyle@^2.3.0": "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz", "csstype@^3.0.10": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "csstype@^3.0.2": "https://registry.npmjs.org/csstype/-/csstype-3.1.2.tgz", "cyclist@^1.0.1": "https://registry.npmjs.org/cyclist/-/cyclist-1.0.2.tgz", "d@1": "https://registry.npmjs.org/d/-/d-1.0.1.tgz", "d@^1.0.1": "https://registry.npmjs.org/d/-/d-1.0.1.tgz", "dag-map@~1.0.0": "https://registry.npmjs.org/dag-map/-/dag-map-1.0.2.tgz", "damerau-levenshtein@^1.0.8": "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz", "dargs@^7.0.0": "https://registry.npmjs.org/dargs/-/dargs-7.0.0.tgz", "dashdash@^1.12.0": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "data-uri-to-buffer@^4.0.0": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "data-uri-to-buffer@^5.0.1": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-5.0.1.tgz", "data-urls@^2.0.0": "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz", "data-urls@^3.0.2": "https://registry.npmjs.org/data-urls/-/data-urls-3.0.2.tgz", "dateformat@^3.0.0": "https://registry.npmjs.org/dateformat/-/dateformat-3.0.3.tgz", "dayjs@^1.11.2": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.8.tgz", "dayjs@^1.8.15": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.8.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^2.2.0": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^2.3.3": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^2.6.0": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^3.1.0": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "debug@^3.2.7": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "debug@^4.0.1": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.1.1": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.3.3": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "debug@^4.3.4": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "decamelize-keys@^1.1.0": "https://registry.npmjs.org/decamelize-keys/-/decamelize-keys-1.1.1.tgz", "decamelize@^1.1.0": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "decamelize@^1.2.0": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "decimal.js@^10.2.1": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.3.tgz", "decimal.js@^10.4.2": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.3.tgz", "decode-uri-component@^0.2.0": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "decode-uri-component@^0.2.2": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "decompress-response@^6.0.0": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz", "dedent@0.7.0": "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz", "dedent@^0.6.0": "https://registry.npmjs.org/dedent/-/dedent-0.6.0.tgz", "dedent@^0.7.0": "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz", "dedent@^1.0.0": "https://registry.npmjs.org/dedent/-/dedent-1.5.1.tgz", "deep-diff@0.3.4": "https://registry.npmjs.org/deep-diff/-/deep-diff-0.3.4.tgz", "deep-equal@^1.0.1": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz", "deep-extend@^0.6.0": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "deep-is@^0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "deep-is@~0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "deepmerge@^3.2.0": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.3.0.tgz", "deepmerge@^4.2.2": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "deepmerge@^4.3.0": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "default-browser-id@^3.0.0": "https://registry.npmjs.org/default-browser-id/-/default-browser-id-3.0.0.tgz", "default-browser@^4.0.0": "https://registry.npmjs.org/default-browser/-/default-browser-4.0.0.tgz", "default-gateway@^4.2.0": "https://registry.npmjs.org/default-gateway/-/default-gateway-4.2.0.tgz", "defaults@^1.0.3": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "defer-to-connect@^2.0.1": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-2.0.1.tgz", "define-lazy-prop@^2.0.0": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "define-lazy-prop@^3.0.0": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz", "define-properties@^1.1.2": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz", "define-properties@^1.1.3": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz", "define-properties@^1.1.4": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz", "define-properties@^1.2.0": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz", "define-property@^0.2.5": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "define-property@^1.0.0": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "define-property@^2.0.2": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "degenerator@^4.0.4": "https://registry.npmjs.org/degenerator/-/degenerator-4.0.4.tgz", "del@^2.2.2": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "del@^4.1.1": "https://registry.npmjs.org/del/-/del-4.1.1.tgz", "del@^6.0.0": "https://registry.npmjs.org/del/-/del-6.1.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "delegates@^1.0.0": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "denodeify@^1.2.1": "https://registry.npmjs.org/denodeify/-/denodeify-1.2.1.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "depd@^2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "depd@~1.1.2": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "deprecated-prop-type@^1.0.0": "https://registry.npmjs.org/deprecated-prop-type/-/deprecated-prop-type-1.0.0.tgz", "deprecated-react-native-prop-types@4.1.0": "https://registry.npmjs.org/deprecated-react-native-prop-types/-/deprecated-react-native-prop-types-4.1.0.tgz", "deprecated-react-native-prop-types@^2.2.0": "https://registry.npmjs.org/deprecated-react-native-prop-types/-/deprecated-react-native-prop-types-2.3.0.tgz", "deprecated-react-native-prop-types@^2.3.0": "https://registry.npmjs.org/deprecated-react-native-prop-types/-/deprecated-react-native-prop-types-2.3.0.tgz", "deprecation@^2.0.0": "https://registry.npmjs.org/deprecation/-/deprecation-2.3.1.tgz", "deprecation@^2.3.1": "https://registry.npmjs.org/deprecation/-/deprecation-2.3.1.tgz", "dequal@^2.0.3": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "des.js@^1.0.0": "https://registry.npmjs.org/des.js/-/des.js-1.1.0.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-indent@^5.0.0": "https://registry.npmjs.org/detect-indent/-/detect-indent-5.0.0.tgz", "detect-libc@^1.0.3": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "detect-newline@^3.0.0": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "detect-node@^2.0.4": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "detect-port-alt@1.1.6": "https://registry.npmjs.org/detect-port-alt/-/detect-port-alt-1.1.6.tgz", "diff-sequences@^26.6.2": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.6.2.tgz", "diff-sequences@^29.6.3": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz", "diff@^4.0.1": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "diffie-hellman@^5.0.0": "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "dns-equal@^1.0.0": "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz", "dns-packet@^1.3.1": "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.4.tgz", "dns-txt@^2.0.2": "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz", "doctrine@^2.1.0": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz", "doctrine@^3.0.0": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "dom-converter@^0.2.0": "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz", "dom-serializer@0": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz", "dom-serializer@^1.0.1": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz", "dom-serializer@^2.0.0": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-2.0.0.tgz", "dom-walk@^0.1.0": "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz", "domain-browser@^1.1.1": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz", "domelementtype@1": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz", "domelementtype@^2.0.1": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domelementtype@^2.2.0": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domelementtype@^2.3.0": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domexception@^2.0.1": "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz", "domexception@^4.0.0": "https://registry.npmjs.org/domexception/-/domexception-4.0.0.tgz", "domhandler@^3.3.0": "https://registry.npmjs.org/domhandler/-/domhandler-3.3.0.tgz", "domhandler@^4.0.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.2.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.3.1": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^5.0.2": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz", "domhandler@^5.0.3": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz", "domutils@^1.7.0": "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz", "domutils@^2.4.2": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "domutils@^2.5.2": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "domutils@^2.8.0": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "domutils@^3.0.1": "https://registry.npmjs.org/domutils/-/domutils-3.1.0.tgz", "dot-case@^3.0.4": "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz", "dot-prop@6.0.1": "https://registry.npmjs.org/dot-prop/-/dot-prop-6.0.1.tgz", "dot-prop@^5.1.0": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz", "dot-prop@^5.2.0": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz", "dot-prop@^6.0.1": "https://registry.npmjs.org/dot-prop/-/dot-prop-6.0.1.tgz", "dotenv-expand@5.1.0": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "dotenv-expand@~10.0.0": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-10.0.0.tgz", "dotenv@8.2.0": "https://registry.npmjs.org/dotenv/-/dotenv-8.2.0.tgz", "dotenv@~10.0.0": "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz", "dotenv@~16.0.3": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.3.tgz", "duplexer@^0.1.1": "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz", "duplexify@^3.4.2": "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz", "duplexify@^3.6.0": "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz", "eastasianwidth@^0.2.0": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "ecc-jsbn@~0.1.1": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "ecdsa-sig-formatter@1.0.11": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "ejs-browser@^3.2.2": "https://registry.npmjs.org/ejs-browser/-/ejs-browser-3.2.2.tgz", "ejs@^2.6.1": "https://registry.npmjs.org/ejs/-/ejs-2.7.4.tgz", "ejs@^3.1.6": "https://registry.npmjs.org/ejs/-/ejs-3.1.9.tgz", "ejs@^3.1.7": "https://registry.npmjs.org/ejs/-/ejs-3.1.9.tgz", "electron-to-chromium@^1.3.564": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.508.tgz", "electron-to-chromium@^1.4.284": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.332.tgz", "electron-to-chromium@^1.4.477": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.508.tgz", "elliptic@^6.5.3": "https://registry.npmjs.org/elliptic/-/elliptic-6.5.4.tgz", "eme-encryption-scheme-polyfill@^2.0.1": "https://registry.npmjs.org/eme-encryption-scheme-polyfill/-/eme-encryption-scheme-polyfill-2.1.1.tgz", "emittery@^0.13.1": "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz", "emittery@^0.7.1": "https://registry.npmjs.org/emittery/-/emittery-0.7.2.tgz", "emoji-regex@^7.0.1": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "emojis-list@^3.0.0": "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encoding@^0.1.11": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "encoding@^0.1.13": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "end-of-stream@^1.0.0": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-of-stream@^1.1.0": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-of-stream@^1.4.1": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "enhanced-resolve@^4.3.0": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "enquirer@^2.3.5": "https://registry.npmjs.org/enquirer/-/enquirer-2.4.1.tgz", "enquirer@~2.3.6": "https://registry.npmjs.org/enquirer/-/enquirer-2.3.6.tgz", "entities@^2.0.0": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "entities@^4.2.0": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "entities@^4.4.0": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "env-editor@^0.4.1": "https://registry.npmjs.org/env-editor/-/env-editor-0.4.2.tgz", "env-paths@^2.2.0": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "envinfo@^7.7.2": "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz", "envinfo@^7.7.4": "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz", "eol@^0.9.1": "https://registry.npmjs.org/eol/-/eol-0.9.1.tgz", "err-code@^2.0.2": "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz", "errno@^0.1.3": "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz", "errno@~0.1.7": "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz", "error-ex@^1.3.1": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz", "errorhandler@^1.5.1": "https://registry.npmjs.org/errorhandler/-/errorhandler-1.5.1.tgz", "es-abstract@^1.17.2": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.22.1.tgz", "es-abstract@^1.20.4": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.22.1.tgz", "es-abstract@^1.22.1": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.22.1.tgz", "es-array-method-boxes-properly@^1.0.0": "https://registry.npmjs.org/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz", "es-get-iterator@^1.0.2": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.3.tgz", "es-iterator-helpers@^1.0.12": "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.0.14.tgz", "es-set-tostringtag@^2.0.1": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "es-shim-unscopables@^1.0.0": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz", "es-to-primitive@^1.2.1": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "es5-ext@^0.10.35": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.62.tgz", "es5-ext@^0.10.50": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.62.tgz", "es6-iterator@2.0.3": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "es6-iterator@^2.0.3": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "es6-symbol@^3.1.1": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.3.tgz", "es6-symbol@^3.1.3": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.3.tgz", "esbuild@^0.19.3": "https://registry.npmjs.org/esbuild/-/esbuild-0.19.3.tgz", "escalade@^3.0.2": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "escape-goat@^4.0.0": "https://registry.npmjs.org/escape-goat/-/escape-goat-4.0.0.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@2.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "escape-string-regexp@^5.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "escodegen@^1.14.3": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.3.tgz", "escodegen@^2.0.0": "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz", "eslint-config-prettier@^8.8.0": "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz", "eslint-config-react-app@^6.0.0": "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-6.0.0.tgz", "eslint-config-universe@^12.0.0": "https://registry.npmjs.org/eslint-config-universe/-/eslint-config-universe-12.0.0.tgz", "eslint-import-resolver-node@^0.3.7": "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz", "eslint-import-resolver-node@^0.3.9": "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz", "eslint-module-utils@^2.8.0": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.8.0.tgz", "eslint-plugin-es@^3.0.0": "https://registry.npmjs.org/eslint-plugin-es/-/eslint-plugin-es-3.0.1.tgz", "eslint-plugin-flowtype@^5.2.0": "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-5.10.0.tgz", "eslint-plugin-import@^2.22.1": "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.28.1.tgz", "eslint-plugin-import@^2.27.5": "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.29.0.tgz", "eslint-plugin-jest@^24.1.0": "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-24.7.0.tgz", "eslint-plugin-jsx-a11y@^6.3.1": "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.7.1.tgz", "eslint-plugin-node@^11.1.0": "https://registry.npmjs.org/eslint-plugin-node/-/eslint-plugin-node-11.1.0.tgz", "eslint-plugin-prettier@^5.0.0": "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.1.tgz", "eslint-plugin-react-hooks@^4.2.0": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz", "eslint-plugin-react-hooks@^4.6.0": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz", "eslint-plugin-react@^7.21.5": "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.33.2.tgz", "eslint-plugin-react@^7.32.2": "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.33.2.tgz", "eslint-plugin-testing-library@^3.9.2": "https://registry.npmjs.org/eslint-plugin-testing-library/-/eslint-plugin-testing-library-3.10.2.tgz", "eslint-scope@^4.0.3": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.3.tgz", "eslint-scope@^5.0.0": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-utils@^2.0.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-2.1.0.tgz", "eslint-utils@^2.1.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-2.1.0.tgz", "eslint-utils@^3.0.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz", "eslint-visitor-keys@^1.0.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^1.1.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^1.3.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^2.0.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^3.3.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.1": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-webpack-plugin@^2.5.2": "https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-2.7.0.tgz", "eslint@^7.11.0": "https://registry.npmjs.org/eslint/-/eslint-7.32.0.tgz", "espree@^7.3.0": "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz", "espree@^7.3.1": "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz", "esprima@^4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esprima@~4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esquery@^1.4.0": "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz", "esrecurse@^4.1.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "esrecurse@^4.3.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^4.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.3.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estree-walker@^0.6.1": "https://registry.npmjs.org/estree-walker/-/estree-walker-0.6.1.tgz", "estree-walker@^1.0.1": "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz", "estree-walker@^2.0.2": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "esutils@^2.0.2": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "event-target-shim@^5.0.0": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "event-target-shim@^5.0.1": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "eventemitter3@^4.0.0": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "eventemitter3@^4.0.4": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "events@^3.0.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "events@^3.3.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "eventsource@^2.0.2": "https://registry.npmjs.org/eventsource/-/eventsource-2.0.2.tgz", "evp_bytestokey@^1.0.0": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "evp_bytestokey@^1.0.3": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "exec-async@^2.2.0": "https://registry.npmjs.org/exec-async/-/exec-async-2.2.0.tgz", "exec-sh@^0.3.2": "https://registry.npmjs.org/exec-sh/-/exec-sh-0.3.6.tgz", "execa@5.0.0": "https://registry.npmjs.org/execa/-/execa-5.0.0.tgz", "execa@7.1.1": "https://registry.npmjs.org/execa/-/execa-7.1.1.tgz", "execa@^1.0.0": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "execa@^4.0.0": "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz", "execa@^5.0.0": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "execa@^5.1.1": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "execa@^7.1.1": "https://registry.npmjs.org/execa/-/execa-7.2.0.tgz", "exit@^0.1.2": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "expand-brackets@^2.1.4": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "expect@^26.6.0": "https://registry.npmjs.org/expect/-/expect-26.6.2.tgz", "expect@^26.6.2": "https://registry.npmjs.org/expect/-/expect-26.6.2.tgz", "expect@^29.0.0": "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz", "expect@^29.7.0": "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz", "expo-application@~5.3.0": "https://registry.npmjs.org/expo-application/-/expo-application-5.3.0.tgz", "expo-asset@~8.10.1": "https://registry.npmjs.org/expo-asset/-/expo-asset-8.10.1.tgz", "expo-build-properties@~0.8.3": "https://registry.npmjs.org/expo-build-properties/-/expo-build-properties-0.8.3.tgz", "expo-constants@~14.4.2": "https://registry.npmjs.org/expo-constants/-/expo-constants-14.4.2.tgz", "expo-file-system@~15.4.0": "https://registry.npmjs.org/expo-file-system/-/expo-file-system-15.4.2.tgz", "expo-file-system@~15.4.2": "https://registry.npmjs.org/expo-file-system/-/expo-file-system-15.4.2.tgz", "expo-font@~11.4.0": "https://registry.npmjs.org/expo-font/-/expo-font-11.4.0.tgz", "expo-keep-awake@~12.3.0": "https://registry.npmjs.org/expo-keep-awake/-/expo-keep-awake-12.3.0.tgz", "expo-module-scripts@^3.1.0": "https://registry.npmjs.org/expo-module-scripts/-/expo-module-scripts-3.1.0.tgz", "expo-modules-autolinking@1.5.0": "https://registry.npmjs.org/expo-modules-autolinking/-/expo-modules-autolinking-1.5.0.tgz", "expo-modules-core@1.5.4": "https://registry.npmjs.org/expo-modules-core/-/expo-modules-core-1.5.4.tgz", "expo-secure-store@^14.0.0": "https://registry.npmjs.org/expo-secure-store/-/expo-secure-store-14.0.1.tgz", "expo-splash-screen@~0.20.4": "https://registry.npmjs.org/expo-splash-screen/-/expo-splash-screen-0.20.4.tgz", "expo-status-bar@~1.6.0": "https://registry.npmjs.org/expo-status-bar/-/expo-status-bar-1.6.0.tgz", "expo@^49.0.0": "https://registry.npmjs.org/expo/-/expo-49.0.0.tgz", "express@^4.17.1": "https://registry.npmjs.org/express/-/express-4.18.2.tgz", "ext@^1.1.2": "https://registry.npmjs.org/ext/-/ext-1.7.0.tgz", "extend-shallow@^2.0.1": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "extend-shallow@^3.0.0": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "extend-shallow@^3.0.2": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "extend@~3.0.2": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "external-editor@^3.0.3": "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz", "extglob@^2.0.4": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "extsprintf@1.3.0": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "extsprintf@^1.2.0": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.4.1.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz", "fast-glob@3.2.7": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.7.tgz", "fast-glob@^3.1.1": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.1.tgz", "fast-glob@^3.2.11": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.1.tgz", "fast-glob@^3.2.5": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz", "fast-glob@^3.2.9": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz", "fast-glob@^3.3.0": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz", "fast-json-stable-stringify@2.x": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.1.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-levenshtein@~2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-loops@^1.1.3": "https://registry.npmjs.org/fast-loops/-/fast-loops-1.1.3.tgz", "fast-xml-parser@^4.0.12": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.15.0.tgz", "faye-websocket@^0.11.3": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "faye-websocket@^0.11.4": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "fb-watchman@^2.0.0": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "fbemitter@^3.0.0": "https://registry.npmjs.org/fbemitter/-/fbemitter-3.0.0.tgz", "fbjs-css-vars@^1.0.0": "https://registry.npmjs.org/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz", "fbjs@^0.8.4": "https://registry.npmjs.org/fbjs/-/fbjs-0.8.18.tgz", "fbjs@^0.8.9": "https://registry.npmjs.org/fbjs/-/fbjs-0.8.18.tgz", "fbjs@^3.0.0": "https://registry.npmjs.org/fbjs/-/fbjs-3.0.4.tgz", "fbjs@^3.0.4": "https://registry.npmjs.org/fbjs/-/fbjs-3.0.5.tgz", "fbjs@~0.8.9": "https://registry.npmjs.org/fbjs/-/fbjs-0.8.18.tgz", "fetch-blob@^3.1.2": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "fetch-blob@^3.1.4": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "fetch-retry@^4.1.1": "https://registry.npmjs.org/fetch-retry/-/fetch-retry-4.1.1.tgz", "figgy-pudding@^3.5.1": "https://registry.npmjs.org/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "figures@3.2.0": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "figures@^3.0.0": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "figures@^5.0.0": "https://registry.npmjs.org/figures/-/figures-5.0.0.tgz", "file-entry-cache@^6.0.1": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "file-loader@6.1.1": "https://registry.npmjs.org/file-loader/-/file-loader-6.1.1.tgz", "file-uri-to-path@1.0.0": "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "file-url@3.0.0": "https://registry.npmjs.org/file-url/-/file-url-3.0.0.tgz", "filelist@^1.0.1": "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz", "filesize@6.1.0": "https://registry.npmjs.org/filesize/-/filesize-6.1.0.tgz", "fill-range@^4.0.0": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "filter-obj@^1.1.0": "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz", "finalhandler@1.1.2": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "finalhandler@1.2.0": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "find-babel-config@^2.0.0": "https://registry.npmjs.org/find-babel-config/-/find-babel-config-2.0.0.tgz", "find-cache-dir@^2.0.0": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "find-cache-dir@^2.1.0": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "find-cache-dir@^3.3.1": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "find-up@4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "find-up@^2.0.0": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "find-up@^3.0.0": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "find-up@^4.0.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "find-up@~5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "find-yarn-workspace-root@^2.0.0": "https://registry.npmjs.org/find-yarn-workspace-root/-/find-yarn-workspace-root-2.0.0.tgz", "find-yarn-workspace-root@~2.0.0": "https://registry.npmjs.org/find-yarn-workspace-root/-/find-yarn-workspace-root-2.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.1.0.tgz", "flat@^5.0.2": "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz", "flatted@^3.2.4": "https://registry.npmjs.org/flatted/-/flatted-3.2.7.tgz", "flatted@^3.2.7": "https://registry.npmjs.org/flatted/-/flatted-3.2.7.tgz", "flatten@^1.0.2": "https://registry.npmjs.org/flatten/-/flatten-1.0.3.tgz", "flow-enums-runtime@^0.0.5": "https://registry.npmjs.org/flow-enums-runtime/-/flow-enums-runtime-0.0.5.tgz", "flow-parser@0.*": "https://registry.npmjs.org/flow-parser/-/flow-parser-0.208.1.tgz", "flow-parser@^0.206.0": "https://registry.npmjs.org/flow-parser/-/flow-parser-0.206.0.tgz", "flowed-st@^1.0.5": "https://registry.npmjs.org/flowed-st/-/flowed-st-1.0.5.tgz", "flush-write-stream@^1.0.0": "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "follow-redirects@^1.0.0": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "follow-redirects@^1.15.0": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "follow-redirects@^1.15.4": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.5.tgz", "fontfaceobserver@^2.1.0": "https://registry.npmjs.org/fontfaceobserver/-/fontfaceobserver-2.3.0.tgz", "for-each@^0.3.3": "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz", "for-in@^0.1.3": "https://registry.npmjs.org/for-in/-/for-in-0.1.8.tgz", "for-in@^1.0.1": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "for-in@^1.0.2": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "for-own@^1.0.0": "https://registry.npmjs.org/for-own/-/for-own-1.0.0.tgz", "forever-agent@~0.6.1": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "fork-ts-checker-webpack-plugin@4.1.6": "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-4.1.6.tgz", "form-data-encoder@^2.1.2": "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-2.1.4.tgz", "form-data@^3.0.0": "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz", "form-data@^3.0.1": "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "form-data@~2.3.2": "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz", "formdata-polyfill@^4.0.10": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fragment-cache@^0.2.1": "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz", "freeport-async@2.0.0": "https://registry.npmjs.org/freeport-async/-/freeport-async-2.0.0.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "from2@^2.1.0": "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz", "fs-constants@^1.0.0": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "fs-extra@9.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.0.0.tgz", "fs-extra@9.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@^11.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-11.1.1.tgz", "fs-extra@^7.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-7.0.1.tgz", "fs-extra@^8.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz", "fs-extra@^9.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@^9.0.1": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@^9.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@~8.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz", "fs-minipass@^2.0.0": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "fs-minipass@^2.1.0": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "fs-minipass@^3.0.0": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.1.tgz", "fs-readdir-recursive@^1.1.0": "https://registry.npmjs.org/fs-readdir-recursive/-/fs-readdir-recursive-1.1.0.tgz", "fs-write-stream-atomic@^1.0.8": "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^1.2.7": "https://registry.npmjs.org/fsevents/-/fsevents-1.2.13.tgz", "fsevents@^2.1.2": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "fsevents@^2.1.3": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "fsevents@^2.3.2": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz", "fsevents@~2.3.2": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "function-bind@^1.1.1": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "function.prototype.name@^1.1.5": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz", "functional-red-black-tree@^1.0.1": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "functions-have-names@^1.2.3": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz", "fuse.js@3.4.5": "https://registry.npmjs.org/fuse.js/-/fuse.js-3.4.5.tgz", "gauge@^4.0.3": "https://registry.npmjs.org/gauge/-/gauge-4.0.4.tgz", "gauge@^5.0.0": "https://registry.npmjs.org/gauge/-/gauge-5.0.0.tgz", "gensync@^1.0.0-beta.1": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.1": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.0.2": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "get-intrinsic@^1.1.1": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "get-intrinsic@^1.1.3": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "get-intrinsic@^1.2.0": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "get-intrinsic@^1.2.1": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "get-own-enumerable-property-symbols@^3.0.0": "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz", "get-package-type@^0.1.0": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "get-pkg-repo@^4.0.0": "https://registry.npmjs.org/get-pkg-repo/-/get-pkg-repo-4.2.1.tgz", "get-port@5.1.1": "https://registry.npmjs.org/get-port/-/get-port-5.1.1.tgz", "get-port@^3.2.0": "https://registry.npmjs.org/get-port/-/get-port-3.2.0.tgz", "get-stream@6.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.0.tgz", "get-stream@^4.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz", "get-stream@^5.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "get-stream@^6.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "get-stream@^6.0.1": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "get-symbol-description@^1.0.0": "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "get-uri@^6.0.1": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.1.tgz", "get-value@^2.0.3": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "get-value@^2.0.6": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "getenv@^1.0.0": "https://registry.npmjs.org/getenv/-/getenv-1.0.0.tgz", "getpass@^0.1.1": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "git-raw-commits@^2.0.8": "https://registry.npmjs.org/git-raw-commits/-/git-raw-commits-2.0.11.tgz", "git-remote-origin-url@^2.0.0": "https://registry.npmjs.org/git-remote-origin-url/-/git-remote-origin-url-2.0.0.tgz", "git-semver-tags@^4.1.1": "https://registry.npmjs.org/git-semver-tags/-/git-semver-tags-4.1.1.tgz", "git-up@^7.0.0": "https://registry.npmjs.org/git-up/-/git-up-7.0.0.tgz", "git-url-parse@13.1.0": "https://registry.npmjs.org/git-url-parse/-/git-url-parse-13.1.0.tgz", "gitconfiglocal@^1.0.0": "https://registry.npmjs.org/gitconfiglocal/-/gitconfiglocal-1.0.0.tgz", "glob-parent@5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^3.1.0": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob@7.1.4": "https://registry.npmjs.org/glob/-/glob-7.1.4.tgz", "glob@7.1.6": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz", "glob@^6.0.1": "https://registry.npmjs.org/glob/-/glob-6.0.4.tgz", "glob@^7.0.0": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.0.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.1": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.2": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.4": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.6": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.7": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.2.0": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^8.0.1": "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz", "glob@^8.0.3": "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz", "glob@^9.2.0": "https://registry.npmjs.org/glob/-/glob-9.3.4.tgz", "glob@^9.3.0": "https://registry.npmjs.org/glob/-/glob-9.3.4.tgz", "glob@^9.3.1": "https://registry.npmjs.org/glob/-/glob-9.3.4.tgz", "global-dirs@^3.0.0": "https://registry.npmjs.org/global-dirs/-/global-dirs-3.0.1.tgz", "global-modules@2.0.0": "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz", "global-prefix@^3.0.0": "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz", "global@^4.3.2": "https://registry.npmjs.org/global/-/global-4.4.0.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "globals@^13.6.0": "https://registry.npmjs.org/globals/-/globals-13.21.0.tgz", "globals@^13.9.0": "https://registry.npmjs.org/globals/-/globals-13.21.0.tgz", "globalthis@^1.0.3": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.3.tgz", "globby@11.0.1": "https://registry.npmjs.org/globby/-/globby-11.0.1.tgz", "globby@11.1.0": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@13.1.4": "https://registry.npmjs.org/globby/-/globby-13.1.4.tgz", "globby@^11.0.1": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@^11.0.3": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@^11.1.0": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@^5.0.0": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "globby@^6.1.0": "https://registry.npmjs.org/globby/-/globby-6.1.0.tgz", "google-auth-library@^0.10.0": "https://registry.npmjs.org/google-auth-library/-/google-auth-library-0.10.0.tgz", "google-auth-library@~0.10.0": "https://registry.npmjs.org/google-auth-library/-/google-auth-library-0.10.0.tgz", "google-libphonenumber@^3.2.10": "https://registry.npmjs.org/google-libphonenumber/-/google-libphonenumber-3.2.33.tgz", "google-p12-pem@^0.1.0": "https://registry.npmjs.org/google-p12-pem/-/google-p12-pem-0.1.2.tgz", "googleapis@^19.0.0": "https://registry.npmjs.org/googleapis/-/googleapis-19.0.0.tgz", "gopd@^1.0.1": "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz", "got@12.6.1": "https://registry.npmjs.org/got/-/got-12.6.1.tgz", "got@^12.1.0": "https://registry.npmjs.org/got/-/got-12.6.1.tgz", "graceful-fs@4.2.10": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz", "graceful-fs@^4.1.11": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.15": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.2": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.3": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.9": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graphemer@^1.4.0": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "graphql-tag@^2.10.1": "https://registry.npmjs.org/graphql-tag/-/graphql-tag-2.12.6.tgz", "graphql@15.8.0": "https://registry.npmjs.org/graphql/-/graphql-15.8.0.tgz", "graphql@^16.8.1": "https://registry.npmjs.org/graphql/-/graphql-16.8.1.tgz", "growly@^1.3.0": "https://registry.npmjs.org/growly/-/growly-1.3.0.tgz", "gtoken@^1.2.1": "https://registry.npmjs.org/gtoken/-/gtoken-1.2.3.tgz", "gzip-size@5.1.1": "https://registry.npmjs.org/gzip-size/-/gzip-size-5.1.1.tgz", "handle-thing@^2.0.0": "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz", "handlebars@^4.7.7": "https://registry.npmjs.org/handlebars/-/handlebars-4.7.7.tgz", "har-schema@^2.0.0": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "har-validator@~5.1.3": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz", "hard-rejection@^2.1.0": "https://registry.npmjs.org/hard-rejection/-/hard-rejection-2.1.0.tgz", "harmony-reflect@^1.4.6": "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz", "has-bigints@^1.0.1": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz", "has-bigints@^1.0.2": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-property-descriptors@^1.0.0": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz", "has-proto@^1.0.1": "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz", "has-symbols@^1.0.1": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "has-symbols@^1.0.2": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "has-symbols@^1.0.3": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "has-tostringtag@^1.0.0": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "has-unicode@2.0.1": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "has-unicode@^2.0.1": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "has-value@^0.3.1": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "has-value@^1.0.0": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "has-values@^0.1.4": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "has-values@^1.0.0": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "has-yarn@^3.0.0": "https://registry.npmjs.org/has-yarn/-/has-yarn-3.0.0.tgz", "has@^1.0.0": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "has@^1.0.3": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "hash-base@^3.0.0": "https://registry.npmjs.org/hash-base/-/hash-base-3.1.0.tgz", "hash.js@^1.0.0": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "hash.js@^1.0.3": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "hasown@^2.0.0": "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz", "he@^1.2.0": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "hermes-estree@0.12.0": "https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.12.0.tgz", "hermes-estree@0.8.0": "https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.8.0.tgz", "hermes-parser@0.12.0": "https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.12.0.tgz", "hermes-parser@0.8.0": "https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.8.0.tgz", "hermes-profile-transformer@^0.0.6": "https://registry.npmjs.org/hermes-profile-transformer/-/hermes-profile-transformer-0.0.6.tgz", "hex-color-regex@^1.1.0": "https://registry.npmjs.org/hex-color-regex/-/hex-color-regex-1.1.0.tgz", "hex-to-rgb@^1.0.0": "https://registry.npmjs.org/hex-to-rgb/-/hex-to-rgb-1.0.1.tgz", "hmac-drbg@^1.0.1": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "hoist-non-react-statics@^3.3.0": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoopy@^0.1.4": "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz", "hosted-git-info@^2.1.4": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "hosted-git-info@^3.0.2": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-3.0.8.tgz", "hosted-git-info@^3.0.6": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-3.0.8.tgz", "hosted-git-info@^4.0.0": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "hosted-git-info@^4.0.1": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "hosted-git-info@^5.0.0": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-5.2.1.tgz", "hosted-git-info@^6.0.0": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-6.1.1.tgz", "hosted-git-info@^6.1.1": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-6.1.1.tgz", "hpack.js@^2.1.6": "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz", "hsl-regex@^1.0.0": "https://registry.npmjs.org/hsl-regex/-/hsl-regex-1.0.0.tgz", "hsla-regex@^1.0.0": "https://registry.npmjs.org/hsla-regex/-/hsla-regex-1.0.0.tgz", "html-encoding-sniffer@^2.0.1": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz", "html-encoding-sniffer@^3.0.0": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "html-entities@^1.2.1": "https://registry.npmjs.org/html-entities/-/html-entities-1.4.0.tgz", "html-entities@^1.3.1": "https://registry.npmjs.org/html-entities/-/html-entities-1.4.0.tgz", "html-entities@^2.3.3": "https://registry.npmjs.org/html-entities/-/html-entities-2.4.0.tgz", "html-escaper@^2.0.0": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "html-minifier-terser@^5.0.1": "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-5.1.1.tgz", "html-parse-stringify@^3.0.1": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz", "html-webpack-plugin@4.5.0": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.5.0.tgz", "htmlparser2@5.0.1": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-5.0.1.tgz", "htmlparser2@^6.1.0": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz", "http-cache-semantics@^4.1.0": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "http-cache-semantics@^4.1.1": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "http-deceiver@^1.2.7": "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "http-errors@~1.6.2": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "http-parser-js@>=0.5.1": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.8.tgz", "http-proxy-agent@^4.0.1": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "http-proxy-agent@^5.0.0": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", "http-proxy-agent@^7.0.0": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.0.tgz", "http-proxy-middleware@0.19.1": "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz", "http-proxy@^1.17.0": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "http-signature@~1.2.0": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "http2-wrapper@^2.1.10": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.2.0.tgz", "https-browserify@^1.0.0": "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz", "https-proxy-agent@^5.0.0": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "https-proxy-agent@^5.0.1": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "https-proxy-agent@^7.0.0": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.2.tgz", "human-signals@^1.1.1": "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz", "human-signals@^2.1.0": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "human-signals@^4.3.0": "https://registry.npmjs.org/human-signals/-/human-signals-4.3.1.tgz", "humanize-ms@^1.2.1": "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz", "hyphenate-style-name@^1.0.3": "https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.0.4.tgz", "i18n-js@^3.0.11": "https://registry.npmjs.org/i18n-js/-/i18n-js-3.9.2.tgz", "i18next@^22.5.1": "https://registry.npmjs.org/i18next/-/i18next-22.5.1.tgz", "iconv-lite@0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@0.6.3": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "iconv-lite@^0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.6.2": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "icss-utils@^4.0.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-4.1.1.tgz", "icss-utils@^4.1.1": "https://registry.npmjs.org/icss-utils/-/icss-utils-4.1.1.tgz", "identity-obj-proxy@3.0.0": "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz", "ieee754@^1.1.13": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ieee754@^1.1.4": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ieee754@^1.2.1": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "iferr@^0.1.5": "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz", "ignore-walk@^5.0.1": "https://registry.npmjs.org/ignore-walk/-/ignore-walk-5.0.1.tgz", "ignore-walk@^6.0.0": "https://registry.npmjs.org/ignore-walk/-/ignore-walk-6.0.2.tgz", "ignore@^4.0.6": "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz", "ignore@^5.0.4": "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz", "ignore@^5.1.1": "https://registry.npmjs.org/ignore/-/ignore-5.3.0.tgz", "ignore@^5.1.4": "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz", "ignore@^5.1.8": "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz", "ignore@^5.2.0": "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz", "ignore@^5.2.4": "https://registry.npmjs.org/ignore/-/ignore-5.3.0.tgz", "image-size@^1.0.2": "https://registry.npmjs.org/image-size/-/image-size-1.0.2.tgz", "immer@8.0.1": "https://registry.npmjs.org/immer/-/immer-8.0.1.tgz", "immer@^9.0.15": "https://registry.npmjs.org/immer/-/immer-9.0.21.tgz", "immer@^9.0.6": "https://registry.npmjs.org/immer/-/immer-9.0.21.tgz", "immutable@~3.7.6": "https://registry.npmjs.org/immutable/-/immutable-3.7.6.tgz", "import-cwd@^2.0.0": "https://registry.npmjs.org/import-cwd/-/import-cwd-2.1.0.tgz", "import-fresh@^2.0.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz", "import-fresh@^3.0.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "import-fresh@^3.2.1": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "import-from@^2.1.0": "https://registry.npmjs.org/import-from/-/import-from-2.1.0.tgz", "import-lazy@^4.0.0": "https://registry.npmjs.org/import-lazy/-/import-lazy-4.0.0.tgz", "import-local@^2.0.0": "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz", "import-local@^3.0.2": "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "indent-string@^4.0.0": "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz", "indexes-of@^1.0.1": "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz", "infer-owner@^1.0.3": "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz", "infer-owner@^1.0.4": "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.1": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "inherits@2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.1": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ini@2.0.0": "https://registry.npmjs.org/ini/-/ini-2.0.0.tgz", "ini@^1.3.2": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "ini@^1.3.4": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "ini@^1.3.5": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "ini@~1.3.0": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "init-package-json@3.0.2": "https://registry.npmjs.org/init-package-json/-/init-package-json-3.0.2.tgz", "init-package-json@^3.0.2": "https://registry.npmjs.org/init-package-json/-/init-package-json-3.0.2.tgz", "inline-style-prefixer@^6.0.1": "https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-6.0.4.tgz", "inquirer@8.2.4": "https://registry.npmjs.org/inquirer/-/inquirer-8.2.4.tgz", "inquirer@9.2.6": "https://registry.npmjs.org/inquirer/-/inquirer-9.2.6.tgz", "inquirer@^8.2.4": "https://registry.npmjs.org/inquirer/-/inquirer-8.2.5.tgz", "internal-ip@4.3.0": "https://registry.npmjs.org/internal-ip/-/internal-ip-4.3.0.tgz", "internal-ip@^4.3.0": "https://registry.npmjs.org/internal-ip/-/internal-ip-4.3.0.tgz", "internal-slot@^1.0.4": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.5.tgz", "internal-slot@^1.0.5": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.5.tgz", "interpret@^1.0.0": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "intl-pluralrules@^2.0.1": "https://registry.npmjs.org/intl-pluralrules/-/intl-pluralrules-2.0.1.tgz", "intl@^1.2.5": "https://registry.npmjs.org/intl/-/intl-1.2.5.tgz", "invariant@*": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "invariant@2.2.4": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "invariant@^2.2.2": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "invariant@^2.2.4": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "ip-regex@^2.1.0": "https://registry.npmjs.org/ip-regex/-/ip-regex-2.1.0.tgz", "ip@^1.1.0": "https://registry.npmjs.org/ip/-/ip-1.1.8.tgz", "ip@^1.1.5": "https://registry.npmjs.org/ip/-/ip-1.1.8.tgz", "ip@^1.1.8": "https://registry.npmjs.org/ip/-/ip-1.1.8.tgz", "ip@^2.0.0": "https://registry.npmjs.org/ip/-/ip-2.0.0.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "ipaddr.js@^1.9.0": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-absolute-url@^2.0.0": "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-2.1.0.tgz", "is-absolute-url@^3.0.3": "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-3.0.3.tgz", "is-accessor-descriptor@^0.1.6": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "is-accessor-descriptor@^1.0.0": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "is-arguments@^1.0.4": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz", "is-arguments@^1.1.1": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz", "is-array-buffer@^3.0.1": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz", "is-array-buffer@^3.0.2": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz", "is-arrayish@^0.2.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-arrayish@^0.3.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "is-async-function@^2.0.0": "https://registry.npmjs.org/is-async-function/-/is-async-function-2.0.0.tgz", "is-bigint@^1.0.1": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz", "is-binary-path@^1.0.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-boolean-object@^1.1.0": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "is-buffer@^1.1.5": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "is-buffer@~1.1.1": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "is-buffer@~1.1.6": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "is-callable@^1.1.3": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "is-callable@^1.1.4": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "is-callable@^1.2.7": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "is-ci@2.0.0": "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz", "is-ci@3.0.1": "https://registry.npmjs.org/is-ci/-/is-ci-3.0.1.tgz", "is-ci@^2.0.0": "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz", "is-ci@^3.0.1": "https://registry.npmjs.org/is-ci/-/is-ci-3.0.1.tgz", "is-color-stop@^1.0.0": "https://registry.npmjs.org/is-color-stop/-/is-color-stop-1.1.0.tgz", "is-core-module@^2.0.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "is-core-module@^2.11.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz", "is-core-module@^2.13.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "is-core-module@^2.13.1": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz", "is-core-module@^2.5.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz", "is-core-module@^2.8.1": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz", "is-core-module@^2.9.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz", "is-data-descriptor@^0.1.4": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "is-data-descriptor@^1.0.0": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "is-date-object@^1.0.1": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz", "is-date-object@^1.0.5": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz", "is-descriptor@^0.1.0": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "is-descriptor@^1.0.0": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "is-descriptor@^1.0.2": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "is-directory@^0.3.1": "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz", "is-docker@^2.0.0": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-docker@^2.1.1": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-docker@^3.0.0": "https://registry.npmjs.org/is-docker/-/is-docker-3.0.0.tgz", "is-extendable@^0.1.0": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^0.1.1": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^1.0.1": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "is-extglob@^1.0.0": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "is-extglob@^2.1.0": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-finalizationregistry@^1.0.2": "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.0.2.tgz", "is-fullwidth-code-point@^2.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "is-generator-function@^1.0.10": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz", "is-glob@^2.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "is-glob@^3.1.0": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-inside-container@^1.0.0": "https://registry.npmjs.org/is-inside-container/-/is-inside-container-1.0.0.tgz", "is-installed-globally@^0.4.0": "https://registry.npmjs.org/is-installed-globally/-/is-installed-globally-0.4.0.tgz", "is-interactive@^1.0.0": "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz", "is-interactive@^2.0.0": "https://registry.npmjs.org/is-interactive/-/is-interactive-2.0.0.tgz", "is-invalid-path@^0.1.0": "https://registry.npmjs.org/is-invalid-path/-/is-invalid-path-0.1.0.tgz", "is-lambda@^1.0.1": "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz", "is-map@^2.0.1": "https://registry.npmjs.org/is-map/-/is-map-2.0.2.tgz", "is-map@^2.0.2": "https://registry.npmjs.org/is-map/-/is-map-2.0.2.tgz", "is-module@^1.0.0": "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz", "is-negative-zero@^2.0.2": "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "is-npm@^6.0.0": "https://registry.npmjs.org/is-npm/-/is-npm-6.0.0.tgz", "is-number-object@^1.0.4": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz", "is-number@^3.0.0": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-obj@^1.0.1": "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz", "is-obj@^2.0.0": "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz", "is-path-cwd@^1.0.0": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "is-path-cwd@^2.0.0": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "is-path-cwd@^2.2.0": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "is-path-in-cwd@^1.0.0": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz", "is-path-in-cwd@^2.0.0": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz", "is-path-inside@^1.0.0": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz", "is-path-inside@^2.1.0": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-2.1.0.tgz", "is-path-inside@^3.0.2": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz", "is-plain-obj@^1.0.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-plain-obj@^1.1.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-plain-obj@^2.1.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "is-plain-object@^2.0.3": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-plain-object@^2.0.4": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-plain-object@^5.0.0": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz", "is-potential-custom-element-name@^1.0.1": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", "is-regex@^1.0.4": "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz", "is-regex@^1.1.4": "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz", "is-regexp@^1.0.0": "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz", "is-resolvable@^1.0.0": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz", "is-root@2.1.0": "https://registry.npmjs.org/is-root/-/is-root-2.1.0.tgz", "is-root@^2.1.0": "https://registry.npmjs.org/is-root/-/is-root-2.1.0.tgz", "is-set@^2.0.1": "https://registry.npmjs.org/is-set/-/is-set-2.0.2.tgz", "is-set@^2.0.2": "https://registry.npmjs.org/is-set/-/is-set-2.0.2.tgz", "is-shared-array-buffer@^1.0.2": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "is-ssh@^1.4.0": "https://registry.npmjs.org/is-ssh/-/is-ssh-1.4.0.tgz", "is-stream@2.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.0.tgz", "is-stream@^1.0.1": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "is-stream@^1.1.0": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "is-stream@^2.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "is-stream@^3.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz", "is-string@^1.0.5": "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz", "is-string@^1.0.7": "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz", "is-symbol@^1.0.2": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz", "is-symbol@^1.0.3": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz", "is-text-path@^1.0.1": "https://registry.npmjs.org/is-text-path/-/is-text-path-1.0.1.tgz", "is-typed-array@^1.1.10": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz", "is-typed-array@^1.1.9": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz", "is-typedarray@^1.0.0": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "is-typedarray@~1.0.0": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "is-unicode-supported@^1.1.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz", "is-unicode-supported@^1.2.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz", "is-valid-path@^0.1.1": "https://registry.npmjs.org/is-valid-path/-/is-valid-path-0.1.1.tgz", "is-weakmap@^2.0.1": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.1.tgz", "is-weakref@^1.0.2": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz", "is-weakset@^2.0.1": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.2.tgz", "is-windows@^1.0.2": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "is-wsl@^1.1.0": "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz", "is-wsl@^2.1.1": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "is-wsl@^2.2.0": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "is-yarn-global@^0.4.0": "https://registry.npmjs.org/is-yarn-global/-/is-yarn-global-0.4.1.tgz", "isarray@1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isarray@^1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isarray@^2.0.5": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz", "isarray@~1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "isobject@^2.0.0": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "isobject@^3.0.0": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "isobject@^3.0.1": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "isomorphic-fetch@^2.1.1": "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz", "isstream@~0.1.2": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "issue-parser@6.0.0": "https://registry.npmjs.org/issue-parser/-/issue-parser-6.0.0.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "istanbul-lib-instrument@^4.0.3": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.3.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "istanbul-lib-instrument@^6.0.0": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.1.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "istanbul-reports@^3.0.2": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.6.tgz", "istanbul-reports@^3.1.3": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.6.tgz", "iterate-iterator@^1.0.1": "https://registry.npmjs.org/iterate-iterator/-/iterate-iterator-1.0.2.tgz", "iterate-value@^1.0.2": "https://registry.npmjs.org/iterate-value/-/iterate-value-1.0.2.tgz", "iterator.prototype@^1.1.0": "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.1.tgz", "jake@^10.8.5": "https://registry.npmjs.org/jake/-/jake-10.8.5.tgz", "jest-changed-files@^26.6.2": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.6.2.tgz", "jest-changed-files@^29.7.0": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "jest-circus@26.6.0": "https://registry.npmjs.org/jest-circus/-/jest-circus-26.6.0.tgz", "jest-circus@^29.7.0": "https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz", "jest-cli@^26.6.0": "https://registry.npmjs.org/jest-cli/-/jest-cli-26.6.3.tgz", "jest-cli@^29.7.0": "https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz", "jest-config@^26.6.3": "https://registry.npmjs.org/jest-config/-/jest-config-26.6.3.tgz", "jest-config@^29.7.0": "https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz", "jest-diff@^26.6.2": "https://registry.npmjs.org/jest-diff/-/jest-diff-26.6.2.tgz", "jest-diff@^29.7.0": "https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz", "jest-docblock@^26.0.0": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-26.0.0.tgz", "jest-docblock@^29.7.0": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz", "jest-each@^26.6.0": "https://registry.npmjs.org/jest-each/-/jest-each-26.6.2.tgz", "jest-each@^26.6.2": "https://registry.npmjs.org/jest-each/-/jest-each-26.6.2.tgz", "jest-each@^29.7.0": "https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz", "jest-environment-jsdom@^26.6.2": "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-26.6.2.tgz", "jest-environment-jsdom@^29.2.1": "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-29.7.0.tgz", "jest-environment-node@^26.6.2": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-26.6.2.tgz", "jest-environment-node@^29.2.1": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.5.0.tgz", "jest-environment-node@^29.7.0": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "jest-expo@~50.0.0-alpha.0": "https://registry.npmjs.org/jest-expo/-/jest-expo-50.0.0.tgz", "jest-get-type@^26.3.0": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.3.0.tgz", "jest-get-type@^29.4.3": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.4.3.tgz", "jest-get-type@^29.6.3": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz", "jest-haste-map@^26.6.2": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-26.6.2.tgz", "jest-haste-map@^29.7.0": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "jest-jasmine2@^26.6.3": "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-26.6.3.tgz", "jest-leak-detector@^26.6.2": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.6.2.tgz", "jest-leak-detector@^29.7.0": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "jest-matcher-utils@^26.6.0": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-26.6.2.tgz", "jest-matcher-utils@^26.6.2": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-26.6.2.tgz", "jest-matcher-utils@^29.7.0": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "jest-message-util@^26.6.0": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-26.6.2.tgz", "jest-message-util@^26.6.2": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-26.6.2.tgz", "jest-message-util@^29.5.0": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.5.0.tgz", "jest-message-util@^29.7.0": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz", "jest-mock-axios@^4.7.1": "https://registry.npmjs.org/jest-mock-axios/-/jest-mock-axios-4.7.3.tgz", "jest-mock@^26.6.2": "https://registry.npmjs.org/jest-mock/-/jest-mock-26.6.2.tgz", "jest-mock@^29.5.0": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.5.0.tgz", "jest-mock@^29.7.0": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz", "jest-pnp-resolver@^1.2.2": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "jest-regex-util@^26.0.0": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0.tgz", "jest-regex-util@^27.0.6": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.5.1.tgz", "jest-regex-util@^29.0.0": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "jest-regex-util@^29.6.3": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "jest-resolve-dependencies@^26.6.3": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-26.6.3.tgz", "jest-resolve-dependencies@^29.7.0": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "jest-resolve@26.6.0": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-26.6.0.tgz", "jest-resolve@^26.6.2": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-26.6.2.tgz", "jest-resolve@^29.7.0": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz", "jest-runner@^26.6.0": "https://registry.npmjs.org/jest-runner/-/jest-runner-26.6.3.tgz", "jest-runner@^26.6.3": "https://registry.npmjs.org/jest-runner/-/jest-runner-26.6.3.tgz", "jest-runner@^29.7.0": "https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz", "jest-runtime@^26.6.0": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-26.6.3.tgz", "jest-runtime@^26.6.3": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-26.6.3.tgz", "jest-runtime@^29.7.0": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz", "jest-serializer@^26.6.2": "https://registry.npmjs.org/jest-serializer/-/jest-serializer-26.6.2.tgz", "jest-snapshot@^26.6.0": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-26.6.2.tgz", "jest-snapshot@^26.6.2": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-26.6.2.tgz", "jest-snapshot@^29.7.0": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "jest-util@^26.6.0": "https://registry.npmjs.org/jest-util/-/jest-util-26.6.2.tgz", "jest-util@^26.6.2": "https://registry.npmjs.org/jest-util/-/jest-util-26.6.2.tgz", "jest-util@^27.2.0": "https://registry.npmjs.org/jest-util/-/jest-util-27.5.1.tgz", "jest-util@^29.0.0": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz", "jest-util@^29.5.0": "https://registry.npmjs.org/jest-util/-/jest-util-29.5.0.tgz", "jest-util@^29.7.0": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz", "jest-validate@^26.6.2": "https://registry.npmjs.org/jest-validate/-/jest-validate-26.6.2.tgz", "jest-validate@^29.2.1": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.5.0.tgz", "jest-validate@^29.7.0": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz", "jest-watch-select-projects@^2.0.0": "https://registry.npmjs.org/jest-watch-select-projects/-/jest-watch-select-projects-2.0.0.tgz", "jest-watch-typeahead@0.6.1": "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-0.6.1.tgz", "jest-watch-typeahead@2.2.1": "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-2.2.1.tgz", "jest-watcher@^26.3.0": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.2.tgz", "jest-watcher@^26.6.2": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.2.tgz", "jest-watcher@^29.0.0": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz", "jest-watcher@^29.7.0": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz", "jest-worker@^24.9.0": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.9.0.tgz", "jest-worker@^26.5.0": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz", "jest-worker@^26.6.2": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz", "jest-worker@^27.2.0": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^27.5.1": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^29.7.0": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz", "jest@26.6.0": "https://registry.npmjs.org/jest/-/jest-26.6.0.tgz", "jest@~29.7.0": "https://registry.npmjs.org/jest/-/jest-29.7.0.tgz", "jimp-compact@0.16.1": "https://registry.npmjs.org/jimp-compact/-/jimp-compact-0.16.1.tgz", "joi@^17.2.1": "https://registry.npmjs.org/joi/-/joi-17.9.2.tgz", "join-component@^1.1.0": "https://registry.npmjs.org/join-component/-/join-component-1.1.0.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "js-yaml@^3.10.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^3.13.1": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "jsbn@~0.1.0": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "jsc-android@^250231.0.0": "https://registry.npmjs.org/jsc-android/-/jsc-android-250231.0.0.tgz", "jsc-safe-url@^0.2.2": "https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz", "jsc-safe-url@^0.2.4": "https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz", "jscodeshift@^0.14.0": "https://registry.npmjs.org/jscodeshift/-/jscodeshift-0.14.0.tgz", "jsdom@^16.4.0": "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz", "jsdom@^20.0.0": "https://registry.npmjs.org/jsdom/-/jsdom-20.0.3.tgz", "jsesc@^2.5.1": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "jsesc@~0.5.0": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "json-buffer@3.0.1": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "json-parse-better-errors@^1.0.1": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "json-parse-better-errors@^1.0.2": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^2.3.1": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^3.0.0": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.0.tgz", "json-schema-deref-sync@^0.13.0": "https://registry.npmjs.org/json-schema-deref-sync/-/json-schema-deref-sync-0.13.0.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-schema@0.4.0": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json-stringify-nice@^1.1.4": "https://registry.npmjs.org/json-stringify-nice/-/json-stringify-nice-1.1.4.tgz", "json-stringify-safe@^5.0.1": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json-stringify-safe@~5.0.1": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json-to-graphql-query@^2.1.0": "https://registry.npmjs.org/json-to-graphql-query/-/json-to-graphql-query-2.2.5.tgz", "json5@^1.0.1": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "json5@^1.0.2": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "json5@^2.1.1": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.1.2": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.2.2": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "jsonc-parser@3.2.0": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.2.0.tgz", "jsonfile@^4.0.0": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "jsonparse@^1.2.0": "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz", "jsonparse@^1.3.1": "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz", "jsprim@^1.2.2": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz", "jsx-ast-utils@^2.4.1 || ^3.0.0": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz", "jsx-ast-utils@^3.3.3": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz", "just-diff-apply@^5.2.0": "https://registry.npmjs.org/just-diff-apply/-/just-diff-apply-5.5.0.tgz", "just-diff@^6.0.0": "https://registry.npmjs.org/just-diff/-/just-diff-6.0.2.tgz", "jwa@^1.4.1": "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz", "jws@^3.0.0": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "jws@^3.1.4": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "keymirror@^0.1.1": "https://registry.npmjs.org/keymirror/-/keymirror-0.1.1.tgz", "keyv@^4.5.3": "https://registry.npmjs.org/keyv/-/keyv-4.5.3.tgz", "killable@^1.0.1": "https://registry.npmjs.org/killable/-/killable-1.0.1.tgz", "kind-of@^3.0.2": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.0.3": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.2.0": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^4.0.0": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "kind-of@^5.0.0": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "kind-of@^6.0.0": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "kind-of@^6.0.1": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "kind-of@^6.0.2": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "kind-of@^6.0.3": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "kleur@^3.0.3": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "klona@^2.0.4": "https://registry.npmjs.org/klona/-/klona-2.0.6.tgz", "language-subtag-registry@~0.3.2": "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.22.tgz", "language-tags@=1.0.5": "https://registry.npmjs.org/language-tags/-/language-tags-1.0.5.tgz", "last-call-webpack-plugin@^3.0.0": "https://registry.npmjs.org/last-call-webpack-plugin/-/last-call-webpack-plugin-3.0.0.tgz", "latest-version@^7.0.0": "https://registry.npmjs.org/latest-version/-/latest-version-7.0.0.tgz", "lerna@^6.6.1": "https://registry.npmjs.org/lerna/-/lerna-6.6.1.tgz", "leven@^3.1.0": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "levn@^0.4.1": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "levn@~0.3.0": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "libnpmaccess@6.0.3": "https://registry.npmjs.org/libnpmaccess/-/libnpmaccess-6.0.3.tgz", "libnpmpublish@6.0.4": "https://registry.npmjs.org/libnpmpublish/-/libnpmpublish-6.0.4.tgz", "lightningcss-darwin-arm64@1.19.0": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.19.0.tgz", "lightningcss-darwin-x64@1.19.0": "https://registry.yarnpkg.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.19.0.tgz#c867308b88859ba61a2c46c82b1ca52ff73a1bd0", "lightningcss-linux-arm-gnueabihf@1.19.0": "https://registry.yarnpkg.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.19.0.tgz#0f921dc45f2e5c3aea70fab98844ac0e5f2f81be", "lightningcss-linux-arm64-gnu@1.19.0": "https://registry.yarnpkg.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.19.0.tgz#027f9df9c7f4ffa127c37a71726245a5794d7ba2", "lightningcss-linux-arm64-musl@1.19.0": "https://registry.yarnpkg.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.19.0.tgz#85ea987da868524eac6db94f8e1eaa23d0b688a3", "lightningcss-linux-x64-gnu@1.19.0": "https://registry.yarnpkg.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.19.0.tgz#02bec89579ab4153dccc0def755d1fd9e3ee7f3c", "lightningcss-linux-x64-musl@1.19.0": "https://registry.yarnpkg.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.19.0.tgz#e36a5df8193ae961d22974635e4c100a1823bb8c", "lightningcss-win32-x64-msvc@1.19.0": "https://registry.yarnpkg.com/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.19.0.tgz#0854dbd153035eca1396e2227c708ad43655a61c", "lightningcss@~1.19.0": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.19.0.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "lines-and-columns@~2.0.3": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-2.0.3.tgz", "load-json-file@6.2.0": "https://registry.npmjs.org/load-json-file/-/load-json-file-6.2.0.tgz", "load-json-file@^4.0.0": "https://registry.npmjs.org/load-json-file/-/load-json-file-4.0.0.tgz", "loader-runner@^2.4.0": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz", "loader-utils@2.0.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.0.tgz", "loader-utils@^1.1.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz", "loader-utils@^1.2.3": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz", "loader-utils@^1.4.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz", "loader-utils@^2.0.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz", "locate-path@^2.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz", "locate-path@^3.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "locate-path@^5.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash-es@^4.17.21": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "lodash-es@^4.2.1": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "lodash._reinterpolate@^3.0.0": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz", "lodash.capitalize@^4.2.1": "https://registry.npmjs.org/lodash.capitalize/-/lodash.capitalize-4.2.1.tgz", "lodash.curry@^4.1.1": "https://registry.npmjs.org/lodash.curry/-/lodash.curry-4.1.1.tgz", "lodash.debounce@4.0.8": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.debounce@^4.0.8": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.escaperegexp@^4.1.2": "https://registry.npmjs.org/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz", "lodash.ismatch@^4.4.0": "https://registry.npmjs.org/lodash.ismatch/-/lodash.ismatch-4.4.0.tgz", "lodash.isplainobject@^4.0.6": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "lodash.isstring@^4.0.1": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "lodash.memoize@4.x": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.memoize@^4.1.2": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.merge@4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.merge@^4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.noop@^3.0.1": "https://registry.npmjs.org/lodash.noop/-/lodash.noop-3.0.1.tgz", "lodash.template@^4.5.0": "https://registry.npmjs.org/lodash.template/-/lodash.template-4.5.0.tgz", "lodash.templatesettings@^4.0.0": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz", "lodash.throttle@^4.1.1": "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz", "lodash.toarray@^4.4.0": "https://registry.npmjs.org/lodash.toarray/-/lodash.toarray-4.4.0.tgz", "lodash.truncate@^4.4.2": "https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz", "lodash.uniq@^4.5.0": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "lodash.uniqby@^4.7.0": "https://registry.npmjs.org/lodash.uniqby/-/lodash.uniqby-4.7.0.tgz", "lodash@*": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@>=3.5 <5": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.11.1": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.14.0": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.11": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.13": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.14": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.15": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.19": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.20": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.4": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.5": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.2.1": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.7.0": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "log-symbols@^2.2.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz", "log-symbols@^4.1.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "log-symbols@^5.1.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-5.1.0.tgz", "logkitty@^0.7.1": "https://registry.npmjs.org/logkitty/-/logkitty-0.7.1.tgz", "loglevel@^1.6.8": "https://registry.npmjs.org/loglevel/-/loglevel-1.8.1.tgz", "loose-envify@^1.0.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.1.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.3.1": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.4.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lottie-react-native@5.1.6": "https://registry.npmjs.org/lottie-react-native/-/lottie-react-native-5.1.6.tgz", "lower-case@^2.0.2": "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz", "lowercase-keys@^3.0.0": "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-3.0.0.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "lru-cache@^6.0.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "lru-cache@^7.14.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "lru-cache@^7.4.4": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "lru-cache@^7.5.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "lru-cache@^7.7.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "macos-release@^3.1.0": "https://registry.npmjs.org/macos-release/-/macos-release-3.2.0.tgz", "magic-string@^0.25.0": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "magic-string@^0.25.7": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "make-dir@3.1.0": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^2.0.0": "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz", "make-dir@^2.1.0": "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz", "make-dir@^3.0.2": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^4.0.0": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "make-error@1.x": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "make-error@^1.1.1": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "make-fetch-happen@^10.0.3": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.2.1.tgz", "make-fetch-happen@^10.0.6": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.2.1.tgz", "make-fetch-happen@^11.0.0": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.3.tgz", "make-fetch-happen@^11.0.1": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.3.tgz", "makeerror@1.0.12": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "map-cache@^0.2.2": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "map-obj@^1.0.0": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "map-obj@^4.0.0": "https://registry.npmjs.org/map-obj/-/map-obj-4.3.0.tgz", "map-visit@^1.0.0": "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz", "md5-file@^3.2.3": "https://registry.npmjs.org/md5-file/-/md5-file-3.2.3.tgz", "md5.js@^1.3.4": "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz", "md5@^2.2.1": "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz", "md5@~2.2.0": "https://registry.npmjs.org/md5/-/md5-2.2.1.tgz", "md5hex@^1.0.0": "https://registry.npmjs.org/md5hex/-/md5hex-1.0.0.tgz", "mdn-data@2.0.14": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz", "mdn-data@2.0.4": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "memoize-one@^5.0.0": "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz", "memoize-one@^6.0.0": "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz", "memory-cache@~0.2.0": "https://registry.npmjs.org/memory-cache/-/memory-cache-0.2.0.tgz", "memory-fs@^0.4.1": "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz", "memory-fs@^0.5.0": "https://registry.npmjs.org/memory-fs/-/memory-fs-0.5.0.tgz", "meow@^8.0.0": "https://registry.npmjs.org/meow/-/meow-8.1.2.tgz", "merge-descriptors@1.0.1": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "merge-options@^3.0.4": "https://registry.npmjs.org/merge-options/-/merge-options-3.0.4.tgz", "merge-stream@^2.0.0": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge@^1.2.0": "https://registry.npmjs.org/merge/-/merge-1.2.1.tgz", "methods@~1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "metro-babel-transformer@0.76.5": "https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.76.5.tgz", "metro-babel-transformer@0.76.7": "https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.76.7.tgz", "metro-cache-key@0.76.5": "https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.76.5.tgz", "metro-cache-key@0.76.7": "https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.76.7.tgz", "metro-cache@0.76.5": "https://registry.npmjs.org/metro-cache/-/metro-cache-0.76.5.tgz", "metro-cache@0.76.7": "https://registry.npmjs.org/metro-cache/-/metro-cache-0.76.7.tgz", "metro-config@0.76.5": "https://registry.npmjs.org/metro-config/-/metro-config-0.76.5.tgz", "metro-config@0.76.7": "https://registry.npmjs.org/metro-config/-/metro-config-0.76.7.tgz", "metro-core@0.76.5": "https://registry.npmjs.org/metro-core/-/metro-core-0.76.5.tgz", "metro-core@0.76.7": "https://registry.npmjs.org/metro-core/-/metro-core-0.76.7.tgz", "metro-file-map@0.76.5": "https://registry.npmjs.org/metro-file-map/-/metro-file-map-0.76.5.tgz", "metro-file-map@0.76.7": "https://registry.npmjs.org/metro-file-map/-/metro-file-map-0.76.7.tgz", "metro-inspector-proxy@0.76.5": "https://registry.npmjs.org/metro-inspector-proxy/-/metro-inspector-proxy-0.76.5.tgz", "metro-inspector-proxy@0.76.7": "https://registry.npmjs.org/metro-inspector-proxy/-/metro-inspector-proxy-0.76.7.tgz", "metro-minify-terser@0.76.5": "https://registry.npmjs.org/metro-minify-terser/-/metro-minify-terser-0.76.5.tgz", "metro-minify-terser@0.76.7": "https://registry.npmjs.org/metro-minify-terser/-/metro-minify-terser-0.76.7.tgz", "metro-minify-uglify@0.76.5": "https://registry.npmjs.org/metro-minify-uglify/-/metro-minify-uglify-0.76.5.tgz", "metro-minify-uglify@0.76.7": "https://registry.npmjs.org/metro-minify-uglify/-/metro-minify-uglify-0.76.7.tgz", "metro-react-native-babel-preset@0.76.5": "https://registry.npmjs.org/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.76.5.tgz", "metro-react-native-babel-preset@0.76.7": "https://registry.npmjs.org/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.76.7.tgz", "metro-react-native-babel-preset@0.76.8": "https://registry.npmjs.org/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.76.8.tgz", "metro-react-native-babel-transformer@0.76.5": "https://registry.npmjs.org/metro-react-native-babel-transformer/-/metro-react-native-babel-transformer-0.76.5.tgz", "metro-react-native-babel-transformer@0.76.7": "https://registry.npmjs.org/metro-react-native-babel-transformer/-/metro-react-native-babel-transformer-0.76.7.tgz", "metro-resolver@0.76.5": "https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.76.5.tgz", "metro-resolver@0.76.7": "https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.76.7.tgz", "metro-runtime@0.76.5": "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.76.5.tgz", "metro-runtime@0.76.7": "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.76.7.tgz", "metro-runtime@0.76.8": "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.76.8.tgz", "metro-source-map@0.76.5": "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.76.5.tgz", "metro-source-map@0.76.7": "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.76.7.tgz", "metro-source-map@0.76.8": "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.76.8.tgz", "metro-symbolicate@0.76.5": "https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.76.5.tgz", "metro-symbolicate@0.76.7": "https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.76.7.tgz", "metro-symbolicate@0.76.8": "https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.76.8.tgz", "metro-transform-plugins@0.76.5": "https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.76.5.tgz", "metro-transform-plugins@0.76.7": "https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.76.7.tgz", "metro-transform-worker@0.76.5": "https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.76.5.tgz", "metro-transform-worker@0.76.7": "https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.76.7.tgz", "metro@0.76.5": "https://registry.npmjs.org/metro/-/metro-0.76.5.tgz", "metro@0.76.7": "https://registry.npmjs.org/metro/-/metro-0.76.7.tgz", "microevent.ts@~0.1.1": "https://registry.npmjs.org/microevent.ts/-/microevent.ts-0.1.1.tgz", "micromatch@^3.1.10": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "micromatch@^3.1.4": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "micromatch@^4.0.2": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "micromatch@^4.0.4": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "micromatch@^4.0.5": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "miller-rabin@^4.0.0": "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-types@2.1.35": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.27": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.17": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.19": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mime@^1.4.1": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mime@^2.4.1": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "mime@^2.4.4": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "mimic-fn@^1.0.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "mimic-fn@^4.0.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz", "mimic-response@^3.1.0": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "mimic-response@^4.0.0": "https://registry.npmjs.org/mimic-response/-/mimic-response-4.0.0.tgz", "min-document@^2.19.0": "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz", "min-indent@^1.0.0": "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz", "mini-css-extract-plugin@0.11.3": "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-0.11.3.tgz", "minimalistic-assert@^1.0.0": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimalistic-assert@^1.0.1": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimalistic-crypto-utils@^1.0.1": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "minimatch@2 || 3": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "minimatch@3.0.5": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.5.tgz", "minimatch@^3.0.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^5.0.1": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^6.1.6": "https://registry.npmjs.org/minimatch/-/minimatch-6.2.0.tgz", "minimatch@^7.4.2": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.5.tgz", "minimatch@^8.0.2": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.3.tgz", "minimist-options@4.1.0": "https://registry.npmjs.org/minimist-options/-/minimist-options-4.1.0.tgz", "minimist@^1.1.1": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.5": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minipass-collect@^1.0.2": "https://registry.npmjs.org/minipass-collect/-/minipass-collect-1.0.2.tgz", "minipass-fetch@^2.0.3": "https://registry.npmjs.org/minipass-fetch/-/minipass-fetch-2.1.2.tgz", "minipass-fetch@^3.0.0": "https://registry.npmjs.org/minipass-fetch/-/minipass-fetch-3.0.1.tgz", "minipass-flush@^1.0.5": "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz", "minipass-json-stream@^1.0.1": "https://registry.npmjs.org/minipass-json-stream/-/minipass-json-stream-1.0.1.tgz", "minipass-pipeline@^1.2.2": "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "minipass-pipeline@^1.2.4": "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "minipass-sized@^1.0.3": "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz", "minipass@3.1.6": "https://registry.npmjs.org/minipass/-/minipass-3.1.6.tgz", "minipass@^3.0.0": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^3.1.1": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^3.1.6": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^4.0.0": "https://registry.npmjs.org/minipass/-/minipass-4.2.5.tgz", "minipass@^4.0.2": "https://registry.npmjs.org/minipass/-/minipass-4.2.5.tgz", "minipass@^4.2.4": "https://registry.npmjs.org/minipass/-/minipass-4.2.5.tgz", "minizlib@^2.1.1": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "minizlib@^2.1.2": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "mississippi@^3.0.0": "https://registry.npmjs.org/mississippi/-/mississippi-3.0.0.tgz", "mixin-deep@^1.2.0": "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz", "mixin-object@^2.0.1": "https://registry.npmjs.org/mixin-object/-/mixin-object-2.0.1.tgz", "mkdirp-infer-owner@^2.0.0": "https://registry.npmjs.org/mkdirp-infer-owner/-/mkdirp-infer-owner-2.0.0.tgz", "mkdirp@^0.5.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^0.5.3": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^0.5.6": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^1.0.3": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "mkdirp@^1.0.4": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "mkdirp@~0.5.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "modal-react-native-web@0.2.0": "https://registry.npmjs.org/modal-react-native-web/-/modal-react-native-web-0.2.0.tgz", "modify-values@^1.0.0": "https://registry.npmjs.org/modify-values/-/modify-values-1.0.1.tgz", "moment@^2.19.1": "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz", "move-concurrently@^1.0.1": "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.2": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.0.0": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "multicast-dns-service-types@^1.1.0": "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "multicast-dns@^6.0.1": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz", "multimatch@5.0.0": "https://registry.npmjs.org/multimatch/-/multimatch-5.0.0.tgz", "mute-stream@0.0.8": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "mute-stream@1.0.0": "https://registry.npmjs.org/mute-stream/-/mute-stream-1.0.0.tgz", "mute-stream@~0.0.4": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "mv@~2": "https://registry.npmjs.org/mv/-/mv-2.1.1.tgz", "mz@^2.7.0": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "nan@^2.12.1": "https://registry.npmjs.org/nan/-/nan-2.17.0.tgz", "nanoclone@^0.2.1": "https://registry.npmjs.org/nanoclone/-/nanoclone-0.2.1.tgz", "nanoid@^3.1.23": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.6.tgz", "nanoid@^3.3.6": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.6.tgz", "nanomatch@^1.2.9": "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz", "native-url@^0.2.6": "https://registry.npmjs.org/native-url/-/native-url-0.2.6.tgz", "natural-compare@^1.4.0": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "ncp@~2.0.0": "https://registry.npmjs.org/ncp/-/ncp-2.0.0.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "negotiator@^0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "neo-async@^2.5.0": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "neo-async@^2.6.0": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "neo-async@^2.6.1": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "neo-async@^2.6.2": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "nested-error-stacks@~2.0.1": "https://registry.npmjs.org/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz", "netmask@^2.0.2": "https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz", "new-github-release-url@2.0.0": "https://registry.npmjs.org/new-github-release-url/-/new-github-release-url-2.0.0.tgz", "next-tick@^1.1.0": "https://registry.npmjs.org/next-tick/-/next-tick-1.1.0.tgz", "nice-try@^1.0.4": "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz", "no-case@^3.0.4": "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz", "nocache@^3.0.1": "https://registry.npmjs.org/nocache/-/nocache-3.0.4.tgz", "node-abort-controller@^3.1.1": "https://registry.npmjs.org/node-abort-controller/-/node-abort-controller-3.1.1.tgz", "node-addon-api@^3.2.1": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.2.1.tgz", "node-dir@^0.1.17": "https://registry.npmjs.org/node-dir/-/node-dir-0.1.17.tgz", "node-domexception@^1.0.0": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "node-emoji@1.10.0": "https://registry.npmjs.org/node-emoji/-/node-emoji-1.10.0.tgz", "node-fetch@2.6.7": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "node-fetch@3.3.1": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.1.tgz", "node-fetch@^1.0.1": "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz", "node-fetch@^2.2.0": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.11.tgz", "node-fetch@^2.6.0": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.9.tgz", "node-fetch@^2.6.1": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.9.tgz", "node-fetch@^2.6.7": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.9.tgz", "node-forge@^0.10.0": "https://registry.npmjs.org/node-forge/-/node-forge-0.10.0.tgz", "node-forge@^0.7.1": "https://registry.npmjs.org/node-forge/-/node-forge-0.7.6.tgz", "node-forge@^1.2.1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-forge@^1.3.1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-gyp-build@^4.3.0": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.6.0.tgz", "node-gyp@^9.0.0": "https://registry.npmjs.org/node-gyp/-/node-gyp-9.3.1.tgz", "node-int64@^0.4.0": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "node-libs-browser@^2.2.1": "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "node-notifier@^8.0.0": "https://registry.npmjs.org/node-notifier/-/node-notifier-8.0.2.tgz", "node-releases@^1.1.61": "https://registry.npmjs.org/node-releases/-/node-releases-1.1.77.tgz", "node-releases@^2.0.13": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.13.tgz", "node-releases@^2.0.8": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.10.tgz", "node-stream-zip@^1.9.1": "https://registry.npmjs.org/node-stream-zip/-/node-stream-zip-1.15.0.tgz", "nopt@^6.0.0": "https://registry.npmjs.org/nopt/-/nopt-6.0.0.tgz", "nopt@^7.0.0": "https://registry.npmjs.org/nopt/-/nopt-7.1.0.tgz", "normalize-package-data@^2.3.2": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "normalize-package-data@^2.5.0": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "normalize-package-data@^3.0.0": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-3.0.3.tgz", "normalize-package-data@^4.0.0": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-4.0.1.tgz", "normalize-package-data@^5.0.0": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-5.0.0.tgz", "normalize-path@^2.1.1": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "normalize-url@1.9.1": "https://registry.npmjs.org/normalize-url/-/normalize-url-1.9.1.tgz", "normalize-url@^3.0.0": "https://registry.npmjs.org/normalize-url/-/normalize-url-3.3.0.tgz", "normalize-url@^8.0.0": "https://registry.npmjs.org/normalize-url/-/normalize-url-8.0.0.tgz", "npm-bundled@^1.1.1": "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.1.2.tgz", "npm-bundled@^1.1.2": "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.1.2.tgz", "npm-bundled@^2.0.0": "https://registry.npmjs.org/npm-bundled/-/npm-bundled-2.0.1.tgz", "npm-bundled@^3.0.0": "https://registry.npmjs.org/npm-bundled/-/npm-bundled-3.0.0.tgz", "npm-install-checks@^5.0.0": "https://registry.npmjs.org/npm-install-checks/-/npm-install-checks-5.0.0.tgz", "npm-install-checks@^6.0.0": "https://registry.npmjs.org/npm-install-checks/-/npm-install-checks-6.1.0.tgz", "npm-normalize-package-bin@^1.0.1": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz", "npm-normalize-package-bin@^2.0.0": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-2.0.0.tgz", "npm-normalize-package-bin@^3.0.0": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.0.tgz", "npm-package-arg@8.1.1": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-8.1.1.tgz", "npm-package-arg@^10.0.0": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-10.1.0.tgz", "npm-package-arg@^10.1.0": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-10.1.0.tgz", "npm-package-arg@^7.0.0": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-7.0.0.tgz", "npm-package-arg@^9.0.0": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-9.1.2.tgz", "npm-package-arg@^9.0.1": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-9.1.2.tgz", "npm-packlist@5.1.1": "https://registry.npmjs.org/npm-packlist/-/npm-packlist-5.1.1.tgz", "npm-packlist@^5.1.0": "https://registry.npmjs.org/npm-packlist/-/npm-packlist-5.1.3.tgz", "npm-packlist@^7.0.0": "https://registry.npmjs.org/npm-packlist/-/npm-packlist-7.0.4.tgz", "npm-pick-manifest@^7.0.0": "https://registry.npmjs.org/npm-pick-manifest/-/npm-pick-manifest-7.0.2.tgz", "npm-pick-manifest@^8.0.0": "https://registry.npmjs.org/npm-pick-manifest/-/npm-pick-manifest-8.0.1.tgz", "npm-pick-manifest@^8.0.1": "https://registry.npmjs.org/npm-pick-manifest/-/npm-pick-manifest-8.0.1.tgz", "npm-registry-fetch@14.0.3": "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-14.0.3.tgz", "npm-registry-fetch@^13.0.0": "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-13.3.1.tgz", "npm-registry-fetch@^13.0.1": "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-13.3.1.tgz", "npm-registry-fetch@^14.0.0": "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-14.0.3.tgz", "npm-registry-fetch@^14.0.3": "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-14.0.3.tgz", "npm-run-path@^2.0.0": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "npm-run-path@^4.0.0": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "npm-run-path@^4.0.1": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "npm-run-path@^5.1.0": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.1.0.tgz", "npmlog@6.0.2": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "npmlog@^6.0.0": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "npmlog@^6.0.2": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "npmlog@^7.0.1": "https://registry.npmjs.org/npmlog/-/npmlog-7.0.1.tgz", "nth-check@^1.0.2": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz", "nth-check@^2.0.1": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz", "nullthrows@^1.1.1": "https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz", "num2fraction@^1.2.2": "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz", "nwsapi@^2.2.0": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.7.tgz", "nwsapi@^2.2.2": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.7.tgz", "nx@15.9.2": "https://registry.npmjs.org/nx/-/nx-15.9.2.tgz", "nx@>=15.5.2 < 16": "https://registry.npmjs.org/nx/-/nx-15.9.2.tgz", "oauth-sign@~0.9.0": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "ob1@0.76.5": "https://registry.npmjs.org/ob1/-/ob1-0.76.5.tgz", "ob1@0.76.7": "https://registry.npmjs.org/ob1/-/ob1-0.76.7.tgz", "ob1@0.76.8": "https://registry.npmjs.org/ob1/-/ob1-0.76.8.tgz", "object-assign@^4.0.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.0": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-copy@^0.1.0": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "object-inspect@^1.12.3": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz", "object-inspect@^1.9.0": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz", "object-is@^1.0.1": "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz", "object-keys@^1.1.1": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "object-visit@^1.0.0": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "object.assign@^4.1.4": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.4.tgz", "object.entries@^1.1.0": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.7.tgz", "object.entries@^1.1.6": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.7.tgz", "object.fromentries@^2.0.6": "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.7.tgz", "object.fromentries@^2.0.7": "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.7.tgz", "object.getownpropertydescriptors@^2.0.3": "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.7.tgz", "object.getownpropertydescriptors@^2.1.0": "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.7.tgz", "object.groupby@^1.0.0": "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.1.tgz", "object.groupby@^1.0.1": "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.1.tgz", "object.hasown@^1.1.2": "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.3.tgz", "object.pick@^1.3.0": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "object.values@^1.1.0": "https://registry.npmjs.org/object.values/-/object.values-1.1.7.tgz", "object.values@^1.1.6": "https://registry.npmjs.org/object.values/-/object.values-1.1.7.tgz", "object.values@^1.1.7": "https://registry.npmjs.org/object.values/-/object.values-1.1.7.tgz", "obuf@^1.0.0": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "obuf@^1.1.2": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "on-finished@~2.3.0": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "on-headers@~1.0.2": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "onetime@^2.0.0": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "onetime@^5.1.0": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "onetime@^5.1.2": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "onetime@^6.0.0": "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz", "open@9.1.0": "https://registry.npmjs.org/open/-/open-9.1.0.tgz", "open@^6.2.0": "https://registry.npmjs.org/open/-/open-6.4.0.tgz", "open@^7.0.2": "https://registry.npmjs.org/open/-/open-7.4.2.tgz", "open@^8.0.4": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "open@^8.3.0": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "open@^8.4.0": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "open@^9.1.0": "https://registry.npmjs.org/open/-/open-9.1.0.tgz", "opencollective-postinstall@^2.0.3": "https://registry.npmjs.org/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz", "opn@^5.5.0": "https://registry.npmjs.org/opn/-/opn-5.5.0.tgz", "optimize-css-assets-webpack-plugin@5.0.4": "https://registry.npmjs.org/optimize-css-assets-webpack-plugin/-/optimize-css-assets-webpack-plugin-5.0.4.tgz", "optionator@^0.8.1": "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz", "optionator@^0.9.1": "https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz", "ora@3.4.0": "https://registry.npmjs.org/ora/-/ora-3.4.0.tgz", "ora@6.3.1": "https://registry.npmjs.org/ora/-/ora-6.3.1.tgz", "ora@^5.4.1": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "os-browserify@^0.3.0": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz", "os-homedir@^1.0.0": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "os-name@5.1.0": "https://registry.npmjs.org/os-name/-/os-name-5.1.0.tgz", "os-tmpdir@^1.0.0": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "os-tmpdir@~1.0.2": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "osenv@^0.1.5": "https://registry.npmjs.org/osenv/-/osenv-0.1.5.tgz", "p-cancelable@^3.0.0": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-3.0.0.tgz", "p-each-series@^2.1.0": "https://registry.npmjs.org/p-each-series/-/p-each-series-2.2.0.tgz", "p-finally@^1.0.0": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "p-limit@^1.1.0": "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz", "p-limit@^2.0.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^2.2.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-limit@^3.1.0": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^2.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz", "p-locate@^3.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz", "p-locate@^4.1.0": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "p-map-series@2.1.0": "https://registry.npmjs.org/p-map-series/-/p-map-series-2.1.0.tgz", "p-map@4.0.0": "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz", "p-map@^2.0.0": "https://registry.npmjs.org/p-map/-/p-map-2.1.0.tgz", "p-map@^4.0.0": "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz", "p-pipe@3.1.0": "https://registry.npmjs.org/p-pipe/-/p-pipe-3.1.0.tgz", "p-queue@6.6.2": "https://registry.npmjs.org/p-queue/-/p-queue-6.6.2.tgz", "p-reduce@2.1.0": "https://registry.npmjs.org/p-reduce/-/p-reduce-2.1.0.tgz", "p-reduce@^2.0.0": "https://registry.npmjs.org/p-reduce/-/p-reduce-2.1.0.tgz", "p-reduce@^2.1.0": "https://registry.npmjs.org/p-reduce/-/p-reduce-2.1.0.tgz", "p-retry@^3.0.1": "https://registry.npmjs.org/p-retry/-/p-retry-3.0.1.tgz", "p-timeout@^3.2.0": "https://registry.npmjs.org/p-timeout/-/p-timeout-3.2.0.tgz", "p-try@^1.0.0": "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz", "p-try@^2.0.0": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "p-waterfall@2.1.1": "https://registry.npmjs.org/p-waterfall/-/p-waterfall-2.1.1.tgz", "pac-proxy-agent@^6.0.3": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.4.tgz", "pac-resolver@^6.0.1": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-6.0.2.tgz", "package-json@^8.1.0": "https://registry.npmjs.org/package-json/-/package-json-8.1.1.tgz", "pacote@13.6.2": "https://registry.npmjs.org/pacote/-/pacote-13.6.2.tgz", "pacote@^13.6.1": "https://registry.npmjs.org/pacote/-/pacote-13.6.2.tgz", "pacote@^15.0.0": "https://registry.npmjs.org/pacote/-/pacote-15.1.1.tgz", "pacote@^15.0.8": "https://registry.npmjs.org/pacote/-/pacote-15.1.1.tgz", "pako@~1.0.5": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "parallel-transform@^1.1.0": "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.2.0.tgz", "param-case@^3.0.3": "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz", "parent-module@^1.0.0": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "parse-asn1@^5.0.0": "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.6.tgz", "parse-asn1@^5.1.5": "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.6.tgz", "parse-conflict-json@^3.0.0": "https://registry.npmjs.org/parse-conflict-json/-/parse-conflict-json-3.0.1.tgz", "parse-json@^4.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz", "parse-json@^5.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse-json@^5.2.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse-path@^7.0.0": "https://registry.npmjs.org/parse-path/-/parse-path-7.0.0.tgz", "parse-png@^2.1.0": "https://registry.npmjs.org/parse-png/-/parse-png-2.1.0.tgz", "parse-url@^8.1.0": "https://registry.npmjs.org/parse-url/-/parse-url-8.1.0.tgz", "parse5@6.0.1": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz", "parse5@^7.0.0": "https://registry.npmjs.org/parse5/-/parse5-7.1.2.tgz", "parse5@^7.1.1": "https://registry.npmjs.org/parse5/-/parse5-7.1.2.tgz", "parse@^1.10.1": "https://registry.npmjs.org/parse/-/parse-1.11.1.tgz", "parseurl@~1.3.2": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "pascal-case@^3.1.2": "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz", "pascalcase@^0.1.1": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "password-prompt@^1.0.4": "https://registry.npmjs.org/password-prompt/-/password-prompt-1.1.2.tgz", "path-browserify@0.0.1": "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz", "path-browserify@^1.0.0": "https://registry.npmjs.org/path-browserify/-/path-browserify-1.0.1.tgz", "path-dirname@^1.0.0": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "path-dirname@^1.0.2": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "path-exists@^3.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-is-inside@^1.0.1": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "path-is-inside@^1.0.2": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "path-key@^2.0.0": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "path-key@^2.0.1": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "path-key@^3.0.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-key@^4.0.0": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz", "path-parse@^1.0.5": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-parse@^1.0.6": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.6.1": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.3.tgz", "path-to-regexp@0.1.7": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "path-type@^3.0.0": "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "pbkdf2@^3.0.3": "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.1.2.tgz", "performance-now@^2.1.0": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "picocolors@^0.2.1": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.2": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.3": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "pify@5.0.0": "https://registry.npmjs.org/pify/-/pify-5.0.0.tgz", "pify@^2.0.0": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "pify@^2.3.0": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "pify@^3.0.0": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "pify@^4.0.1": "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz", "pify@^5.0.0": "https://registry.npmjs.org/pify/-/pify-5.0.0.tgz", "pinkie-promise@^2.0.0": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "pinkie@^2.0.0": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "pirates@^4.0.1": "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz", "pirates@^4.0.4": "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz", "pirates@^4.0.5": "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz", "pkg-dir@^3.0.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz", "pkg-dir@^4.1.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "pkg-dir@^4.2.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "pkg-up@3.1.0": "https://registry.npmjs.org/pkg-up/-/pkg-up-3.1.0.tgz", "pkg-up@^3.1.0": "https://registry.npmjs.org/pkg-up/-/pkg-up-3.1.0.tgz", "plist@^3.0.5": "https://registry.npmjs.org/plist/-/plist-3.0.6.tgz", "plist@^3.1.0": "https://registry.npmjs.org/plist/-/plist-3.1.0.tgz", "pngjs@^3.3.0": "https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz", "pnp-webpack-plugin@1.6.4": "https://registry.npmjs.org/pnp-webpack-plugin/-/pnp-webpack-plugin-1.6.4.tgz", "portfinder@^1.0.26": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.32.tgz", "posix-character-classes@^0.1.0": "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "postcss-attribute-case-insensitive@^4.0.1": "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-4.0.2.tgz", "postcss-browser-comments@^3.0.0": "https://registry.npmjs.org/postcss-browser-comments/-/postcss-browser-comments-3.0.0.tgz", "postcss-calc@^7.0.1": "https://registry.npmjs.org/postcss-calc/-/postcss-calc-7.0.5.tgz", "postcss-color-functional-notation@^2.0.1": "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-2.0.1.tgz", "postcss-color-gray@^5.0.0": "https://registry.npmjs.org/postcss-color-gray/-/postcss-color-gray-5.0.0.tgz", "postcss-color-hex-alpha@^5.0.3": "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-5.0.3.tgz", "postcss-color-mod-function@^3.0.3": "https://registry.npmjs.org/postcss-color-mod-function/-/postcss-color-mod-function-3.0.3.tgz", "postcss-color-rebeccapurple@^4.0.1": "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-4.0.1.tgz", "postcss-colormin@^4.0.3": "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-4.0.3.tgz", "postcss-convert-values@^4.0.1": "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz", "postcss-custom-media@^7.0.8": "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-7.0.8.tgz", "postcss-custom-properties@^8.0.11": "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-8.0.11.tgz", "postcss-custom-selectors@^5.1.2": "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-5.1.2.tgz", "postcss-dir-pseudo-class@^5.0.0": "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-5.0.0.tgz", "postcss-discard-comments@^4.0.2": "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz", "postcss-discard-duplicates@^4.0.2": "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz", "postcss-discard-empty@^4.0.1": "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz", "postcss-discard-overridden@^4.0.1": "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz", "postcss-double-position-gradients@^1.0.0": "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-1.0.0.tgz", "postcss-env-function@^2.0.2": "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-2.0.2.tgz", "postcss-flexbugs-fixes@4.2.1": "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-4.2.1.tgz", "postcss-focus-visible@^4.0.0": "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-4.0.0.tgz", "postcss-focus-within@^3.0.0": "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-3.0.0.tgz", "postcss-font-variant@^4.0.0": "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-4.0.1.tgz", "postcss-gap-properties@^2.0.0": "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-2.0.0.tgz", "postcss-image-set-function@^3.0.1": "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-3.0.1.tgz", "postcss-initial@^3.0.0": "https://registry.npmjs.org/postcss-initial/-/postcss-initial-3.0.4.tgz", "postcss-lab-function@^2.0.1": "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-2.0.1.tgz", "postcss-load-config@^2.0.0": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.2.tgz", "postcss-loader@3.0.0": "https://registry.npmjs.org/postcss-loader/-/postcss-loader-3.0.0.tgz", "postcss-logical@^3.0.0": "https://registry.npmjs.org/postcss-logical/-/postcss-logical-3.0.0.tgz", "postcss-media-minmax@^4.0.0": "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-4.0.0.tgz", "postcss-merge-longhand@^4.0.11": "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz", "postcss-merge-rules@^4.0.3": "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz", "postcss-minify-font-values@^4.0.2": "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz", "postcss-minify-gradients@^4.0.2": "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz", "postcss-minify-params@^4.0.2": "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz", "postcss-minify-selectors@^4.0.2": "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz", "postcss-modules-extract-imports@^2.0.0": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz", "postcss-modules-local-by-default@^3.0.3": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.3.tgz", "postcss-modules-scope@^2.2.0": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz", "postcss-modules-values@^3.0.0": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-3.0.0.tgz", "postcss-nesting@^7.0.0": "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-7.0.1.tgz", "postcss-normalize-charset@^4.0.1": "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz", "postcss-normalize-display-values@^4.0.2": "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz", "postcss-normalize-positions@^4.0.2": "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz", "postcss-normalize-repeat-style@^4.0.2": "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz", "postcss-normalize-string@^4.0.2": "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz", "postcss-normalize-timing-functions@^4.0.2": "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz", "postcss-normalize-unicode@^4.0.1": "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz", "postcss-normalize-url@^4.0.1": "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz", "postcss-normalize-whitespace@^4.0.2": "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz", "postcss-normalize@8.0.1": "https://registry.npmjs.org/postcss-normalize/-/postcss-normalize-8.0.1.tgz", "postcss-ordered-values@^4.1.2": "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz", "postcss-overflow-shorthand@^2.0.0": "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-2.0.0.tgz", "postcss-page-break@^2.0.0": "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-2.0.0.tgz", "postcss-place@^4.0.1": "https://registry.npmjs.org/postcss-place/-/postcss-place-4.0.1.tgz", "postcss-preset-env@6.7.0": "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-6.7.0.tgz", "postcss-pseudo-class-any-link@^6.0.0": "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-6.0.0.tgz", "postcss-reduce-initial@^4.0.3": "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz", "postcss-reduce-transforms@^4.0.2": "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz", "postcss-replace-overflow-wrap@^3.0.0": "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-3.0.0.tgz", "postcss-safe-parser@5.0.2": "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-5.0.2.tgz", "postcss-selector-matches@^4.0.0": "https://registry.npmjs.org/postcss-selector-matches/-/postcss-selector-matches-4.0.0.tgz", "postcss-selector-not@^4.0.0": "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-4.0.1.tgz", "postcss-selector-parser@^3.0.0": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "postcss-selector-parser@^5.0.0-rc.3": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0.tgz", "postcss-selector-parser@^5.0.0-rc.4": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0.tgz", "postcss-selector-parser@^6.0.0": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz", "postcss-selector-parser@^6.0.10": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz", "postcss-selector-parser@^6.0.2": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz", "postcss-svgo@^4.0.3": "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-4.0.3.tgz", "postcss-unique-selectors@^4.0.1": "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz", "postcss-value-parser@^3.0.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "postcss-value-parser@^4.0.2": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-values-parser@^2.0.0": "https://registry.npmjs.org/postcss-values-parser/-/postcss-values-parser-2.0.1.tgz", "postcss-values-parser@^2.0.1": "https://registry.npmjs.org/postcss-values-parser/-/postcss-values-parser-2.0.1.tgz", "postcss@7.0.36": "https://registry.npmjs.org/postcss/-/postcss-7.0.36.tgz", "postcss@^7": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.0": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.1": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.14": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.17": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.2": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.26": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.27": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.32": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.5": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.6": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^8.1.0": "https://registry.npmjs.org/postcss/-/postcss-8.4.29.tgz", "postcss@~8.4.21": "https://registry.npmjs.org/postcss/-/postcss-8.4.24.tgz", "prelude-ls@^1.2.1": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "prelude-ls@~1.1.2": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "prepend-http@^1.0.0": "https://registry.npmjs.org/prepend-http/-/prepend-http-1.0.4.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "pretty-bytes@5.6.0": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz", "pretty-bytes@^5.3.0": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz", "pretty-error@^2.1.1": "https://registry.npmjs.org/pretty-error/-/pretty-error-2.1.2.tgz", "pretty-format@29.4.3": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.4.3.tgz", "pretty-format@^26.5.2": "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz", "pretty-format@^26.6.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz", "pretty-format@^26.6.2": "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz", "pretty-format@^29.0.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "pretty-format@^29.5.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.5.0.tgz", "pretty-format@^29.7.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "proc-log@^2.0.0": "https://registry.npmjs.org/proc-log/-/proc-log-2.0.1.tgz", "proc-log@^2.0.1": "https://registry.npmjs.org/proc-log/-/proc-log-2.0.1.tgz", "proc-log@^3.0.0": "https://registry.npmjs.org/proc-log/-/proc-log-3.0.0.tgz", "process-nextick-args@~2.0.0": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "process@^0.11.10": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "progress@2.0.3": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "progress@^2.0.0": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "promise-all-reject-late@^1.0.0": "https://registry.npmjs.org/promise-all-reject-late/-/promise-all-reject-late-1.0.1.tgz", "promise-call-limit@^1.0.1": "https://registry.npmjs.org/promise-call-limit/-/promise-call-limit-1.0.2.tgz", "promise-inflight@^1.0.1": "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz", "promise-retry@^2.0.1": "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz", "promise.allsettled@1.0.6": "https://registry.npmjs.org/promise.allsettled/-/promise.allsettled-1.0.6.tgz", "promise@^7.1.1": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "promise@^8.1.0": "https://registry.npmjs.org/promise/-/promise-8.3.0.tgz", "promise@^8.3.0": "https://registry.npmjs.org/promise/-/promise-8.3.0.tgz", "prompts@2.4.0": "https://registry.npmjs.org/prompts/-/prompts-2.4.0.tgz", "prompts@^2.0.1": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "prompts@^2.2.1": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "prompts@^2.3.2": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "prompts@^2.4.0": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "promzard@^0.3.0": "https://registry.npmjs.org/promzard/-/promzard-0.3.0.tgz", "prop-types@*": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@15.7.2": "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz", "prop-types@15.8.1": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.5.10": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.5.7": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.5.8": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.6.1": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.6.2": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.7.2": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.8.1": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "property-expr@^2.0.4": "https://registry.npmjs.org/property-expr/-/property-expr-2.0.5.tgz", "proto-list@~1.2.1": "https://registry.npmjs.org/proto-list/-/proto-list-1.2.4.tgz", "protocols@^2.0.0": "https://registry.npmjs.org/protocols/-/protocols-2.0.1.tgz", "protocols@^2.0.1": "https://registry.npmjs.org/protocols/-/protocols-2.0.1.tgz", "proxy-addr@~2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-agent@6.2.1": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.2.1.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "prr@~1.0.1": "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz", "psl@^1.1.28": "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz", "psl@^1.1.33": "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz", "public-encrypt@^4.0.0": "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz", "pump@^2.0.0": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "pump@^3.0.0": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "pumpify@^1.3.3": "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz", "punycode@^1.2.4": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "punycode@^1.4.1": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "punycode@^2.1.0": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "punycode@^2.1.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "pupa@^3.1.0": "https://registry.npmjs.org/pupa/-/pupa-3.1.0.tgz", "pure-rand@^6.0.0": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.4.tgz", "q@^1.1.2": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "q@^1.5.1": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "qrcode-terminal@0.11.0": "https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.11.0.tgz", "qs@6.11.0": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "qs@^6.10.1": "https://registry.npmjs.org/qs/-/qs-6.11.2.tgz", "qs@^6.11.0": "https://registry.npmjs.org/qs/-/qs-6.11.2.tgz", "qs@~6.5.2": "https://registry.npmjs.org/qs/-/qs-6.5.3.tgz", "query-string@^4.1.0": "https://registry.npmjs.org/query-string/-/query-string-4.3.4.tgz", "query-string@^6.13.5": "https://registry.npmjs.org/query-string/-/query-string-6.14.1.tgz", "query-string@^7.1.3": "https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz", "querystring-es3@^0.2.0": "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz", "querystring@^0.2.0": "https://registry.npmjs.org/querystring/-/querystring-0.2.1.tgz", "querystringify@^2.1.1": "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "queue@6.0.2": "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz", "quick-lru@^4.0.1": "https://registry.npmjs.org/quick-lru/-/quick-lru-4.0.1.tgz", "quick-lru@^5.1.1": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz", "raf@^3.4.1": "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz", "randombytes@^2.0.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.0.1": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.0.5": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.1.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "randomfill@^1.0.3": "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz", "range-parser@^1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.5.1": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "raw-body@2.5.2": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "rc@1.2.8": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "rc@~1.2.7": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "react-addons-shallow-compare@15.6.2": "https://registry.npmjs.org/react-addons-shallow-compare/-/react-addons-shallow-compare-15.6.2.tgz", "react-app-polyfill@^2.0.0": "https://registry.npmjs.org/react-app-polyfill/-/react-app-polyfill-2.0.0.tgz", "react-async-hook@3.6.1": "https://registry.npmjs.org/react-async-hook/-/react-async-hook-3.6.1.tgz", "react-base16-styling@^0.8.0": "https://registry.npmjs.org/react-base16-styling/-/react-base16-styling-0.8.2.tgz", "react-dev-utils@^11.0.3": "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-11.0.4.tgz", "react-devtools-core@^4.27.2": "https://registry.npmjs.org/react-devtools-core/-/react-devtools-core-4.27.8.tgz", "react-dom@18.2.0": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "react-error-boundary@^3.1.0": "https://registry.npmjs.org/react-error-boundary/-/react-error-boundary-3.1.4.tgz", "react-error-overlay@^6.0.9": "https://registry.npmjs.org/react-error-overlay/-/react-error-overlay-6.0.11.tgz", "react-freeze@^1.0.0": "https://registry.npmjs.org/react-freeze/-/react-freeze-1.0.3.tgz", "react-hook-form@^7.46.1": "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.46.1.tgz", "react-i18next@^12.3.1": "https://registry.npmjs.org/react-i18next/-/react-i18next-12.3.1.tgz", "react-is@^16.12.0 || ^17.0.0 || ^18.0.0": "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz", "react-is@^16.13.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.13.1": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.6.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.7.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.8.1": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^17.0.1": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "react-is@^18.0.0": "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz", "react-is@^18.2.0": "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz", "react-lifecycles-compat@^3.0.0": "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "react-mixin@^3.0.5": "https://registry.npmjs.org/react-mixin/-/react-mixin-3.1.1.tgz", "react-native-animatable@1.3.3": "https://registry.npmjs.org/react-native-animatable/-/react-native-animatable-1.3.3.tgz", "react-native-animated-pulse@^1.0.1": "https://registry.npmjs.org/react-native-animated-pulse/-/react-native-animated-pulse-1.0.1.tgz", "react-native-auto-height-image@^3.2.4": "https://registry.npmjs.org/react-native-auto-height-image/-/react-native-auto-height-image-3.2.4.tgz", "react-native-autoheight-webview@^1.6.4": "https://registry.npmjs.org/react-native-autoheight-webview/-/react-native-autoheight-webview-1.6.5.tgz", "react-native-base64@^0.2.1": "https://registry.npmjs.org/react-native-base64/-/react-native-base64-0.2.1.tgz", "react-native-checkbox-field@git+https://github.com/Appmaker-xyz/react-native-checkbox-field.git": "git+ssh://**************/Appmaker-xyz/react-native-checkbox-field.git#070d48de0bac86b07f9f5d70619ddbc90895613a", "react-native-circular-progress@1.4.0": "https://registry.npmjs.org/react-native-circular-progress/-/react-native-circular-progress-1.4.0.tgz", "react-native-collapsible@^1.6.2": "https://registry.npmjs.org/react-native-collapsible/-/react-native-collapsible-1.6.2.tgz", "react-native-countdown-component@^2.7.1": "https://registry.npmjs.org/react-native-countdown-component/-/react-native-countdown-component-2.7.1.tgz", "react-native-country-picker-modal@^2.0.0": "https://registry.npmjs.org/react-native-country-picker-modal/-/react-native-country-picker-modal-2.0.0.tgz", "react-native-debug-stylesheet@^0.1.1": "https://registry.npmjs.org/react-native-debug-stylesheet/-/react-native-debug-stylesheet-0.1.1.tgz", "react-native-default-preference@^1.4.4": "https://registry.npmjs.org/react-native-default-preference/-/react-native-default-preference-1.4.4.tgz", "react-native-deprecated-custom-components@^0.1.0": "https://registry.npmjs.org/react-native-deprecated-custom-components/-/react-native-deprecated-custom-components-0.1.2.tgz", "react-native-drawer@^2.5.0": "https://registry.npmjs.org/react-native-drawer/-/react-native-drawer-2.5.1.tgz", "react-native-dropdown-select-list@^2.0.2": "https://registry.npmjs.org/react-native-dropdown-select-list/-/react-native-dropdown-select-list-2.0.5.tgz", "react-native-element-dropdown@^1.8.12": "https://registry.npmjs.org/react-native-element-dropdown/-/react-native-element-dropdown-1.8.13.tgz", "react-native-event-listeners@^1.0.7": "https://registry.npmjs.org/react-native-event-listeners/-/react-native-event-listeners-1.0.7.tgz", "react-native-exception-handler@^2.9.0": "https://registry.npmjs.org/react-native-exception-handler/-/react-native-exception-handler-2.10.10.tgz", "react-native-fast-image@^8.6.3": "https://registry.npmjs.org/react-native-fast-image/-/react-native-fast-image-8.6.3.tgz", "react-native-fbsdk-next@^12.1.0": "https://registry.npmjs.org/react-native-fbsdk-next/-/react-native-fbsdk-next-12.1.0.tgz", "react-native-gesture-handler@2.12.1": "https://registry.npmjs.org/react-native-gesture-handler/-/react-native-gesture-handler-2.12.1.tgz", "react-native-i18n@2.0.14": "https://registry.npmjs.org/react-native-i18n/-/react-native-i18n-2.0.14.tgz", "react-native-image-pan-zoom@^2.1.12": "https://registry.npmjs.org/react-native-image-pan-zoom/-/react-native-image-pan-zoom-2.1.12.tgz", "react-native-image-zoom-viewer@^3.0.1": "https://registry.npmjs.org/react-native-image-zoom-viewer/-/react-native-image-zoom-viewer-3.0.1.tgz", "react-native-in-app-review@^4.3.3": "https://registry.npmjs.org/react-native-in-app-review/-/react-native-in-app-review-4.3.3.tgz", "react-native-iphone-x-helper@^1.3.1": "https://registry.npmjs.org/react-native-iphone-x-helper/-/react-native-iphone-x-helper-1.3.1.tgz", "react-native-json-tree@^1.3.0": "https://registry.npmjs.org/react-native-json-tree/-/react-native-json-tree-1.5.0.tgz", "react-native-linear-gradient@^2.6.2": "https://registry.npmjs.org/react-native-linear-gradient/-/react-native-linear-gradient-2.6.2.tgz", "react-native-localize@^2.2.6": "https://registry.npmjs.org/react-native-localize/-/react-native-localize-2.2.6.tgz", "react-native-material-ripple@^0.9.1": "https://registry.npmjs.org/react-native-material-ripple/-/react-native-material-ripple-0.9.1.tgz", "react-native-message-bar@^1.6.0": "https://registry.npmjs.org/react-native-message-bar/-/react-native-message-bar-1.6.0.tgz", "react-native-mmkv@2.12.2": "https://registry.npmjs.org/react-native-mmkv/-/react-native-mmkv-2.12.2.tgz", "react-native-modal-datetime-picker@^15.0.1": "https://registry.npmjs.org/react-native-modal-datetime-picker/-/react-native-modal-datetime-picker-15.0.1.tgz", "react-native-modal@^11.5.6": "https://registry.npmjs.org/react-native-modal/-/react-native-modal-11.10.0.tgz", "react-native-modal@^13.0.1": "https://registry.npmjs.org/react-native-modal/-/react-native-modal-13.0.1.tgz", "react-native-modalbox@^1.7.1": "https://registry.npmjs.org/react-native-modalbox/-/react-native-modalbox-1.7.1.tgz", "react-native-otp-verify@^1.1.6": "https://registry.npmjs.org/react-native-otp-verify/-/react-native-otp-verify-1.1.6.tgz", "react-native-pager-view@6.2.0": "https://registry.npmjs.org/react-native-pager-view/-/react-native-pager-view-6.2.0.tgz", "react-native-permissions@^4.1.1": "https://registry.npmjs.org/react-native-permissions/-/react-native-permissions-4.1.1.tgz", "react-native-phone-number-input@^2.1.0": "https://registry.npmjs.org/react-native-phone-number-input/-/react-native-phone-number-input-2.1.0.tgz", "react-native-raw-bottom-sheet@^2.2.0": "https://registry.npmjs.org/react-native-raw-bottom-sheet/-/react-native-raw-bottom-sheet-2.2.0.tgz", "react-native-reanimated-carousel@^3.5.1": "https://registry.npmjs.org/react-native-reanimated-carousel/-/react-native-reanimated-carousel-3.5.1.tgz", "react-native-reanimated@~3.3.0": "https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-3.3.0.tgz", "react-native-render-html@^5.1.0": "https://registry.npmjs.org/react-native-render-html/-/react-native-render-html-5.1.1.tgz", "react-native-responsive-fontsize@^0.5.1": "https://registry.npmjs.org/react-native-responsive-fontsize/-/react-native-responsive-fontsize-0.5.1.tgz", "react-native-responsive-screen@^1.4.2": "https://registry.npmjs.org/react-native-responsive-screen/-/react-native-responsive-screen-1.4.2.tgz", "react-native-restart@^0.0.27": "https://registry.npmjs.org/react-native-restart/-/react-native-restart-0.0.27.tgz", "react-native-safe-area-context@4.6.3": "https://registry.npmjs.org/react-native-safe-area-context/-/react-native-safe-area-context-4.6.3.tgz", "react-native-safe-modules@^1.0.3": "https://registry.npmjs.org/react-native-safe-modules/-/react-native-safe-modules-1.0.3.tgz", "react-native-screens@~3.22.0": "https://registry.npmjs.org/react-native-screens/-/react-native-screens-3.22.0.tgz", "react-native-select-input-ios@^2.0.5": "https://registry.npmjs.org/react-native-select-input-ios/-/react-native-select-input-ios-2.0.5.tgz", "react-native-sha256@^1.4.7": "https://registry.npmjs.org/react-native-sha256/-/react-native-sha256-1.4.9.tgz", "react-native-shimmer-placeholder@^2.0.9": "https://registry.npmjs.org/react-native-shimmer-placeholder/-/react-native-shimmer-placeholder-2.0.9.tgz", "react-native-simple-radio-button@^2.7.4": "https://registry.npmjs.org/react-native-simple-radio-button/-/react-native-simple-radio-button-2.7.4.tgz", "react-native-simple-store@^2.0.2": "https://registry.npmjs.org/react-native-simple-store/-/react-native-simple-store-2.0.2.tgz", "react-native-skeleton-placeholder@^5.2.4": "https://registry.npmjs.org/react-native-skeleton-placeholder/-/react-native-skeleton-placeholder-5.2.4.tgz", "react-native-snackbar@^2.6.2": "https://registry.npmjs.org/react-native-snackbar/-/react-native-snackbar-2.6.2.tgz", "react-native-snap-carousel@3.9.1": "https://registry.npmjs.org/react-native-snap-carousel/-/react-native-snap-carousel-3.9.1.tgz", "react-native-snapmint3@^1.0.49": "https://registry.npmjs.org/react-native-snapmint3/-/react-native-snapmint3-1.0.49.tgz", "react-native-svg-transformer@^1.0.0": "https://registry.npmjs.org/react-native-svg-transformer/-/react-native-svg-transformer-1.0.0.tgz", "react-native-svg@13.9.0": "https://registry.npmjs.org/react-native-svg/-/react-native-svg-13.9.0.tgz", "react-native-swiper-flatlist@^3.0.16": "https://registry.npmjs.org/react-native-swiper-flatlist/-/react-native-swiper-flatlist-3.2.3.tgz", "react-native-swiper@^1.6.0": "https://registry.npmjs.org/react-native-swiper/-/react-native-swiper-1.6.0.tgz", "react-native-tab-view@^3.5.2": "https://registry.npmjs.org/react-native-tab-view/-/react-native-tab-view-3.5.2.tgz", "react-native-tracking-transparency@^0.1.1": "https://registry.npmjs.org/react-native-tracking-transparency/-/react-native-tracking-transparency-0.1.2.tgz", "react-native-unistyles@^1.0.0-rc.1": "https://registry.npmjs.org/react-native-unistyles/-/react-native-unistyles-1.2.0.tgz", "react-native-uuid@^2.0.2": "https://registry.npmjs.org/react-native-uuid/-/react-native-uuid-2.0.3.tgz", "react-native-vector-icons@^6.6.0": "https://registry.npmjs.org/react-native-vector-icons/-/react-native-vector-icons-6.7.0.tgz", "react-native-video@^5.2.1": "https://registry.npmjs.org/react-native-video/-/react-native-video-5.2.1.tgz", "react-native-web@~0.19.6": "https://registry.npmjs.org/react-native-web/-/react-native-web-0.19.6.tgz", "react-native-webview@13.2.2": "https://registry.npmjs.org/react-native-webview/-/react-native-webview-13.2.2.tgz", "react-native@0.72.1": "https://registry.npmjs.org/react-native/-/react-native-0.72.1.tgz", "react-native@>=0.60.0": "https://registry.npmjs.org/react-native/-/react-native-0.72.4.tgz", "react-query-native-devtools@^4.0.0": "https://registry.npmjs.org/react-query-native-devtools/-/react-query-native-devtools-4.0.0.tgz", "react-redux@^5.0.4": "https://registry.npmjs.org/react-redux/-/react-redux-5.1.2.tgz", "react-refresh@^0.4.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.4.3.tgz", "react-refresh@^0.8.3": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.8.3.tgz", "react-scripts@^4.0.3": "https://registry.npmjs.org/react-scripts/-/react-scripts-4.0.3.tgz", "react-shallow-renderer@^16.15.0": "https://registry.npmjs.org/react-shallow-renderer/-/react-shallow-renderer-16.15.0.tgz", "react-test-renderer@18.2.0": "https://registry.npmjs.org/react-test-renderer/-/react-test-renderer-18.2.0.tgz", "react-timer-mixin@^0.13.2": "https://registry.npmjs.org/react-timer-mixin/-/react-timer-mixin-0.13.4.tgz", "react-timer-mixin@^0.13.3": "https://registry.npmjs.org/react-timer-mixin/-/react-timer-mixin-0.13.4.tgz", "react-toastify@^8.0.3": "https://registry.npmjs.org/react-toastify/-/react-toastify-8.2.0.tgz", "react@18.2.0": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "react@>=16.9.0": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "read-cmd-shim@3.0.0": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-3.0.0.tgz", "read-cmd-shim@^4.0.0": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-4.0.0.tgz", "read-package-json-fast@^2.0.3": "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-2.0.3.tgz", "read-package-json-fast@^3.0.0": "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-3.0.2.tgz", "read-package-json-fast@^3.0.2": "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-3.0.2.tgz", "read-package-json@5.0.1": "https://registry.npmjs.org/read-package-json/-/read-package-json-5.0.1.tgz", "read-package-json@^5.0.0": "https://registry.npmjs.org/read-package-json/-/read-package-json-5.0.2.tgz", "read-package-json@^6.0.0": "https://registry.npmjs.org/read-package-json/-/read-package-json-6.0.1.tgz", "read-pkg-up@^3.0.0": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-3.0.0.tgz", "read-pkg-up@^7.0.1": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "read-pkg@^3.0.0": "https://registry.npmjs.org/read-pkg/-/read-pkg-3.0.0.tgz", "read-pkg@^5.2.0": "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz", "read@1": "https://registry.npmjs.org/read/-/read-1.0.7.tgz", "read@^1.0.7": "https://registry.npmjs.org/read/-/read-1.0.7.tgz", "readable-stream@1 || 2": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@3": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^2.0.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.0.1": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.0.2": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.1.5": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.2.2": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.3.3": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^2.3.6": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^3.0.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.0.2": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.0.6": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.1.1": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.4.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.6.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^4.1.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.3.0.tgz", "readable-stream@~2.3.6": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readdirp@^2.2.1": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "readline@^1.3.0": "https://registry.npmjs.org/readline/-/readline-1.3.0.tgz", "reanimated-bottom-sheet@^1.0.0-alpha.20": "https://registry.npmjs.org/reanimated-bottom-sheet/-/reanimated-bottom-sheet-1.0.0-alpha.22.tgz", "rebound@^0.0.13": "https://registry.npmjs.org/rebound/-/rebound-0.0.13.tgz", "recast@^0.21.0": "https://registry.npmjs.org/recast/-/recast-0.21.5.tgz", "rechoir@^0.6.2": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "recursive-readdir@2.2.2": "https://registry.npmjs.org/recursive-readdir/-/recursive-readdir-2.2.2.tgz", "recyclerlistview@4.2.0": "https://registry.npmjs.org/recyclerlistview/-/recyclerlistview-4.2.0.tgz", "recyclerlistview@^4.1.3": "https://registry.npmjs.org/recyclerlistview/-/recyclerlistview-4.2.0.tgz", "redent@^3.0.0": "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz", "redux-logger@^2.10.2": "https://registry.npmjs.org/redux-logger/-/redux-logger-2.10.2.tgz", "redux-persist@^3.5.0": "https://registry.npmjs.org/redux-persist/-/redux-persist-3.5.0.tgz", "redux-thunk@^2.2.0": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.2.tgz", "redux@^3.7.2": "https://registry.npmjs.org/redux/-/redux-3.7.2.tgz", "reflect.getprototypeof@^1.0.3": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.4.tgz", "regenerate-unicode-properties@^10.1.0": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "regenerate@^1.4.2": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz", "regenerator-runtime@^0.11.0": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "regenerator-runtime@^0.13.11": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-runtime@^0.13.2": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-runtime@^0.13.7": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-runtime@^0.14.0": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz", "regenerator-transform@^0.15.1": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.1.tgz", "regenerator-transform@^0.15.2": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.2.tgz", "regex-not@^1.0.0": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "regex-not@^1.0.2": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "regex-parser@^2.2.11": "https://registry.npmjs.org/regex-parser/-/regex-parser-2.2.11.tgz", "regexp.prototype.flags@^1.2.0": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz", "regexp.prototype.flags@^1.5.0": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz", "regexpp@^3.0.0": "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz", "regexpp@^3.1.0": "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz", "regexpu-core@^5.3.1": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.2.tgz", "registry-auth-token@^5.0.1": "https://registry.npmjs.org/registry-auth-token/-/registry-auth-token-5.0.2.tgz", "registry-url@^6.0.0": "https://registry.npmjs.org/registry-url/-/registry-url-6.0.1.tgz", "regjsparser@^0.9.1": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.1.tgz", "relateurl@^0.2.7": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz", "release-it@^15.11.0": "https://registry.npmjs.org/release-it/-/release-it-15.11.0.tgz", "remove-trailing-separator@^1.0.1": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "remove-trailing-slash@^0.1.0": "https://registry.npmjs.org/remove-trailing-slash/-/remove-trailing-slash-0.1.1.tgz", "renderkid@^2.0.4": "https://registry.npmjs.org/renderkid/-/renderkid-2.0.7.tgz", "repeat-element@^1.1.2": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.4.tgz", "repeat-string@^1.6.1": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "request@^2.72.0": "https://registry.npmjs.org/request/-/request-2.88.2.tgz", "request@^2.74.0": "https://registry.npmjs.org/request/-/request-2.88.2.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "require-main-filename@^2.0.0": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz", "requireg@^0.2.2": "https://registry.npmjs.org/requireg/-/requireg-0.2.2.tgz", "requires-port@^1.0.0": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "reselect@^2.5.4": "https://registry.npmjs.org/reselect/-/reselect-2.5.4.tgz", "reselect@^4.1.7": "https://registry.npmjs.org/reselect/-/reselect-4.1.8.tgz", "resolve-alpn@^1.2.0": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "resolve-cwd@^2.0.0": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "resolve-cwd@^3.0.0": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "resolve-from@5.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-from@^3.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-url-loader@^3.1.2": "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-3.1.5.tgz", "resolve-url@^0.2.1": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "resolve.exports@^2.0.0": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.2.tgz", "resolve@1.18.1": "https://registry.npmjs.org/resolve/-/resolve-1.18.1.tgz", "resolve@^1.1.6": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.10.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz", "resolve@^1.10.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz", "resolve@^1.12.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.14.2": "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz", "resolve@^1.17.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.18.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.19.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.20.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz", "resolve@^1.22.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz", "resolve@^1.22.4": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^1.3.2": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "resolve@^2.0.0-next.4": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.4.tgz", "resolve@~1.7.1": "https://registry.npmjs.org/resolve/-/resolve-1.7.1.tgz", "responselike@^3.0.0": "https://registry.npmjs.org/responselike/-/responselike-3.0.0.tgz", "restore-cursor@^2.0.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "restore-cursor@^3.1.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz", "restore-cursor@^4.0.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-4.0.0.tgz", "ret@~0.1.10": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "retry@0.13.1": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "retry@^0.12.0": "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "rework-visit@1.0.0": "https://registry.npmjs.org/rework-visit/-/rework-visit-1.0.0.tgz", "rework@1.0.1": "https://registry.npmjs.org/rework/-/rework-1.0.1.tgz", "rgb-regex@^1.0.1": "https://registry.npmjs.org/rgb-regex/-/rgb-regex-1.0.1.tgz", "rgba-regex@^1.0.0": "https://registry.npmjs.org/rgba-regex/-/rgba-regex-1.0.0.tgz", "rimraf@^2.2.8": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^2.5.4": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^2.6.2": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^2.6.3": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^3.0.0": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^4.4.1": "https://registry.npmjs.org/rimraf/-/rimraf-4.4.1.tgz", "rimraf@~2.4.0": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.5.tgz", "rimraf@~2.6.2": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz", "ripemd160@^2.0.0": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz", "ripemd160@^2.0.1": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz", "rollup-plugin-babel@^4.3.3": "https://registry.npmjs.org/rollup-plugin-babel/-/rollup-plugin-babel-4.4.0.tgz", "rollup-plugin-local-resolve@^1.0.7": "https://registry.npmjs.org/rollup-plugin-local-resolve/-/rollup-plugin-local-resolve-1.0.7.tgz", "rollup-plugin-terser@^5.3.1": "https://registry.npmjs.org/rollup-plugin-terser/-/rollup-plugin-terser-5.3.1.tgz", "rollup-pluginutils@^2.8.1": "https://registry.npmjs.org/rollup-pluginutils/-/rollup-pluginutils-2.8.2.tgz", "rollup-pluginutils@^2.8.2": "https://registry.npmjs.org/rollup-pluginutils/-/rollup-pluginutils-2.8.2.tgz", "rollup@^1.31.1": "https://registry.npmjs.org/rollup/-/rollup-1.32.1.tgz", "rsvp@^4.8.4": "https://registry.npmjs.org/rsvp/-/rsvp-4.8.5.tgz", "run-applescript@^5.0.0": "https://registry.npmjs.org/run-applescript/-/run-applescript-5.0.0.tgz", "run-async@^2.4.0": "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz", "run-async@^3.0.0": "https://registry.npmjs.org/run-async/-/run-async-3.0.0.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "run-queue@^1.0.0": "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz", "run-queue@^1.0.3": "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz", "rxjs@^7.5.5": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.0.tgz", "rxjs@^7.8.1": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz", "safe-array-concat@^1.0.0": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.0.1.tgz", "safe-buffer@5.1.2": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@>=5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.2": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-json-stringify@~1": "https://registry.npmjs.org/safe-json-stringify/-/safe-json-stringify-1.2.0.tgz", "safe-regex-test@^1.0.0": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.0.tgz", "safe-regex@^1.1.0": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@^2.0.2": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@^2.1.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@~2.1.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "sane@^4.0.3": "https://registry.npmjs.org/sane/-/sane-4.1.0.tgz", "sanitize.css@^10.0.0": "https://registry.npmjs.org/sanitize.css/-/sanitize.css-10.0.0.tgz", "sass-loader@^10.0.5": "https://registry.npmjs.org/sass-loader/-/sass-loader-10.4.1.tgz", "sax@>=0.6.0": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "sax@~1.2.4": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "saxes@^5.0.1": "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz", "saxes@^6.0.0": "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz", "scheduler@0.24.0-canary-efb381bbf-20230505": "https://registry.npmjs.org/scheduler/-/scheduler-0.24.0-canary-efb381bbf-20230505.tgz", "scheduler@^0.20.1": "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz", "scheduler@^0.23.0": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "schema-utils@^1.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "schema-utils@^2.6.5": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^2.7.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^2.7.1": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^3.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "schema-utils@^3.1.1": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "select-hose@^2.0.0": "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz", "selfsigned@^1.10.8": "https://registry.npmjs.org/selfsigned/-/selfsigned-1.10.14.tgz", "semver-diff@^4.0.0": "https://registry.npmjs.org/semver-diff/-/semver-diff-4.0.0.tgz", "semver@2 || 3 || 4 || 5": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "semver@7.3.2": "https://registry.npmjs.org/semver/-/semver-7.3.2.tgz", "semver@7.3.4": "https://registry.npmjs.org/semver/-/semver-7.3.4.tgz", "semver@7.3.8": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@7.5.1": "https://registry.npmjs.org/semver/-/semver-7.5.1.tgz", "semver@7.5.3": "https://registry.npmjs.org/semver/-/semver-7.5.3.tgz", "semver@7.x": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "semver@^5.3.0": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "semver@^5.4.1": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "semver@^5.5.0": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "semver@^5.6.0": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "semver@^6.0.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^6.1.0": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^6.1.1": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^6.1.2": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^7.0.0": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.1.1": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.2.1": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "semver@^7.3.2": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "semver@^7.3.4": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.3.5": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.3.7": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.3.8": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "semver@^7.5.2": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "semver@^7.5.3": "https://registry.npmjs.org/semver/-/semver-7.5.3.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "send@0.18.0": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "send@^0.18.0": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "serialize-error@6.0.0": "https://registry.npmjs.org/serialize-error/-/serialize-error-6.0.0.tgz", "serialize-error@^2.1.0": "https://registry.npmjs.org/serialize-error/-/serialize-error-2.1.0.tgz", "serialize-javascript@^4.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "serialize-javascript@^5.0.1": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-5.0.1.tgz", "serve-index@^1.9.1": "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz", "serve-static@1.15.0": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "serve-static@^1.13.1": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "set-blocking@^2.0.0": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "set-value@^2.0.0": "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz", "set-value@^2.0.1": "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz", "setimmediate@^1.0.4": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "setimmediate@^1.0.5": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "setprototypeof@1.1.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "sha.js@^2.4.0": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "sha.js@^2.4.8": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "shaka-player@^2.5.9": "https://registry.npmjs.org/shaka-player/-/shaka-player-2.5.23.tgz", "shallow-clone@^1.0.0": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-1.0.0.tgz", "shallow-clone@^3.0.0": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "shebang-command@^1.2.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^1.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "shell-quote@1.7.2": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.2.tgz", "shell-quote@^1.6.1": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.1.tgz", "shell-quote@^1.7.3": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.1.tgz", "shelljs@0.8.5": "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz", "shellwords@^0.1.1": "https://registry.npmjs.org/shellwords/-/shellwords-0.1.1.tgz", "side-channel@^1.0.4": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "signal-exit@3.0.7": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.0": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.2": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.3": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.7": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "sigstore@^1.0.0": "https://registry.npmjs.org/sigstore/-/sigstore-1.2.0.tgz", "simple-plist@^1.1.0": "https://registry.npmjs.org/simple-plist/-/simple-plist-1.3.1.tgz", "simple-swizzle@^0.2.2": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "simpler-state@^1.0.3": "https://registry.npmjs.org/simpler-state/-/simpler-state-1.2.1.tgz", "sisteransi@^1.0.5": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "slash@3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "slash@^2.0.0": "https://registry.npmjs.org/slash/-/slash-2.0.0.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "slash@^4.0.0": "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz", "slash@^5.0.0": "https://registry.npmjs.org/slash/-/slash-5.1.0.tgz", "slice-ansi@^2.0.0": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-2.1.0.tgz", "slice-ansi@^4.0.0": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz", "slugify@^1.3.4": "https://registry.npmjs.org/slugify/-/slugify-1.6.5.tgz", "smart-buffer@^4.2.0": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "smart-mixin@^2.0.0": "https://registry.npmjs.org/smart-mixin/-/smart-mixin-2.0.0.tgz", "snapdragon-node@^2.0.1": "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "snapdragon-util@^3.0.1": "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "snapdragon@^0.8.1": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "sockjs-client@^1.5.0": "https://registry.npmjs.org/sockjs-client/-/sockjs-client-1.6.1.tgz", "sockjs@^0.3.21": "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz", "socks-proxy-agent@^7.0.0": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-7.0.0.tgz", "socks-proxy-agent@^8.0.1": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.2.tgz", "socks@^2.6.2": "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz", "socks@^2.7.1": "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz", "sort-keys@^1.0.0": "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz", "sort-keys@^2.0.0": "https://registry.npmjs.org/sort-keys/-/sort-keys-2.0.0.tgz", "source-list-map@^2.0.0": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz", "source-map-js@^1.0.2": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-resolve@^0.5.0": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "source-map-resolve@^0.5.2": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "source-map-support@0.5.13": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz", "source-map-support@^0.5.16": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@^0.5.6": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.12": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.20": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-url@^0.4.0": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.1.tgz", "source-map@0.5.6": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz", "source-map@0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.5.0": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "source-map@^0.5.6": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz", "source-map@~0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@~0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "spdx-correct@^3.0.0": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz", "spdx-exceptions@^2.1.0": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz", "spdx-expression-parse@^3.0.0": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "spdx-license-ids@^3.0.0": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz", "spdy-transport@^3.0.0": "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz", "spdy@^4.0.2": "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz", "split-on-first@^1.0.0": "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz", "split-string@^3.0.1": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "split-string@^3.0.2": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "split2@^3.0.0": "https://registry.npmjs.org/split2/-/split2-3.2.2.tgz", "split@^1.0.0": "https://registry.npmjs.org/split/-/split-1.0.1.tgz", "split@^1.0.1": "https://registry.npmjs.org/split/-/split-1.0.1.tgz", "sprintf-js@^1.1.1": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.2.tgz", "sprintf-js@~1.0.2": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "sshpk@^1.7.0": "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz", "ssri@9.0.1": "https://registry.npmjs.org/ssri/-/ssri-9.0.1.tgz", "ssri@^10.0.0": "https://registry.npmjs.org/ssri/-/ssri-10.0.2.tgz", "ssri@^10.0.1": "https://registry.npmjs.org/ssri/-/ssri-10.0.2.tgz", "ssri@^6.0.1": "https://registry.npmjs.org/ssri/-/ssri-6.0.2.tgz", "ssri@^8.0.1": "https://registry.npmjs.org/ssri/-/ssri-8.0.1.tgz", "ssri@^9.0.0": "https://registry.npmjs.org/ssri/-/ssri-9.0.1.tgz", "stable@^0.1.8": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "stack-generator@^2.0.5": "https://registry.npmjs.org/stack-generator/-/stack-generator-2.0.10.tgz", "stack-utils@^2.0.2": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "stack-utils@^2.0.3": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "stackframe@^1.3.4": "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz", "stacktrace-gps@^3.0.4": "https://registry.npmjs.org/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz", "stacktrace-js@^2.0.2": "https://registry.npmjs.org/stacktrace-js/-/stacktrace-js-2.0.2.tgz", "stacktrace-parser@^0.1.10": "https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.10.tgz", "static-extend@^0.1.1": "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "statuses@>= 1.4.0 < 2": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "statuses@~1.5.0": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "stdin-discarder@^0.1.0": "https://registry.npmjs.org/stdin-discarder/-/stdin-discarder-0.1.0.tgz", "stop-iteration-iterator@^1.0.0": "https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.0.0.tgz", "stream-browserify@^2.0.1": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz", "stream-buffers@2.2.x": "https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz", "stream-each@^1.1.0": "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz", "stream-http@^2.7.2": "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz", "stream-shift@^1.0.0": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.1.tgz", "strict-uri-encode@^1.0.0": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz", "strict-uri-encode@^2.0.0": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "string-length@^4.0.1": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "string-length@^5.0.1": "https://registry.npmjs.org/string-length/-/string-length-5.0.1.tgz", "string-natural-compare@^3.0.1": "https://registry.npmjs.org/string-natural-compare/-/string-natural-compare-3.0.1.tgz", "string-template@~1.0.0": "https://registry.npmjs.org/string-template/-/string-template-1.0.0.tgz", "string-width@^1.0.2 || 2 || 3 || 4": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^3.0.0": "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz", "string-width@^3.1.0": "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string.prototype.matchall@^4.0.8": "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.9.tgz", "string.prototype.trim@^1.2.7": "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.7.tgz", "string.prototype.trimend@^1.0.6": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz", "string.prototype.trimstart@^1.0.6": "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.7.tgz", "string_decoder@^1.0.0": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@^1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@~1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "stringify-entities@^3.1.0": "https://registry.npmjs.org/stringify-entities/-/stringify-entities-3.1.0.tgz", "stringify-object@^3.3.0": "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz", "strip-ansi@6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.0.tgz", "strip-ansi@^3.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "strip-ansi@^5.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^5.1.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^5.2.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-bom@^3.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "strip-bom@^4.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "strip-comments@^1.0.2": "https://registry.npmjs.org/strip-comments/-/strip-comments-1.0.2.tgz", "strip-eof@^1.0.0": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-final-newline@^3.0.0": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz", "strip-indent@^3.0.0": "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz", "strip-json-comments@^3.1.0": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@~2.0.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "striptags@^3.1.1": "https://registry.npmjs.org/striptags/-/striptags-3.2.0.tgz", "striptags@^3.2.0": "https://registry.npmjs.org/striptags/-/striptags-3.2.0.tgz", "strnum@^1.0.5": "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz", "strong-log-transformer@2.1.0": "https://registry.npmjs.org/strong-log-transformer/-/strong-log-transformer-2.1.0.tgz", "strong-log-transformer@^2.1.0": "https://registry.npmjs.org/strong-log-transformer/-/strong-log-transformer-2.1.0.tgz", "structured-headers@^0.4.1": "https://registry.npmjs.org/structured-headers/-/structured-headers-0.4.1.tgz", "style-loader@1.3.0": "https://registry.npmjs.org/style-loader/-/style-loader-1.3.0.tgz", "stylehacks@^4.0.0": "https://registry.npmjs.org/stylehacks/-/stylehacks-4.0.3.tgz", "styleq@^0.1.3": "https://registry.npmjs.org/styleq/-/styleq-0.1.3.tgz", "sucrase@^3.20.0": "https://registry.npmjs.org/sucrase/-/sucrase-3.29.0.tgz", "sudo-prompt@9.1.1": "https://registry.npmjs.org/sudo-prompt/-/sudo-prompt-9.1.1.tgz", "sudo-prompt@^8.2.0": "https://registry.npmjs.org/sudo-prompt/-/sudo-prompt-8.2.5.tgz", "sudo-prompt@^9.0.0": "https://registry.npmjs.org/sudo-prompt/-/sudo-prompt-9.2.1.tgz", "superstruct@^0.6.2": "https://registry.npmjs.org/superstruct/-/superstruct-0.6.2.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^6.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz", "supports-color@^7.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "svg-parser@^2.0.2": "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz", "svg-parser@^2.0.4": "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz", "svgo@^1.0.0": "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz", "svgo@^1.2.2": "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz", "svgo@^2.8.0": "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz", "symbol-observable@^1.0.3": "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.2.0.tgz", "symbol-tree@^3.2.4": "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz", "synchronous-promise@^2.0.17": "https://registry.npmjs.org/synchronous-promise/-/synchronous-promise-2.0.17.tgz", "synckit@^0.8.5": "https://registry.npmjs.org/synckit/-/synckit-0.8.6.tgz", "table@^6.0.9": "https://registry.npmjs.org/table/-/table-6.8.1.tgz", "tapable@^1.0.0": "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz", "tapable@^1.1.3": "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz", "tar-stream@~2.2.0": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "tar@6.1.11": "https://registry.npmjs.org/tar/-/tar-6.1.11.tgz", "tar@^6.0.2": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "tar@^6.0.5": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "tar@^6.1.11": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "tar@^6.1.2": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "temp-dir@1.0.0": "https://registry.npmjs.org/temp-dir/-/temp-dir-1.0.0.tgz", "temp-dir@^1.0.0": "https://registry.npmjs.org/temp-dir/-/temp-dir-1.0.0.tgz", "temp-dir@^2.0.0": "https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz", "temp@^0.8.4": "https://registry.npmjs.org/temp/-/temp-0.8.4.tgz", "tempy@0.3.0": "https://registry.npmjs.org/tempy/-/tempy-0.3.0.tgz", "tempy@1.0.0": "https://registry.npmjs.org/tempy/-/tempy-1.0.0.tgz", "tempy@^0.3.0": "https://registry.npmjs.org/tempy/-/tempy-0.3.0.tgz", "tempy@^0.7.1": "https://registry.npmjs.org/tempy/-/tempy-0.7.1.tgz", "terminal-link@^2.0.0": "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz", "terminal-link@^2.1.1": "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz", "terser-webpack-plugin@4.2.3": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.2.3.tgz", "terser-webpack-plugin@^1.4.3": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz", "terser@^4.1.2": "https://registry.npmjs.org/terser/-/terser-4.8.1.tgz", "terser@^4.6.2": "https://registry.npmjs.org/terser/-/terser-4.8.1.tgz", "terser@^4.6.3": "https://registry.npmjs.org/terser/-/terser-4.8.1.tgz", "terser@^5.15.0": "https://registry.npmjs.org/terser/-/terser-5.18.2.tgz", "terser@^5.3.4": "https://registry.npmjs.org/terser/-/terser-5.19.4.tgz", "test-exclude@^6.0.0": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "text-extensions@^1.0.0": "https://registry.npmjs.org/text-extensions/-/text-extensions-1.9.0.tgz", "text-table@0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "text-table@^0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "thenify-all@^1.0.0": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "throat@^5.0.0": "https://registry.npmjs.org/throat/-/throat-5.0.0.tgz", "through2@^2.0.0": "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz", "through2@^2.0.1": "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz", "through2@^4.0.0": "https://registry.npmjs.org/through2/-/through2-4.0.2.tgz", "through@2": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "through@>=2.2.7 <3": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "through@^2.3.4": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "through@^2.3.6": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "thunky@^1.0.2": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "timers-browserify@^2.0.4": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.12.tgz", "timsort@^0.3.0": "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz", "titleize@^3.0.0": "https://registry.npmjs.org/titleize/-/titleize-3.0.0.tgz", "tmp@^0.0.33": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "tmp@~0.2.1": "https://registry.npmjs.org/tmp/-/tmp-0.2.1.tgz", "tmpl@1.0.5": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "to-arraybuffer@^1.0.0": "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "to-fast-properties@^2.0.0": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "to-object-path@^0.3.0": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "to-regex-range@^2.1.0": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "to-regex@^3.0.1": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "to-regex@^3.0.2": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "toggle-switch-react-native@^3.3.0": "https://registry.npmjs.org/toggle-switch-react-native/-/toggle-switch-react-native-3.3.0.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "toposort@^2.0.2": "https://registry.npmjs.org/toposort/-/toposort-2.0.2.tgz", "tough-cookie@^4.0.0": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.3.tgz", "tough-cookie@^4.1.2": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.3.tgz", "tough-cookie@~2.5.0": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz", "tr46@^2.1.0": "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz", "tr46@^3.0.0": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz", "tr46@~0.0.3": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "traverse@~0.6.6": "https://registry.npmjs.org/traverse/-/traverse-0.6.7.tgz", "treeverse@^3.0.0": "https://registry.npmjs.org/treeverse/-/treeverse-3.0.0.tgz", "trim-newlines@^3.0.0": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-3.0.1.tgz", "tryer@^1.0.1": "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz", "ts-api-utils@^1.0.1": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.3.tgz", "ts-interface-checker@^0.1.9": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "ts-jest@~29.0.4": "https://registry.npmjs.org/ts-jest/-/ts-jest-29.0.5.tgz", "ts-node@^10.7.0": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.1.tgz", "ts-object-utils@0.0.5": "https://registry.npmjs.org/ts-object-utils/-/ts-object-utils-0.0.5.tgz", "ts-pnp@1.2.0": "https://registry.npmjs.org/ts-pnp/-/ts-pnp-1.2.0.tgz", "ts-pnp@^1.1.6": "https://registry.npmjs.org/ts-pnp/-/ts-pnp-1.2.0.tgz", "tsconfig-paths@^3.14.2": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.14.2.tgz", "tsconfig-paths@^4.1.2": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz", "tslib@2.4.0": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "tslib@^1.10.0": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "tslib@^1.8.1": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "tslib@^2.0.1": "https://registry.npmjs.org/tslib/-/tslib-2.5.3.tgz", "tslib@^2.0.3": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "tslib@^2.1.0": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "tslib@^2.3.0": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "tslib@^2.4.0": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "tslib@^2.6.0": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "tslib@^2.6.2": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "tsutils@^3.17.1": "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz", "tsutils@^3.21.0": "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz", "tty-browserify@0.0.0": "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz", "tuf-js@^1.0.0": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.2.tgz", "tunnel-agent@^0.6.0": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "tween-functions@^1.0.1": "https://registry.npmjs.org/tween-functions/-/tween-functions-1.2.0.tgz", "tweetnacl@^0.14.3": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "tweetnacl@~0.14.0": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "type-check@^0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-check@~0.3.2": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "type-check@~0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-detect@4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "type-detect@^4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.1.0.tgz", "type-fest@^0.12.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.12.0.tgz", "type-fest@^0.16.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.16.0.tgz", "type-fest@^0.18.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.18.1.tgz", "type-fest@^0.20.2": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "type-fest@^0.3.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.3.1.tgz", "type-fest@^0.4.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.4.1.tgz", "type-fest@^0.6.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz", "type-fest@^0.7.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz", "type-fest@^0.8.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz", "type-fest@^1.0.1": "https://registry.npmjs.org/type-fest/-/type-fest-1.4.0.tgz", "type-fest@^2.13.0": "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz", "type-fest@^2.5.1": "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz", "type-fest@^3.0.0": "https://registry.npmjs.org/type-fest/-/type-fest-3.13.1.tgz", "type-is@~1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "type@^1.0.1": "https://registry.npmjs.org/type/-/type-1.2.0.tgz", "type@^2.7.2": "https://registry.npmjs.org/type/-/type-2.7.2.tgz", "typed-array-buffer@^1.0.0": "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.0.tgz", "typed-array-byte-length@^1.0.0": "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.0.tgz", "typed-array-byte-offset@^1.0.0": "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.0.tgz", "typed-array-length@^1.0.4": "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.4.tgz", "typedarray-to-buffer@^3.1.5": "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "typedarray@^0.0.6": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "typescript@^3 || ^4": "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz", "typescript@^5.1.3": "https://registry.npmjs.org/typescript/-/typescript-5.2.2.tgz", "ua-parser-js@^0.7.30": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.34.tgz", "ua-parser-js@^1.0.35": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.35.tgz", "uglify-es@^3.1.9": "https://registry.npmjs.org/uglify-es/-/uglify-es-3.3.9.tgz", "uglify-js@^3.1.4": "https://registry.npmjs.org/uglify-js/-/uglify-js-3.17.4.tgz", "ultron@~1.1.0": "https://registry.npmjs.org/ultron/-/ultron-1.1.1.tgz", "unbox-primitive@^1.0.2": "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz", "unescape@^1.0.1": "https://registry.npmjs.org/unescape/-/unescape-1.0.1.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.1.0": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "union-value@^1.0.0": "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz", "uniq@^1.0.1": "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz", "uniqs@^2.0.0": "https://registry.npmjs.org/uniqs/-/uniqs-2.0.0.tgz", "unique-filename@^1.1.1": "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz", "unique-filename@^2.0.0": "https://registry.npmjs.org/unique-filename/-/unique-filename-2.0.1.tgz", "unique-filename@^3.0.0": "https://registry.npmjs.org/unique-filename/-/unique-filename-3.0.0.tgz", "unique-slug@^2.0.0": "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz", "unique-slug@^3.0.0": "https://registry.npmjs.org/unique-slug/-/unique-slug-3.0.0.tgz", "unique-slug@^4.0.0": "https://registry.npmjs.org/unique-slug/-/unique-slug-4.0.0.tgz", "unique-string@^1.0.0": "https://registry.npmjs.org/unique-string/-/unique-string-1.0.0.tgz", "unique-string@^2.0.0": "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz", "unique-string@^3.0.0": "https://registry.npmjs.org/unique-string/-/unique-string-3.0.0.tgz", "universal-user-agent@^6.0.0": "https://registry.npmjs.org/universal-user-agent/-/universal-user-agent-6.0.0.tgz", "universalify@^0.1.0": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "universalify@^0.2.0": "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz", "universalify@^1.0.0": "https://registry.npmjs.org/universalify/-/universalify-1.0.0.tgz", "universalify@^2.0.0": "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unquote@~1.1.1": "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz", "unset-value@^1.0.0": "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz", "untildify@^4.0.0": "https://registry.npmjs.org/untildify/-/untildify-4.0.0.tgz", "upath@2.0.1": "https://registry.npmjs.org/upath/-/upath-2.0.1.tgz", "upath@^1.1.1": "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz", "upath@^1.1.2": "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz", "upath@^1.2.0": "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz", "upath@^2.0.1": "https://registry.npmjs.org/upath/-/upath-2.0.1.tgz", "update-browserslist-db@^1.0.10": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "update-browserslist-db@^1.0.11": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "update-notifier@6.0.2": "https://registry.npmjs.org/update-notifier/-/update-notifier-6.0.2.tgz", "uri-js@^4.2.2": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "urix@^0.1.0": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "url-join@4.0.0": "https://registry.npmjs.org/url-join/-/url-join-4.0.0.tgz", "url-join@5.0.0": "https://registry.npmjs.org/url-join/-/url-join-5.0.0.tgz", "url-loader@4.1.1": "https://registry.npmjs.org/url-loader/-/url-loader-4.1.1.tgz", "url-parse@^1.5.10": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "url-parse@^1.5.3": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "url-parse@^1.5.9": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "url-pattern@^1.0.3": "https://registry.npmjs.org/url-pattern/-/url-pattern-1.0.3.tgz", "url@^0.11.0": "https://registry.npmjs.org/url/-/url-0.11.1.tgz", "use-context-selector@^1.3.7": "https://registry.npmjs.org/use-context-selector/-/use-context-selector-1.4.1.tgz", "use-immer@^0.8.1": "https://registry.npmjs.org/use-immer/-/use-immer-0.8.1.tgz", "use-latest-callback@^0.1.5": "https://registry.npmjs.org/use-latest-callback/-/use-latest-callback-0.1.6.tgz", "use-sync-external-store@^1.0.0": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz", "use-sync-external-store@^1.2.0": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz", "use@^3.1.0": "https://registry.npmjs.org/use/-/use-3.1.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@^1.0.2": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util.promisify@1.0.0": "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.0.tgz", "util.promisify@~1.0.0": "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz", "util@0.10.3": "https://registry.npmjs.org/util/-/util-0.10.3.tgz", "util@^0.11.0": "https://registry.npmjs.org/util/-/util-0.11.1.tgz", "utila@~0.4": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "uuid@8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "uuid@^3.3.2": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "uuid@^3.4.0": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "uuid@^7.0.3": "https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz", "uuid@^8.0.0": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "uuid@^8.3.0": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "uuid@^8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "v8-compile-cache-lib@^3.0.1": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "v8-compile-cache@2.3.0": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "v8-compile-cache@^2.0.3": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz", "v8-to-istanbul@^7.0.0": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.1.2.tgz", "v8-to-istanbul@^9.0.1": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.2.0.tgz", "valid-url@~1.0.9": "https://registry.npmjs.org/valid-url/-/valid-url-1.0.9.tgz", "validate-npm-package-license@3.0.4": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "validate-npm-package-license@^3.0.1": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "validate-npm-package-license@^3.0.4": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "validate-npm-package-name@4.0.0": "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-4.0.0.tgz", "validate-npm-package-name@^3.0.0": "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz", "validate-npm-package-name@^4.0.0": "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-4.0.0.tgz", "validate-npm-package-name@^5.0.0": "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-5.0.0.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vendors@^1.0.0": "https://registry.npmjs.org/vendors/-/vendors-1.0.4.tgz", "verror@1.10.0": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "vlq@^1.0.0": "https://registry.npmjs.org/vlq/-/vlq-1.0.1.tgz", "vm-browserify@^1.0.1": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.2.tgz", "vm2@^3.9.19": "https://registry.npmjs.org/vm2/-/vm2-3.9.19.tgz", "void-elements@3.1.0": "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz", "w3c-hr-time@^1.0.2": "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz", "w3c-xmlserializer@^2.0.0": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz", "w3c-xmlserializer@^4.0.0": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-4.0.0.tgz", "walk-up-path@^1.0.0": "https://registry.npmjs.org/walk-up-path/-/walk-up-path-1.0.0.tgz", "walker@^1.0.7": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "walker@^1.0.8": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "walker@~1.0.5": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "warn-once@^0.1.0": "https://registry.npmjs.org/warn-once/-/warn-once-0.1.1.tgz", "warning@4.0.1": "https://registry.npmjs.org/warning/-/warning-4.0.1.tgz", "warning@^4.0.1": "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz", "watchpack-chokidar2@^2.0.1": "https://registry.npmjs.org/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz", "watchpack@^1.7.4": "https://registry.npmjs.org/watchpack/-/watchpack-1.7.5.tgz", "wbuf@^1.1.0": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "wbuf@^1.7.3": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "wcwidth@^1.0.0": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "wcwidth@^1.0.1": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "web-streams-polyfill@^3.0.3": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "webidl-conversions@^3.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "webidl-conversions@^5.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz", "webidl-conversions@^6.1.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz", "webidl-conversions@^7.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "webpack-dev-middleware@^3.7.2": "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-3.7.3.tgz", "webpack-dev-server@3.11.1": "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-3.11.1.tgz", "webpack-log@^2.0.0": "https://registry.npmjs.org/webpack-log/-/webpack-log-2.0.0.tgz", "webpack-manifest-plugin@2.2.0": "https://registry.npmjs.org/webpack-manifest-plugin/-/webpack-manifest-plugin-2.2.0.tgz", "webpack-merge@^4.2.2": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.2.tgz", "webpack-sources@^1.1.0": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.3.0": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.0": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.1": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.3": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack@4.44.2": "https://registry.npmjs.org/webpack/-/webpack-4.44.2.tgz", "websocket-driver@>=0.5.1": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-driver@^0.7.4": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-extensions@>=0.1.1": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "whatwg-encoding@^1.0.5": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz", "whatwg-encoding@^2.0.0": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "whatwg-fetch@>=0.10.0": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.18.tgz", "whatwg-fetch@^3.0.0": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz", "whatwg-fetch@^3.4.1": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.18.tgz", "whatwg-mimetype@^2.3.0": "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz", "whatwg-mimetype@^3.0.0": "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz", "whatwg-url@^11.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz", "whatwg-url@^5.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "whatwg-url@^8.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz", "whatwg-url@^8.5.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz", "which-boxed-primitive@^1.0.2": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "which-builtin-type@^1.1.3": "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.1.3.tgz", "which-collection@^1.0.1": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.1.tgz", "which-module@^2.0.0": "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz", "which-typed-array@^1.1.10": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "which-typed-array@^1.1.11": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "which-typed-array@^1.1.9": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "which@^1.2.9": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "which@^1.3.1": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "which@^2.0.2": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "which@^3.0.0": "https://registry.npmjs.org/which/-/which-3.0.0.tgz", "wide-align@^1.1.5": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz", "widest-line@^4.0.1": "https://registry.npmjs.org/widest-line/-/widest-line-4.0.1.tgz", "wildcard-match@5.1.2": "https://registry.npmjs.org/wildcard-match/-/wildcard-match-5.1.2.tgz", "windows-release@^5.0.1": "https://registry.npmjs.org/windows-release/-/windows-release-5.1.1.tgz", "wonka@^4.0.14": "https://registry.npmjs.org/wonka/-/wonka-4.0.15.tgz", "wonka@^6.1.2": "https://registry.npmjs.org/wonka/-/wonka-6.2.4.tgz", "word-wrap@~1.2.3": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "wordwrap@^1.0.0": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "workbox-background-sync@^5.1.4": "https://registry.npmjs.org/workbox-background-sync/-/workbox-background-sync-5.1.4.tgz", "workbox-broadcast-update@^5.1.4": "https://registry.npmjs.org/workbox-broadcast-update/-/workbox-broadcast-update-5.1.4.tgz", "workbox-build@^5.1.4": "https://registry.npmjs.org/workbox-build/-/workbox-build-5.1.4.tgz", "workbox-cacheable-response@^5.1.4": "https://registry.npmjs.org/workbox-cacheable-response/-/workbox-cacheable-response-5.1.4.tgz", "workbox-core@^5.1.4": "https://registry.npmjs.org/workbox-core/-/workbox-core-5.1.4.tgz", "workbox-expiration@^5.1.4": "https://registry.npmjs.org/workbox-expiration/-/workbox-expiration-5.1.4.tgz", "workbox-google-analytics@^5.1.4": "https://registry.npmjs.org/workbox-google-analytics/-/workbox-google-analytics-5.1.4.tgz", "workbox-navigation-preload@^5.1.4": "https://registry.npmjs.org/workbox-navigation-preload/-/workbox-navigation-preload-5.1.4.tgz", "workbox-precaching@^5.1.4": "https://registry.npmjs.org/workbox-precaching/-/workbox-precaching-5.1.4.tgz", "workbox-range-requests@^5.1.4": "https://registry.npmjs.org/workbox-range-requests/-/workbox-range-requests-5.1.4.tgz", "workbox-routing@^5.1.4": "https://registry.npmjs.org/workbox-routing/-/workbox-routing-5.1.4.tgz", "workbox-strategies@^5.1.4": "https://registry.npmjs.org/workbox-strategies/-/workbox-strategies-5.1.4.tgz", "workbox-streams@^5.1.4": "https://registry.npmjs.org/workbox-streams/-/workbox-streams-5.1.4.tgz", "workbox-sw@^5.1.4": "https://registry.npmjs.org/workbox-sw/-/workbox-sw-5.1.4.tgz", "workbox-webpack-plugin@5.1.4": "https://registry.npmjs.org/workbox-webpack-plugin/-/workbox-webpack-plugin-5.1.4.tgz", "workbox-window@^5.1.4": "https://registry.npmjs.org/workbox-window/-/workbox-window-5.1.4.tgz", "worker-farm@^1.7.0": "https://registry.npmjs.org/worker-farm/-/worker-farm-1.7.0.tgz", "worker-rpc@^0.1.0": "https://registry.npmjs.org/worker-rpc/-/worker-rpc-0.1.1.tgz", "wrap-ansi@^5.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "wrap-ansi@^6.0.1": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "wrap-ansi@^6.2.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@4.0.1": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.1.tgz", "write-file-atomic@^2.3.0": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.3.tgz", "write-file-atomic@^2.4.2": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.3.tgz", "write-file-atomic@^3.0.0": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "write-file-atomic@^3.0.3": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "write-file-atomic@^4.0.2": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "write-file-atomic@^5.0.0": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.0.tgz", "write-json-file@^3.2.0": "https://registry.npmjs.org/write-json-file/-/write-json-file-3.2.0.tgz", "write-pkg@4.0.0": "https://registry.npmjs.org/write-pkg/-/write-pkg-4.0.0.tgz", "ws@^3.3.1": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "ws@^6.2.1": "https://registry.npmjs.org/ws/-/ws-6.2.2.tgz", "ws@^6.2.2": "https://registry.npmjs.org/ws/-/ws-6.2.2.tgz", "ws@^7": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "ws@^7.4.6": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "ws@^7.5.1": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "ws@^8.11.0": "https://registry.npmjs.org/ws/-/ws-8.15.0.tgz", "ws@^8.12.1": "https://registry.npmjs.org/ws/-/ws-8.13.0.tgz", "xcode@^3.0.1": "https://registry.npmjs.org/xcode/-/xcode-3.0.1.tgz", "xdg-basedir@^5.0.1": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-5.1.0.tgz", "xdg-basedir@^5.1.0": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-5.1.0.tgz", "xml-name-validator@^3.0.0": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz", "xml-name-validator@^4.0.0": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz", "xml2js@0.6.0": "https://registry.npmjs.org/xml2js/-/xml2js-0.6.0.tgz", "xml2js@^0.4.23": "https://registry.npmjs.org/xml2js/-/xml2js-0.4.23.tgz", "xmlbuilder@^14.0.0": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-14.0.0.tgz", "xmlbuilder@^15.1.1": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "xmlbuilder@~11.0.0": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "xmlchars@^2.2.0": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz", "xmlhttprequest@^1.7.0": "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz", "xtend@^4.0.0": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "xtend@~4.0.1": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "y18n@^4.0.0": "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yallist@^4.0.0": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "yaml@^1.10.0": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yaml@^2.2.1": "https://registry.npmjs.org/yaml/-/yaml-2.3.1.tgz", "yargs-parser@20.2.4": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.4.tgz", "yargs-parser@21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs-parser@^13.1.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.1.2.tgz", "yargs-parser@^18.1.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz", "yargs-parser@^20.2.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs-parser@^20.2.3": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs-parser@^21.0.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs-parser@^21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@16.2.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yargs@^13.3.2": "https://registry.npmjs.org/yargs/-/yargs-13.3.2.tgz", "yargs@^15.0.2": "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz", "yargs@^15.1.0": "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz", "yargs@^15.4.1": "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz", "yargs@^16.2.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yargs@^17.3.1": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "yargs@^17.6.2": "https://registry.npmjs.org/yargs/-/yargs-17.7.1.tgz", "yn@3.1.1": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "yup@^0.32.11": "https://registry.npmjs.org/yup/-/yup-0.32.11.tgz", "zod@^3.20.6": "https://registry.npmjs.org/zod/-/zod-3.21.4.tgz", "zustand@^3.5.12": "https://registry.npmjs.org/zustand/-/zustand-3.7.2.tgz"}, "files": [], "artifacts": {"fsevents@1.2.13": ["build", "build/config.gypi", "build/node_gyp_bins", "build/node_gyp_bins/python3"]}}