import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Spinner from '../common/spinner';
import { Pressable } from 'react-native';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getSettings } from '../../../config';
import { useOrders, useCurrentUser, useUser } from '@appmaker-xyz/shopify';
import { storePincode, getPincode, extractPincodeFromLatestOrder } from '../../utils/Helper';
import { getPincodeFromLocation } from '../../utils/LocationService';
import Icon from 'react-native-vector-icons/MaterialIcons';
export default function Edd({ variants, selectedVariant }) {
  const [pin, setPin] = useState('');
  const [pinText, setPinText] = useState('');
  const [loading, setLoading] = useState(false);
  const [locationLoading, setLocationLoading] = useState(false);
  const [finalEdd, setFinalEdd] = useState('');
  const [hasLatestOrder, setHasLatestOrder] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const settings = getSettings();
  const { data: currentUser } = useCurrentUser({});
  const { user, isLoggedin } = useUser();
  const { orderList, isLoading: ordersLoading } = useOrders({
    limit: 5
  });

  console.log("=== COMPONENT DATA DEBUG ===");
  console.log("orderLIST", JSON.stringify(orderList));
  console.log("currentUser", JSON.stringify(currentUser));
  console.log("user", JSON.stringify(user));
  console.log("isLoggedin", isLoggedin);
  console.log("ordersLoading", ordersLoading);

  // Reset initialization when user login status changes
  useEffect(() => {
    console.log('🔄 User login status changed, resetting initialization');
    setIsInitialized(false);
    setPin('');
    setPinText('');
    setFinalEdd('');
  }, [isLoggedin]);
  const extractProductVariantId = variants.map((value) => {
    return { variantId: value.node.id.split('/ProductVariant/')[1] };
  });

  // Initialize pincode auto-fill logic
  useEffect(() => {
    const initializePincode = async () => {
      console.log('=== PINCODE INITIALIZATION DEBUG ===');
      console.log('isInitialized:', isInitialized);
      console.log('ordersLoading:', ordersLoading);
      console.log('isLoggedin:', isLoggedin);
      console.log('user:', user ? 'Present' : 'Not present');
      console.log('orderList length:', orderList ? orderList.length : 'No orderList');

      // Don't initialize if already done or if orders are still loading
      if (isInitialized) {
        console.log('Skipping initialization - already initialized');
        return;
      }

      // If orders are loading, wait for them to complete
      if (ordersLoading) {
        console.log('Orders are loading, waiting...');
        return;
      }

      try {
        // Step 1: Check if user is logged in
        if (isLoggedin && user) {
          console.log('✅ User is logged in, checking for latest order...');
          console.log('User details:', JSON.stringify(user));

          // Wait a bit more if orderList is not available yet
          if (!orderList || orderList.length === 0) {
            console.log('⏳ OrderList not available yet, will retry...');
            // Don't set initialized to true, so it will retry
            return;
          }

          console.log('OrderList data:', JSON.stringify(orderList, null, 2));

          // Step 2: Extract pincode from latest order
          const orderPincode = extractPincodeFromLatestOrder(orderList);
          console.log('Extracted orderPincode:', orderPincode);

          if (orderPincode) {
            console.log('✅ Found pincode from latest order:', orderPincode);
            setPin(orderPincode);
            setHasLatestOrder(true);
            await storePincode(orderPincode);
            setPinText('Pincode auto-filled from your latest order');

            // Trigger EDD check automatically
            console.log('🚀 Triggering EDD API with pincode:', orderPincode);
            fetchRequestEDD(orderPincode);
          } else {
            console.log('❌ No pincode found in latest order');
            setHasLatestOrder(false);

            // Step 3: Try to get pincode from location if no order found
            await tryLocationPincode();
          }
        } else {
          console.log('❌ User not logged in, trying location...');
          // Step 3: Try to get pincode from location for non-logged users
          await tryLocationPincode();
        }

        setIsInitialized(true);
        console.log('✅ Pincode initialization completed');
      } catch (error) {
        console.error('❌ Error initializing pincode:', error);
        setIsInitialized(true);
      }
    };

    initializePincode();
  }, [isLoggedin, user, orderList, ordersLoading, isInitialized]);

  // Try to get pincode from stored data or location
  const tryLocationPincode = async () => {
    try {
      // First try to get stored pincode
      const storedPincode = await getPincode();
      if (storedPincode) {
        console.log('Found stored pincode:', storedPincode);
        setPin(storedPincode);
        setPinText('Using previously saved pincode');
        fetchRequestEDD(storedPincode);
        return;
      }

      // If no stored pincode, try location (but don't auto-request permission)
      console.log('No stored pincode found');
    } catch (error) {
      console.error('Error trying location pincode:', error);
    }
  };

  // Handle location button click
  const handleGetLocation = async () => {
    setLocationLoading(true);
    setPinText('');

    try {
      const locationPincode = await getPincodeFromLocation();

      if (locationPincode) {
        setPin(locationPincode);
        fetchRequestEDD(locationPincode);
        setPinText('Pincode auto-filled from your current location');
      } else {
        setPinText('Unable to get location. Please enter pincode manually.');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setPinText('Location access failed. Please enter pincode manually.');
    } finally {
      setLocationLoading(false);
    }
  };

  // Handle manual pincode entry and check
  function handleDeliveryDetailbutton() {
    setLoading(true);

    if (!pin || pin.length !== 6) {
      setPinText('Please Enter a valid Pin');
      setLoading(false);
    } else {
      fetchRequestEDD(pin);
      setPinText('');
    }
  }

  // Handle pincode input change
  const handlePincodeChange = async (newPin) => {
    if (newPin === '') {
      setPinText('');
    }
    setPin(newPin.trim());

    // Store pincode when user manually enters it
    if (newPin.length === 6) {
      await storePincode(newPin.trim());
    }
  };

  const fetchRequestEDD = async (pinData) => {
    console.log('=== EDD API CALL DEBUG ===');
    console.log('🚀 Calling EDD API with pincode:', pinData);
    console.log('EDD API URL:', settings.edd_api_url);
    console.log('Variants data:', extractProductVariantId);

    setLoading(true);

    try {
      const requestBody = {
        dropPincode: pinData,
        quantity: 1,
        variantsData: extractProductVariantId,
      };

      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(
        settings.edd_api_url,
        {
          method: 'POST',
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        },
      );

      const responseData = await response.json();
      console.log('✅ EDD API Response:', JSON.stringify(responseData, null, 2));

      if (responseData.serviceable) {
        if (responseData?.variantsData?.length) {
          const edd = responseData?.variantsData?.[0]?.edd;
          console.log('✅ EDD found:', edd);
          setFinalEdd(edd);
        }
        setPinText('');
        setLoading(false);
      } else {
        console.log('❌ Pincode not serviceable');
        setLoading(false);
        setFinalEdd('');
        setPinText('Pincode not serviceable.');
      }
    } catch (error) {
      console.error('❌ EDD API Error:', error);
      setLoading(false);
      setPinText('Error checking delivery details. Please try again.');
    }
  };

  return (
    <View>
      <View
        style={{
          borderWidth: 0,
          elevation: 0,
          paddingVertical: heightPixel(8),
          borderRadius: widthPixel(10),
        }}>
        <Text
          style={{
            color: '#221f20',
            fontSize: fonts._16,
            fontFamily: fonts.FONT_FAMILY.SemiBold,
            marginBottom: heightPixel(10),
          }}>
          Delivery Details
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            overflow: 'hidden',
            gap: widthPixel(8),
            flexWrap: 'wrap',
          }}>
          <View style={{ width: '60%' }}>
            <TextInput
              placeholder="Enter Your pincode"
              maxLength={6}
              keyboardType="numeric"
              onChangeText={handlePincodeChange}
              value={pin}
              style={{
                borderWidth: 1,
                borderColor: 'rgba(34,31,32,.1)',
                padding: widthPixel(8),
                borderRadius: widthPixel(8),
                fontFamily: 'Jost-SemiBold',
                paddingLeft: widthPixel(20),
                height: heightPixel(50),
                alignItems: 'center', justifyContent: 'center',
              }}
            />
          </View>
          <View style={{ width: '32%' }}>
            <Pressable
              onPress={() => {
                handleDeliveryDetailbutton();
              }}>
              <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: -0.3336, y: 0 }} // Adjusted based on the angle in the CSS
                end={{ x: 1.3952, y: 1 }} // Adjusted based on the angle in the CSS
                style={{
                  paddingHorizontal: widthPixel(10),
                  // paddingVertical: heightPixel(13),
                  height: heightPixel(50),
                  alignItems: 'center', justifyContent: 'center',
                  borderRadius: widthPixel(10),
                }}>
                {!loading ? (
                  <Text style={styles.buttonText}>CHECK</Text>
                ) : (
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {/* <Spinner /> */}
                    <ActivityIndicator size={'small'} color={'white'} />
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {/* Get My Location Button - Show for all users */}
        <View style={{ marginTop: heightPixel(10) }}>
          <Pressable
            onPress={handleGetLocation}
            disabled={locationLoading}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: heightPixel(12),
              paddingHorizontal: widthPixel(16),
              borderWidth: 1,
              borderColor: '#221f20',
              borderRadius: widthPixel(8),
              backgroundColor: locationLoading ? '#f5f5f5' : 'transparent',
            }}>
            {locationLoading ? (
              <ActivityIndicator size="small" color="#221f20" />
            ) : (
              <>
                <Icon name="my-location" size={20} color="#221f20" style={{ marginRight: widthPixel(8) }} />
                <Text style={{
                  color: '#221f20',
                  fontSize: fonts._14,
                  fontFamily: fonts.FONT_FAMILY.Medium,
                }}>
                  {hasLatestOrder ? 'Get My Location' : 'Use Current Location'}
                </Text>
              </>
            )}
          </Pressable>
        </View>

        {/* Debug button - Only in development */}
        {__DEV__ && (
          <View style={{ marginTop: heightPixel(10) }}>
            <Pressable
              onPress={() => {
                console.log('=== MANUAL DEBUG TRIGGER ===');
                const testPincode = extractPincodeFromLatestOrder(orderList);
                console.log('Manual test result:', testPincode);
                if (testPincode) {
                  setPin(testPincode);
                  fetchRequestEDD(testPincode);
                  setPinText('Debug: Pincode extracted manually');
                } else {
                  setPinText('Debug: No pincode found in orders');
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: heightPixel(8),
                paddingHorizontal: widthPixel(16),
                borderWidth: 1,
                borderColor: '#ff6b6b',
                borderRadius: widthPixel(8),
                backgroundColor: '#ffe0e0',
              }}>
              <Text style={{
                color: '#ff6b6b',
                fontSize: fonts._12,
                fontFamily: fonts.FONT_FAMILY.Medium,
              }}>
                🐛 TEST: Extract Pincode from Orders
              </Text>
            </Pressable>
          </View>
        )}

        {pinText != '' && (
          <View style={{ paddingVertical: heightPixel(10) }}>
            <Text
              style={{
                color: pinText.includes('auto-filled') || pinText.includes('location') ? '#4CAF50' : 'red',
                fontFamily: fonts.FONT_FAMILY.Regular
              }}>
              {pinText}
            </Text>
          </View>
        )}

        {/* EDD details , div needs to be visible when pincode entered  */}
        {finalEdd ? (
          <View>
            <ScrollView
              horizontal={true}
              style={{
                paddingVertical: heightPixel(10),
                borderWidth: 0,
                marginVertical: heightPixel(10),
                width: '100%',
              }}
              contentContainerStyle={{
                paddingRight: widthPixel(50),
              }}>
              <View style={{ display: 'flex', flexDirection: 'row' }}>
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '60%',
                    marginRight: widthPixel(10),
                    height: heightPixel(50),
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Group_239103.jpg?v=16732760107086212298',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: fonts._14,
                        fontFamily: fonts.FONT_FAMILY.Regular,
                      }}>
                      Estimated Delivery by{' '}
                      <Text
                        style={{
                          fontSize: fonts._13,
                          fontFamily: fonts.FONT_FAMILY.Bold,
                        }}>
                        {finalEdd}
                      </Text>
                    </Text>
                  </View>
                </View>

                {/* 3 */}
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '40%',
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Vector_1_a29858f8-e63b-4de4-9be6-3f78849099e3.png?v=9477741078544455416',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      fontFamily: fonts.FONT_FAMILY.Regular,
                    }}>
                    {'Eligible for Free Delivery'}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        ) : (
          <View></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonText: {
    fontSize: fonts._14,
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
});
