import Geolocation from '@react-native-community/geolocation';
import { Platform, PermissionsAndroid } from 'react-native';
import { PERMISSIONS, request, check, RESULTS } from 'react-native-permissions';
import { getSettings } from '../../config';
import { storePincode } from './Helper';

/**
 * Request location permissions based on platform
 * @returns {Promise<boolean>} - Whether permission was granted
 */
export const requestLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location to provide accurate delivery estimates.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
  } catch (err) {
    console.warn('Error requesting location permission:', err);
    return false;
  }
};

/**
 * Check if location permission is granted
 * @returns {Promise<boolean>} - Whether permission is granted
 */
export const checkLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      const granted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      return granted;
    }
  } catch (err) {
    console.warn('Error checking location permission:', err);
    return false;
  }
};

/**
 * Get current location coordinates
 * @returns {Promise<{latitude: number, longitude: number} | null>} - Location coordinates or null
 */
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        resolve({ latitude, longitude });
      },
      (error) => {
        console.error('Error getting current location:', error);
        reject(error);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  });
};

/**
 * Reverse geocode coordinates to get pincode using free service
 * @param {number} latitude - Latitude coordinate
 * @param {number} longitude - Longitude coordinate
 * @returns {Promise<string | null>} - Pincode or null
 */
export const reverseGeocode = async (latitude, longitude) => {
  try {
    // First try with Google Maps API if available
    const settings = getSettings();
    const googleApiKey = settings.google_maps_api_key;

    if (googleApiKey && googleApiKey !== 'YOUR_API_KEY') {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${googleApiKey}`
        );

        const data = await response.json();

        if (data.status === 'OK') {
          // Extract postal code from address components
          for (const result of data.results) {
            for (const component of result.address_components) {
              if (component.types.includes('postal_code')) {
                return component.long_name;
              }
            }
          }
        }
      } catch (error) {
        console.log('Google Maps API failed, trying fallback service');
      }
    }

    // Fallback to free reverse geocoding service
    const fallbackResponse = await fetch(
      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
    );

    const fallbackData = await fallbackResponse.json();

    if (fallbackData && fallbackData.postcode) {
      // Validate if it's a valid Indian pincode (6 digits)
      const pincode = fallbackData.postcode.toString();
      if (/^\d{6}$/.test(pincode)) {
        return pincode;
      }
    }

    console.log('No valid pincode found from reverse geocoding');
    return null;
  } catch (error) {
    console.error('Error in reverse geocoding:', error);
    return null;
  }
};

/**
 * Get pincode from current location
 * @returns {Promise<string | null>} - Pincode or null
 */
export const getPincodeFromLocation = async () => {
  try {
    const hasPermission = await checkLocationPermission();
    
    if (!hasPermission) {
      const granted = await requestLocationPermission();
      if (!granted) {
        console.log('Location permission denied');
        return null;
      }
    }
    
    const location = await getCurrentLocation();
    if (!location) return null;
    
    const { latitude, longitude } = location;
    const pincode = await reverseGeocode(latitude, longitude);
    
    if (pincode) {
      // Store the pincode for future use
      await storePincode(pincode);
    }
    
    return pincode;
  } catch (error) {
    console.error('Error getting pincode from location:', error);
    return null;
  }
};
