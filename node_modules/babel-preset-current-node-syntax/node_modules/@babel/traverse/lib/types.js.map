{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport type { NodePath } from \"./index.ts\";\nimport type { VirtualTypeAliases } from \"./path/lib/virtual-types.ts\";\n\nexport type VisitPhase = \"enter\" | \"exit\";\n\ntype VisitNodeObject<S, P extends t.Node> = {\n  [K in VisitPhase]?: VisitNodeFunction<S, P>;\n};\n\nexport type ExplVisitNode<S, P extends t.Node> = {\n  [K in VisitPhase]?: VisitNodeFunction<S, P>[];\n};\n\nexport type ExplodedVisitor<S = unknown> = ExplVisitNode<S, t.Node> & {\n  [Type in t.Node[\"type\"]]?: ExplVisitNode<S, Extract<t.Node, { type: Type }>>;\n} & { _exploded: true; _verified: true };\n\nexport type Visitor<S = unknown> =\n  | (VisitNodeObject<S, t.Node> & {\n      [Type in t.Node[\"type\"]]?: VisitNode<S, Extract<t.Node, { type: Type }>>;\n    } & {\n      [K in keyof t.Aliases]?: VisitNode<S, t.Aliases[K]>;\n    } & {\n      [K in keyof VirtualTypeAliases]?: VisitNode<S, VirtualTypeAliases[K]>;\n    } & {\n      // Babel supports `NodeTypesWithoutComment | NodeTypesWithoutComment | ... ` but it is\n      // too complex for TS. So we type it as a general visitor only if the key contains `|`\n      // this is good enough for non-visitor traverse options e.g. `noScope`\n      [k: `${string}|${string}`]: VisitNode<S, t.Node>;\n    })\n  | ExplodedVisitor<S>;\n\nexport type VisitNode<S, P extends t.Node> =\n  | VisitNodeFunction<S, P>\n  | VisitNodeObject<S, P>;\n\nexport type VisitNodeFunction<S, P extends t.Node> = (\n  this: S,\n  path: NodePath<P>,\n  state: S,\n) => void;\n"], "mappings": ""}