{"version": 3, "names": ["_buffer", "require", "n", "_t", "generatorFunctions", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumDeclaration", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "NON_DECIMAL_LITERAL", "PURE_ANNOTATION_RE", "HAS_NEWLINE", "HAS_BlOCK_COMMENT_END", "needsParens", "Printer", "constructor", "format", "map", "inForStatementInitCounter", "_printStack", "_indent", "_indentChar", "_indentRepeat", "_insideAux", "_parenPushNewlineState", "_noLineTerminator", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "_buf", "<PERSON><PERSON><PERSON>", "indent", "style", "charCodeAt", "length", "_inputMap", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "node", "minified", "removeLastSemicolon", "sourceWithOffset", "loc", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "endsWith", "_append", "number", "Number", "isInteger", "test", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "withSource", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_maybeIndent", "append", "_maybeAddParenChar", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "parenPushNewlineState", "printed", "len", "cha", "chaPost", "slice", "catchUp", "line", "count", "getCurrentLine", "_loc$prop", "printTerminatorless", "parent", "isLabel", "terminatorState", "trailingCommentsLineOffset", "forceParens", "_node$extra", "nodeType", "type", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "push", "oldInAux", "shouldPrintParens", "retainFunctionParens", "extra", "parenthesized", "_printLeadingComments", "bind", "_printTrailingComments", "pop", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "value", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "_nodes$0$loc", "startLine", "start", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "statement", "_printNewline", "iterator", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "leadingComments", "printBlock", "body", "lineOffset", "innerComments", "trailingComments", "_printComments", "comments", "printInnerComments", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "_opts$indent", "printList", "items", "commaSeparator", "newLine", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "column", "newlineRegex", "RegExp", "replace", "indentSize", "getCurrentColumn", "repeat", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "end", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "Noop", "_default", "exports", "default"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumDeclaration,\n} from \"@babel/types\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport * as charCodes from \"charcodes\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst NON_DECIMAL_LITERAL = /^0[box]/;\nconst PURE_ANNOTATION_RE = /^\\s*[@#]__PURE__\\s*$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_BlOCK_COMMENT_END = /\\*\\//;\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  recordAndTupleSyntaxType: RecordAndTuplePluginOptions[\"syntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n    this._buf = new Buffer(map);\n\n    this._indentChar = format.indent.style.charCodeAt(0);\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n  inForStatementInitCounter: number = 0;\n\n  declare _buf: Buffer;\n  _printStack: Array<t.Node> = [];\n  _indent: number = 0;\n  _indentChar: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _parenPushNewlineState: { printed: boolean } | null = null;\n  _noLineTerminator: boolean = false;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this._maybePrintInnerComments();\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (str.charCodeAt(0) === charCodes.slash && this.endsWith(charCodes.slash))\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string): void {\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(+str) &&\n      !NON_DECIMAL_LITERAL.test(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n  token(str: string, maybeNewline = false): void {\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  withSource(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    cb: () => void,\n  ): void {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(prop, loc);\n\n    this._buf.withSource(prop, loc, cb);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeAddParen(str);\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _queue(char: number) {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._indentChar, this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  _maybeAddParenChar(char: number): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    if (char === charCodes.space) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    if (char !== charCodes.lineFeed) {\n      this._parenPushNewlineState = null;\n      return;\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _maybeAddParen(str: string): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    const len = str.length;\n\n    let i;\n    for (i = 0; i < len && str.charCodeAt(i) === charCodes.space; i++) continue;\n    if (i === len) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    const cha = str.charCodeAt(i);\n    if (cha !== charCodes.lineFeed) {\n      if (\n        // This is not a comment (it doesn't start with /)\n        cha !== charCodes.slash ||\n        // This is not a comment (it's a / operator)\n        i + 1 === len\n      ) {\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n\n      const chaPost = str.charCodeAt(i + 1);\n\n      if (chaPost === charCodes.asterisk) {\n        // This is a block comment\n\n        if (PURE_ANNOTATION_RE.test(str.slice(i + 2, len - 2))) {\n          // We avoid printing newlines after #__PURE__ comments (we treat\n          // then as unary operators), but we must keep the old\n          // parenPushNewlineState because, if a newline was forbidden, it is\n          // still forbidden after the comment.\n          return;\n        }\n\n        // NOTE: code flow continues from here to after these if/elses\n      } else if (chaPost !== charCodes.slash) {\n        // This is neither a block comment, nor a line comment.\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const line = loc?.[prop]?.line;\n    if (line != null) {\n      const count = line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node, parent: t.Node, isLabel: boolean) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    if (isLabel) {\n      this._noLineTerminator = true;\n      this.print(node, parent);\n    } else {\n      const terminatorState = {\n        printed: false,\n      };\n      this._parenPushNewlineState = terminatorState;\n      this.print(node, parent);\n      /**\n       * Print an ending parentheses if a starting one has been printed.\n       */\n      if (terminatorState.printed) {\n        this.dedent();\n        this.newline();\n        this.token(\")\");\n      }\n    }\n  }\n\n  print(\n    node: t.Node | null,\n    parent?: t.Node,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n    forceParens?: boolean,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    this._printStack.push(node);\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == undefined;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const shouldPrintParens =\n      forceParens ||\n      (format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\" &&\n        node.extra?.parenthesized) ||\n      needsParens(node, parent, this._printStack);\n\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      this._endsWithInnerRaw = false;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(loc, printMethod.bind(this, node, parent));\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._printStack.pop();\n\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DecimalLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    parent: t.Node,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    let { indent } = opts;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, parent, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (i < len - 1) separator?.();\n\n      if (opts.statement) {\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node, parent: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node, parent);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node, parent);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments() {\n    if (this._endsWithInnerRaw) this.printInnerComments();\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments() {\n    const node = this._printStack[this._printStack.length - 1];\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(COMMENT_TYPE.INNER, comments, node);\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    parent: t.Node,\n    opts: PrintSequenceOptions = {},\n  ) {\n    opts.statement = true;\n    opts.indent ??= false;\n    this.printJoin(nodes, parent, opts);\n  }\n\n  printList(items: t.Node[], parent: t.Node, opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    this.printJoin(items, parent, opts);\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(comment: t.Comment): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      (HAS_NEWLINE.test(comment.value) ||\n        HAS_BlOCK_COMMENT_END.test(comment.value))\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n\n        let indentSize = this.format.retainLines\n          ? 0\n          : this._buf.getCurrentColumn();\n\n        if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n          indentSize += this._getIndent();\n        }\n\n        val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid creating //* comments\n    if (this.endsWith(charCodes.slash)) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine != commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumDeclaration(parent);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n              ? COMMENT_SKIP_NEWLINE.TRAILING\n              : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAaA,IAAAG,kBAAA,GAAAH,OAAA;AAA4D;EAZ1DI,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAmB,IAAAN,EAAA;AAarB,MAAMO,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,mBAAmB,GAAG,SAAS;AACrC,MAAMC,kBAAkB,GAAG,sBAAsB;AACjD,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,qBAAqB,GAAG,MAAM;AAEpC,MAAM;EAAEC;AAAY,CAAC,GAAGd,CAAC;AA8EzB,MAAMe,OAAO,CAAC;EACZC,WAAWA,CAACC,MAAc,EAAEC,GAAc,EAAE;IAAA,KAY5CC,yBAAyB,GAAW,CAAC;IAAA,KAGrCC,WAAW,GAAkB,EAAE;IAAA,KAC/BC,OAAO,GAAW,CAAC;IAAA,KACnBC,WAAW,GAAW,CAAC;IAAA,KACvBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,sBAAsB,GAAgC,IAAI;IAAA,KAC1DC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IA3BlC,IAAI,CAACjB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkB,IAAI,GAAG,IAAIC,eAAM,CAAClB,GAAG,CAAC;IAE3B,IAAI,CAACI,WAAW,GAAGL,MAAM,CAACoB,MAAM,CAACC,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC;IACpD,IAAI,CAAChB,aAAa,GAAGN,MAAM,CAACoB,MAAM,CAACC,KAAK,CAACE,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAGvB,GAAG,oBAAHA,GAAG,CAAEuB,SAAS;EACjC;EAsBAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IACf,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAC,CAAC;EACxB;EAMAT,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACpB,MAAM,CAAC8B,OAAO,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;IAEhD,IAAI,CAAC3B,OAAO,EAAE;EAChB;EAMA4B,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAAChC,MAAM,CAAC8B,OAAO,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;IAEhD,IAAI,CAAC3B,OAAO,EAAE;EAChB;EAMA6B,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,GAAoB,CAAC;IAClC;IACA,IAAI,CAAC3B,iBAAiB,GAAG,KAAK;EAChC;EAMA4B,UAAUA,CAACC,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAACtC,MAAM,CAACuC,QAAQ,EAAE;MACxB,IAAI,CAACrB,IAAI,CAACsB,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACN,IAAY,EAAQ;IAC9B,IAAI,CAACG,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAACX,KAAc,GAAG,KAAK,EAAQ;IAClC,IAAI,IAAI,CAAClC,MAAM,CAAC8B,OAAO,EAAE;IAEzB,IAAII,KAAK,EAAE;MACT,IAAI,CAACY,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAAC5B,IAAI,CAAC6B,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAG/B,IACE,IAAI,CAACvC,aAAa,IACjBqC,GAAG,CAAC7B,UAAU,CAAC,CAAC,CAAC,OAAoB,IAAI,IAAI,CAACgC,QAAQ,GAAgB,CAAE,EACzE;MACA,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACrC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACL,iBAAiB,GAAG2C,qBAAqB;EAChD;EAMAI,MAAMA,CAACL,GAAW,EAAQ;IACxB,IAAI,CAACD,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAACtC,gBAAgB,GACnB4C,MAAM,CAACC,SAAS,CAAC,CAACP,GAAG,CAAC,IACtB,CAAC1D,mBAAmB,CAACkE,IAAI,CAACR,GAAG,CAAC,IAC9B,CAAC5D,mBAAmB,CAACoE,IAAI,CAACR,GAAG,CAAC,IAC9B,CAAC3D,oBAAoB,CAACmE,IAAI,CAACR,GAAG,CAAC,IAC/BA,GAAG,CAAC7B,UAAU,CAAC6B,GAAG,CAAC5B,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAKAoB,KAAKA,CAACQ,GAAW,EAAES,YAAY,GAAG,KAAK,EAAQ;IAC7C,IAAI,CAACP,wBAAwB,CAAC,CAAC;IAE/B,MAAMQ,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAAC,CAAC;IACnC,MAAMa,QAAQ,GAAGX,GAAG,CAAC7B,UAAU,CAAC,CAAC,CAAC;IAClC,IACGuC,QAAQ,OAA8B,KAGpCV,GAAG,KAAK,IAAI,IAEXW,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAACjD,gBAAiB,EACrD;MACA,IAAI,CAACiC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAES,YAAY,CAAC;IAC/B,IAAI,CAACnD,iBAAiB,GAAG,KAAK;EAChC;EAEAsD,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAACX,wBAAwB,CAAC,CAAC;IAE/B,MAAMQ,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAAC,CAAC;IACnC,IAEGe,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAACnD,gBAAiB,EACjD;MACA,IAAI,CAACiC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACO,WAAW,CAAC6B,IAAI,CAAC;IACtB,IAAI,CAACvD,iBAAiB,GAAG,KAAK;EAChC;EAQAwD,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAEhC,KAAe,EAAQ;IAC5C,IAAIgC,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAChC,KAAK,EAAE;MACV,IAAI,IAAI,CAAClC,MAAM,CAACmE,WAAW,IAAI,IAAI,CAACnE,MAAM,CAAC8B,OAAO,EAAE;MAEpD,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;QACvB,IAAI,CAACc,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAChD,IAAI,CAACkD,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAhB,QAAQA,CAACU,IAAY,EAAW;IAC9B,OAAO,IAAI,CAACf,WAAW,CAAC,CAAC,KAAKe,IAAI;EACpC;EAEAf,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAAC/B,IAAI,CAAC+B,WAAW,CAAC,CAAC;EAChC;EAEAsB,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACrD,IAAI,CAACqD,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAACtD,IAAI,CAACsD,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAAC/B,GAAoB,EAAEgC,EAAc,EAAE;IAChD,IAAI,CAAChC,GAAG,EAAE;MACRgC,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAEjC,GAAG,CAAC;IAE3B,IAAI,CAACxB,IAAI,CAACuD,WAAW,CAAC/B,GAAG,EAAEgC,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAEnC,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAACiC,QAAQ,CAACE,IAAI,EAAEnC,GAAG,CAAC;IAExB,IAAI,CAACxB,IAAI,CAAC0D,MAAM,CAACC,IAAI,EAAEnC,GAAG,CAAC;EAC7B;EAEAD,gBAAgBA,CACdoC,IAAqB,EACrBnC,GAAoB,EACpBoC,YAAoB,EACd;IACN,IAAI,CAACpC,GAAG,EAAE;IAEV,IAAI,CAACiC,QAAQ,CAACE,IAAI,EAAEnC,GAAG,CAAC;IAExB,IAAI,CAACxB,IAAI,CAACuB,gBAAgB,CAACoC,IAAI,EAAEnC,GAAG,EAAEoC,YAAY,CAAC;EACrD;EAEAC,UAAUA,CACRF,IAAqB,EACrBnC,GAAoB,EACpBgC,EAAc,EACR;IACN,IAAI,CAAChC,GAAG,EAAE;MACRgC,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAACE,IAAI,EAAEnC,GAAG,CAAC;IAExB,IAAI,CAACxB,IAAI,CAAC6D,UAAU,CAACF,IAAI,EAAEnC,GAAG,EAAEgC,EAAE,CAAC;EACrC;EAEAM,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAChE,IAAI,CAACiE,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAAClE,IAAI,CAACmE,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEAnC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACV,MAAM,GAAgB,CAAC;EAC9B;EAEAkC,QAAQA,CAAA,EAAS;IACf,IAAI,CAAClC,MAAM,GAAmB,CAAC;EACjC;EAEAmB,OAAOA,CAACJ,GAAW,EAAES,YAAqB,EAAQ;IAChD,IAAI,CAAC2B,cAAc,CAACpC,GAAG,CAAC;IACxB,IAAI,CAACqC,YAAY,CAACrC,GAAG,CAAC7B,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAACJ,IAAI,CAACuE,MAAM,CAACtC,GAAG,EAAES,YAAY,CAAC;IAEnC,IAAI,CAAC9C,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAsB,WAAWA,CAAC6B,IAAY,EAAQ;IAC9B,IAAI,CAAC0B,kBAAkB,CAAC1B,IAAI,CAAC;IAC7B,IAAI,CAACwB,YAAY,CAACxB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAACyE,UAAU,CAAC3B,IAAI,CAAC;IAE1B,IAAI,CAAClD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAuB,MAAMA,CAAC4B,IAAY,EAAE;IACnB,IAAI,CAAC0B,kBAAkB,CAAC1B,IAAI,CAAC;IAC7B,IAAI,CAACwB,YAAY,CAACxB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAAC0E,KAAK,CAAC5B,IAAI,CAAC;IAErB,IAAI,CAAClD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEA2E,YAAYA,CAACK,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAACzF,OAAO,IACZyF,SAAS,OAAuB,IAChC,IAAI,CAACvC,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAACpC,IAAI,CAAC4E,gBAAgB,CAAC,IAAI,CAACzF,WAAW,EAAE,IAAI,CAAC0F,UAAU,CAAC,CAAC,CAAC;IACjE;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAACzF,OAAO,IACZyF,SAAS,OAAuB,IAChC,IAAI,CAACvC,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAoC,kBAAkBA,CAAC1B,IAAY,EAAQ;IAErC,MAAMiC,qBAAqB,GAAG,IAAI,CAACzF,sBAAsB;IACzD,IAAI,CAACyF,qBAAqB,EAAE;IAS5B,IAAIjC,IAAI,OAAoB,EAAE;MAE5B;IACF;IAGA,IAAIA,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAACxD,sBAAsB,GAAG,IAAI;MAClC;IACF;IAEA,IAAI,CAACmC,SAAK,GAAI,CAAC;IACf,IAAI,CAACvB,MAAM,CAAC,CAAC;IACb6E,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAX,cAAcA,CAACpC,GAAW,EAAQ;IAEhC,MAAM8C,qBAAqB,GAAG,IAAI,CAACzF,sBAAsB;IACzD,IAAI,CAACyF,qBAAqB,EAAE;IAS5B,MAAME,GAAG,GAAGhD,GAAG,CAAC5B,MAAM;IAEtB,IAAI2C,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,IAAIhD,GAAG,CAAC7B,UAAU,CAAC4C,CAAC,CAAC,OAAoB,EAAEA,CAAC,EAAE,EAAE;IACnE,IAAIA,CAAC,KAAKiC,GAAG,EAAE;MAEb;IACF;IAGA,MAAMC,GAAG,GAAGjD,GAAG,CAAC7B,UAAU,CAAC4C,CAAC,CAAC;IAC7B,IAAIkC,GAAG,OAAuB,EAAE;MAC9B,IAEEA,GAAG,OAAoB,IAEvBlC,CAAC,GAAG,CAAC,KAAKiC,GAAG,EACb;QAEA,IAAI,CAAC3F,sBAAsB,GAAG,IAAI;QAClC;MACF;MAEA,MAAM6F,OAAO,GAAGlD,GAAG,CAAC7B,UAAU,CAAC4C,CAAC,GAAG,CAAC,CAAC;MAErC,IAAImC,OAAO,OAAuB,EAAE;QAGlC,IAAI3G,kBAAkB,CAACiE,IAAI,CAACR,GAAG,CAACmD,KAAK,CAACpC,CAAC,GAAG,CAAC,EAAEiC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;UAKtD;QACF;MAGF,CAAC,MAAM,IAAIE,OAAO,OAAoB,EAAE;QAGtC,IAAI,CAAC7F,sBAAsB,GAAG,IAAI;QAClC;MACF;IACF;IAEA,IAAI,CAACmC,SAAK,GAAI,CAAC;IACf,IAAI,CAACvB,MAAM,CAAC,CAAC;IACb6E,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAK,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAACxG,MAAM,CAACmE,WAAW,EAAE;IAG9B,MAAMsC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACtF,IAAI,CAACwF,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,KAAK,EAAEvC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAK,QAAQA,CAACE,IAAqB,EAAEnC,GAAS,EAAE;IAAA,IAAAiE,SAAA;IACzC,IAAI,CAAC,IAAI,CAAC3G,MAAM,CAACmE,WAAW,EAAE;IAG9B,MAAMqC,IAAI,GAAG9D,GAAG,aAAAiE,SAAA,GAAHjE,GAAG,CAAGmC,IAAI,CAAC,qBAAX8B,SAAA,CAAaH,IAAI;IAC9B,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAMC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACtF,IAAI,CAACwF,cAAc,CAAC,CAAC;MAE/C,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,KAAK,EAAEvC,CAAC,EAAE,EAAE;QAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;MACjB;IACF;EACF;EAMAyB,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACzF,aAAa,GAAG,IAAI,CAACF,OAAO;EAC1C;EAEAwG,mBAAmBA,CAACtE,IAAY,EAAEuE,MAAc,EAAEC,OAAgB,EAAE;IAgBlE,IAAIA,OAAO,EAAE;MACX,IAAI,CAACrG,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACkB,KAAK,CAACW,IAAI,EAAEuE,MAAM,CAAC;IAC1B,CAAC,MAAM;MACL,MAAME,eAAe,GAAG;QACtBb,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAAC1F,sBAAsB,GAAGuG,eAAe;MAC7C,IAAI,CAACpF,KAAK,CAACW,IAAI,EAAEuE,MAAM,CAAC;MAIxB,IAAIE,eAAe,CAACb,OAAO,EAAE;QAC3B,IAAI,CAAClE,MAAM,CAAC,CAAC;QACb,IAAI,CAACiC,OAAO,CAAC,CAAC;QACd,IAAI,CAACtB,SAAK,GAAI,CAAC;MACjB;IACF;EACF;EAEAhB,KAAKA,CACHW,IAAmB,EACnBuE,MAAe,EACfzD,qBAA+B,EAG/B4D,0BAAmC,EACnCC,WAAqB,EACrB;IAAA,IAAAC,WAAA;IACA,IAAI,CAAC5E,IAAI,EAAE;IAEX,IAAI,CAACtB,iBAAiB,GAAG,KAAK;IAE9B,MAAMmG,QAAQ,GAAG7E,IAAI,CAAC8E,IAAI;IAC1B,MAAMpH,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAMqH,UAAU,GAAGrH,MAAM,CAAC+B,OAAO;IACjC,IAEEO,IAAI,CAACgF,QAAQ,EACb;MACAtH,MAAM,CAAC+B,OAAO,GAAG,IAAI;IACvB;IAEA,MAAMwF,WAAW,GACf,IAAI,CACFJ,QAAQ,CAOT;IACH,IAAII,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACrB,wBAAuBC,IAAI,CAACC,SAAS,CACpCR,QACF,CAAE,qBAAoBO,IAAI,CAACC,SAAS,CAACrF,IAAI,CAACvC,WAAW,CAAC6H,IAAI,CAAE,EAC9D,CAAC;IACH;IAEA,IAAI,CAACzH,WAAW,CAAC0H,IAAI,CAACvF,IAAI,CAAC;IAE3B,MAAMwF,QAAQ,GAAG,IAAI,CAACvH,UAAU;IAChC,IAAI,CAACA,UAAU,GAAG+B,IAAI,CAACI,GAAG,IAAI8E,SAAS;IACvC,IAAI,CAAC5F,mBAAmB,CAAC,IAAI,CAACrB,UAAU,IAAI,CAACuH,QAAQ,CAAC;IAEtD,MAAMC,iBAAiB,GACrBd,WAAW,IACVjH,MAAM,CAACgI,oBAAoB,IAC1Bb,QAAQ,KAAK,oBAAoB,MAAAD,WAAA,GACjC5E,IAAI,CAAC2F,KAAK,qBAAVf,WAAA,CAAYgB,aAAa,CAAC,IAC5BrI,WAAW,CAACyC,IAAI,EAAEuE,MAAM,EAAE,IAAI,CAAC1G,WAAW,CAAC;IAE7C,IAAI4H,iBAAiB,EAAE;MACrB,IAAI,CAACpF,SAAK,GAAI,CAAC;MACf,IAAI,CAAC3B,iBAAiB,GAAG,KAAK;IAChC;IAEA,IAAI,CAACD,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAACoH,qBAAqB,CAAC7F,IAAI,EAAEuE,MAAM,CAAC;IAExC,MAAMnE,GAAG,GAAGyE,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAG7E,IAAI,CAACI,GAAG;IAE3E,IAAI,CAAC+B,WAAW,CAAC/B,GAAG,EAAE6E,WAAW,CAACa,IAAI,CAAC,IAAI,EAAE9F,IAAI,EAAEuE,MAAM,CAAC,CAAC;IAE3D,IAAIkB,iBAAiB,EAAE;MACrB,IAAI,CAACM,sBAAsB,CAAC/F,IAAI,EAAEuE,MAAM,CAAC;MACzC,IAAI,CAAClE,SAAK,GAAI,CAAC;MACf,IAAI,CAAClC,iBAAiB,GAAG2C,qBAAqB;IAChD,CAAC,MAAM,IAAIA,qBAAqB,IAAI,CAAC,IAAI,CAAC3C,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAC4H,sBAAsB,CAAC/F,IAAI,EAAEuE,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACwB,sBAAsB,CAAC/F,IAAI,EAAEuE,MAAM,EAAEG,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAAC7G,WAAW,CAACmI,GAAG,CAAC,CAAC;IAEtBtI,MAAM,CAAC+B,OAAO,GAAGsF,UAAU;IAC3B,IAAI,CAAC9G,UAAU,GAAGuH,QAAQ;IAE1B,IAAI,CAAC9G,iBAAiB,GAAG,KAAK;EAChC;EAEAY,mBAAmBA,CAAC2G,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACjI,UAAU,EAAE,IAAI,CAACkI,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC9H,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAMgI,OAAO,GAAG,IAAI,CAAC1I,MAAM,CAAC2I,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExB,IAAI,EAAE,cAAc;QACpByB,KAAK,EAAEH;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC/H,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAMgI,OAAO,GAAG,IAAI,CAAC1I,MAAM,CAAC8I,qBAAqB;IACjD,IAAIJ,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExB,IAAI,EAAE,cAAc;QACpByB,KAAK,EAAEH;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAK,cAAcA,CACZzG,IAMa,EACO;IACpB,MAAM2F,KAAK,GAAG3F,IAAI,CAAC2F,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEe,GAAG,KAAI,IAAI,IAClBf,KAAK,CAACgB,QAAQ,IAAI,IAAI,IACtB3G,IAAI,CAACuG,KAAK,KAAKZ,KAAK,CAACgB,QAAQ,EAC7B;MAEA,OAAOhB,KAAK,CAACe,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCtC,MAAc,EACduC,IAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI,EAACD,KAAK,YAALA,KAAK,CAAE5H,MAAM,GAAE;IAEpB,IAAI;MAAEH;IAAO,CAAC,GAAGgI,IAAI;IAErB,IAAIhI,MAAM,IAAI,IAAI,IAAI,IAAI,CAACpB,MAAM,CAACmE,WAAW,EAAE;MAAA,IAAAkF,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACzG,GAAG,qBAAZ2G,YAAA,CAAcE,KAAK,CAAC/C,IAAI;MAC1C,IAAI8C,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAACpI,IAAI,CAACwF,cAAc,CAAC,CAAC,EAAE;QACjEtF,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAMoI,WAA+B,GAAG;MACtCC,WAAW,EAAEL,IAAI,CAACK,WAAW;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,SAAS,GAAGP,IAAI,CAACO,SAAS,GAAGP,IAAI,CAACO,SAAS,CAACvB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;IAEnE,MAAMjC,GAAG,GAAGgD,KAAK,CAAC5H,MAAM;IACxB,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;MAC5B,MAAM5B,IAAI,GAAG6G,KAAK,CAACjF,CAAC,CAAC;MACrB,IAAI,CAAC5B,IAAI,EAAE;MAEX,IAAI8G,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC3F,CAAC,KAAK,CAAC,EAAEsF,WAAW,CAAC;MAE5D,IAAI,CAAC7H,KAAK,CAACW,IAAI,EAAEuE,MAAM,EAAEW,SAAS,EAAE4B,IAAI,CAACpC,0BAA0B,IAAI,CAAC,CAAC;MAEzEoC,IAAI,CAACU,QAAQ,oBAAbV,IAAI,CAACU,QAAQ,CAAGxH,IAAI,EAAE4B,CAAC,CAAC;MAExB,IAAIA,CAAC,GAAGiC,GAAG,GAAG,CAAC,EAAEwD,SAAS,oBAATA,SAAS,CAAG,CAAC;MAE9B,IAAIP,IAAI,CAACQ,SAAS,EAAE;QAClB,IAAI1F,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;UACjB,IAAI,CAAClC,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAA8F,aAAA;UACL,MAAMC,QAAQ,GAAGb,KAAK,CAACjF,CAAC,GAAG,CAAC,CAAC;UAC7BsF,WAAW,CAACE,iBAAiB,GAAG,EAAAK,aAAA,GAAAC,QAAQ,CAACtH,GAAG,qBAAZqH,aAAA,CAAcR,KAAK,CAAC/C,IAAI,KAAI,CAAC;UAE7D,IAAI,CAACqD,aAAa,CAAC,IAAI,EAAEL,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAIpI,MAAM,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC;EAC3B;EAEAiI,wBAAwBA,CAAC3H,IAAY,EAAEuE,MAAc,EAAE;IACrD,MAAMzF,MAAM,GAAGkB,IAAI,CAAC4H,eAAe,IAAI5H,IAAI,CAAC4H,eAAe,CAAC3I,MAAM,GAAG,CAAC;IACtE,IAAIH,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACO,KAAK,CAACW,IAAI,EAAEuE,MAAM,CAAC;IACxB,IAAIzF,MAAM,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC;EAC3B;EAEAmI,UAAUA,CAACtD,MAA8C,EAAE;IACzD,MAAMvE,IAAI,GAAGuE,MAAM,CAACuD,IAAI;IAExB,IAAI9H,IAAI,CAAC8E,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACvE,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAAClB,KAAK,CAACW,IAAI,EAAEuE,MAAM,CAAC;EAC1B;EAEAwB,sBAAsBA,CAAC/F,IAAY,EAAEuE,MAAe,EAAEwD,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAEC;IAAiB,CAAC,GAAGjI,IAAI;IAIhD,IAAIgI,aAAa,YAAbA,aAAa,CAAE/I,MAAM,EAAE;MACzB,IAAI,CAACiJ,cAAc,IAEjBF,aAAa,EACbhI,IAAI,EACJuE,MAAM,EACNwD,UACF,CAAC;IACH;IACA,IAAIE,gBAAgB,YAAhBA,gBAAgB,CAAEhJ,MAAM,EAAE;MAC5B,IAAI,CAACiJ,cAAc,IAEjBD,gBAAgB,EAChBjI,IAAI,EACJuE,MAAM,EACNwD,UACF,CAAC;IACH;EACF;EAEAlC,qBAAqBA,CAAC7F,IAAY,EAAEuE,MAAc,EAAE;IAClD,MAAM4D,QAAQ,GAAGnI,IAAI,CAAC4H,eAAe;IACrC,IAAI,EAACO,QAAQ,YAARA,QAAQ,CAAElJ,MAAM,GAAE;IACvB,IAAI,CAACiJ,cAAc,IAAuBC,QAAQ,EAAEnI,IAAI,EAAEuE,MAAM,CAAC;EACnE;EAEAxD,wBAAwBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACrC,iBAAiB,EAAE,IAAI,CAAC0J,kBAAkB,CAAC,CAAC;IACrD,IAAI,CAAC1J,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEAyJ,kBAAkBA,CAAA,EAAG;IACnB,MAAMpI,IAAI,GAAG,IAAI,CAACnC,WAAW,CAAC,IAAI,CAACA,WAAW,CAACoB,MAAM,GAAG,CAAC,CAAC;IAC1D,MAAMkJ,QAAQ,GAAGnI,IAAI,CAACgI,aAAa;IACnC,IAAI,EAACG,QAAQ,YAARA,QAAQ,CAAElJ,MAAM,GAAE;IAEvB,MAAMoJ,QAAQ,GAAG,IAAI,CAACrH,QAAQ,GAAgB,CAAC;IAC/C,MAAMlC,MAAM,GAAG,IAAI,CAACH,oBAAoB;IACxC,MAAM2J,oBAAoB,GAAG,IAAI,CAACjK,gBAAgB,CAACkK,IAAI;IACvD,IAAIzJ,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACoJ,cAAc,IAAqBC,QAAQ,EAAEnI,IAAI,CAAC;IACvD,IAAIqI,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAACjK,gBAAgB,CAACkK,IAAI,EAAE;MACnE,IAAI,CAAChI,KAAK,CAAC,CAAC;IACd;IACA,IAAIzB,MAAM,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC;EAC3B;EAEA8I,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAAC7J,oBAAoB,GAAG,KAAK;EACnC;EAEA8J,aAAaA,CACX5B,KAAe,EACftC,MAAc,EACduC,IAA0B,GAAG,CAAC,CAAC,EAC/B;IAAA,IAAA4B,YAAA;IACA5B,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,CAAAoB,YAAA,GAAA5B,IAAI,CAAChI,MAAM,YAAA4J,YAAA,GAAX5B,IAAI,CAAChI,MAAM,GAAK,KAAK;IACrB,IAAI,CAAC8H,SAAS,CAACC,KAAK,EAAEtC,MAAM,EAAEuC,IAAI,CAAC;EACrC;EAEA6B,SAASA,CAACC,KAAe,EAAErE,MAAc,EAAEuC,IAAsB,GAAG,CAAC,CAAC,EAAE;IACtE,IAAIA,IAAI,CAACO,SAAS,IAAI,IAAI,EAAE;MAC1BP,IAAI,CAACO,SAAS,GAAGwB,cAAc;IACjC;IAEA,IAAI,CAACjC,SAAS,CAACgC,KAAK,EAAErE,MAAM,EAAEuC,IAAI,CAAC;EACrC;EAEAS,aAAaA,CAACuB,OAAgB,EAAEhC,IAAwB,EAAE;IACxD,MAAMpJ,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAACmE,WAAW,IAAInE,MAAM,CAAC8B,OAAO,EAAE;IAI1C,IAAI9B,MAAM,CAAC+B,OAAO,EAAE;MAClB,IAAI,CAACc,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAACuI,OAAO,EAAE;MACZ;IACF;IAEA,MAAM9B,SAAS,GAAGF,IAAI,CAACM,iBAAiB;IACxC,MAAM2B,eAAe,GAAG,IAAI,CAACtK,gBAAgB;IAC7C,IAAIuI,SAAS,GAAG,CAAC,IAAI+B,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGhC,SAAS,GAAG+B,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAACrH,OAAO,CAACqH,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAACpK,IAAI,CAAC6B,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAsH,mBAAmBA,CAAC7C,OAAkB,EAAsB;IAG1D,IAAIA,OAAO,CAAC8C,MAAM,EAAE;IAEpB,IAAI,IAAI,CAAC7K,gBAAgB,CAAC8K,GAAG,CAAC/C,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAACjI,iBAAiB,KACrBd,WAAW,CAACgE,IAAI,CAAC+E,OAAO,CAACG,KAAK,CAAC,IAC9BjJ,qBAAqB,CAAC+D,IAAI,CAAC+E,OAAO,CAACG,KAAK,CAAC,CAAC,EAC5C;MACA;IACF;IAEA,IAAI,CAAClI,gBAAgB,CAAC+K,GAAG,CAAChD,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAAC1I,MAAM,CAAC2L,kBAAkB,CAACjD,OAAO,CAACG,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEAD,aAAaA,CAACF,OAAkB,EAAEkD,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAACpL,iBAAiB;IAC/C,MAAMqL,cAAc,GAAGpD,OAAO,CAACtB,IAAI,KAAK,cAAc;IAItD,MAAM2E,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAACnL,iBAAiB;IAEzB,IACEsL,aAAa,IACb,IAAI,CAAC7K,IAAI,CAAC6B,UAAU,CAAC,CAAC,IACtB6I,YAAY,MAAiC,EAC7C;MACA,IAAI,CAAC3H,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM+H,YAAY,GAAG,IAAI,CAAC/I,WAAW,CAAC,CAAC;IACvC,IACE+I,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,EACzC;MACA,IAAI,CAACnJ,KAAK,CAAC,CAAC;IACd;IAEA,IAAIoJ,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAI,KAAIvD,OAAO,CAACG,KAAM,IAAG;MAC5B,IAAI,IAAI,CAAC7I,MAAM,CAACoB,MAAM,CAAC8K,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMb,MAAM,IAAAa,YAAA,GAAGzD,OAAO,CAAChG,GAAG,qBAAXyJ,YAAA,CAAa5C,KAAK,CAAC6C,MAAM;QACxC,IAAId,MAAM,EAAE;UACV,MAAMe,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGhB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEW,GAAG,GAAGA,GAAG,CAACM,OAAO,CAACF,YAAY,EAAE,IAAI,CAAC;QACvC;QAEA,IAAIG,UAAU,GAAG,IAAI,CAACxM,MAAM,CAACmE,WAAW,GACpC,CAAC,GACD,IAAI,CAACjD,IAAI,CAACuL,gBAAgB,CAAC,CAAC;QAEhC,IAAI,IAAI,CAACzG,aAAa,GAAgB,CAAC,IAAI,IAAI,CAAChG,MAAM,CAACmE,WAAW,EAAE;UAClEqI,UAAU,IAAI,IAAI,CAACzG,UAAU,CAAC,CAAC;QACjC;QAEAkG,GAAG,GAAGA,GAAG,CAACM,OAAO,CAAC,UAAU,EAAG,KAAI,GAAG,CAACG,MAAM,CAACF,UAAU,CAAE,EAAC,CAAC;MAC9D;IACF,CAAC,MAAM,IAAI,CAACX,gBAAgB,EAAE;MAC5BI,GAAG,GAAI,KAAIvD,OAAO,CAACG,KAAM,EAAC;IAC5B,CAAC,MAAM;MAILoD,GAAG,GAAI,KAAIvD,OAAO,CAACG,KAAM,IAAG;IAC9B;IAGA,IAAI,IAAI,CAACvF,QAAQ,GAAgB,CAAC,EAAE,IAAI,CAACR,MAAM,CAAC,CAAC;IAEjD,IAAI,CAAC8B,MAAM,CAAC,OAAO,EAAE8D,OAAO,CAAChG,GAAG,CAAC;IACjC,IAAI,CAACa,OAAO,CAAC0I,GAAG,EAAEH,cAAc,CAAC;IAEjC,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAAC5H,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI8H,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAAC3H,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAuG,cAAcA,CACZpD,IAAkB,EAClBqD,QAA8B,EAC9BnI,IAAY,EACZuE,MAAe,EACfwD,UAAkB,GAAG,CAAC,EACtB;IACA,MAAMsC,OAAO,GAAGrK,IAAI,CAACI,GAAG;IACxB,MAAMyD,GAAG,GAAGsE,QAAQ,CAAClJ,MAAM;IAC3B,IAAIqL,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAACpD,KAAK,CAAC/C,IAAI,GAAG,CAAC;IACrD,MAAMsG,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACI,GAAG,CAACvG,IAAI,GAAG,CAAC;IACjD,IAAIwG,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMrJ,YAAY,GAAG,IAAI,CAACnD,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAACwD,OAAO,CAACmE,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;MAC5B,MAAMwE,OAAO,GAAG+B,QAAQ,CAACvG,CAAC,CAAC;MAE3B,MAAMgJ,WAAW,GAAG,IAAI,CAAC3B,mBAAmB,CAAC7C,OAAO,CAAC;MACrD,IAAIwE,WAAW,MAA6B,EAAE;QAC5CN,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIlE,OAAO,CAAChG,GAAG,IAAIwK,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAGzE,OAAO,CAAChG,GAAG,CAAC6G,KAAK,CAAC/C,IAAI;QAC/C,MAAM4G,cAAc,GAAG1E,OAAO,CAAChG,GAAG,CAACqK,GAAG,CAACvG,IAAI;QAC3C,IAAIY,IAAI,MAAyB,EAAE;UACjC,IAAIkE,MAAM,GAAG,CAAC;UACd,IAAIpH,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAChD,IAAI,CAAC6B,UAAU,CAAC,CAAC,KACrB2F,OAAO,CAACtB,IAAI,KAAK,aAAa,IAC7B+F,gBAAgB,IAAIC,cAAc,CAAC,EACrC;cACA9B,MAAM,GAAG2B,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACL3B,MAAM,GAAG6B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBxJ,YAAY,CAAC0H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAIxE,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;YACjBvC,YAAY,CACVyJ,IAAI,CAACC,GAAG,CAACT,aAAa,GAAGG,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGH,aAAa;UAC1B;QACF,CAAC,MAAM,IAAIzF,IAAI,MAAuB,EAAE;UACtC,MAAMkE,MAAM,GACV6B,gBAAgB,IAAIjJ,CAAC,KAAK,CAAC,GAAG2I,aAAa,GAAGG,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBxJ,YAAY,CAAC0H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAIxE,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;YACjBvC,YAAY,CAACyJ,IAAI,CAACE,GAAG,CAAC,CAAC,EAAET,WAAW,GAAGE,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGF,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMxB,MAAM,GACV6B,gBAAgB,IAAIjJ,CAAC,KAAK,CAAC,GAAG4I,WAAW,GAAGzC,UAAU,GAAG2C,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBxJ,YAAY,CAAC0H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLkE,MAAM,GAAG,KAAK;QACd,IAAIM,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAI/G,GAAG,KAAK,CAAC,EAAE;UACb,MAAMqH,UAAU,GAAG9E,OAAO,CAAChG,GAAG,GAC1BgG,OAAO,CAAChG,GAAG,CAAC6G,KAAK,CAAC/C,IAAI,KAAKkC,OAAO,CAAChG,GAAG,CAACqK,GAAG,CAACvG,IAAI,GAC/C,CAAC7G,WAAW,CAACgE,IAAI,CAAC+E,OAAO,CAACG,KAAK,CAAC;UAEpC,MAAM4E,iBAAiB,GACrBD,UAAU,IACV,CAACrO,WAAW,CAACmD,IAAI,CAAC,IAClB,CAAClD,WAAW,CAACyH,MAAM,CAAC,IACpB,CAACxH,iBAAiB,CAACwH,MAAM,CAAC,IAC1B,CAACvH,mBAAmB,CAACuH,MAAM,CAAC;UAE9B,IAAIO,IAAI,MAAyB,EAAE;YACjC,IAAI,CAACwB,aAAa,CAChBF,OAAO,EACN+E,iBAAiB,IAAInL,IAAI,CAAC8E,IAAI,KAAK,kBAAkB,IACnDoG,UAAU,IAAItO,UAAU,CAAC2H,MAAM,EAAE;cAAEuD,IAAI,EAAE9H;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAImL,iBAAiB,IAAIrG,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAACwB,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACLtB,IAAI,MAAuB,IAC3B,EAAE9E,IAAI,CAAC8E,IAAI,KAAK,kBAAkB,IAAI9E,IAAI,CAACoL,UAAU,CAACnM,MAAM,GAAG,CAAC,CAAC,IACjEe,IAAI,CAAC8E,IAAI,KAAK,WAAW,IACzB9E,IAAI,CAAC8E,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAACwB,aAAa,CAChBF,OAAO,EACPxE,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAKiC,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACyC,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAItB,IAAI,MAA0B,IAAIwF,MAAM,IAAII,QAAQ,EAAE;MACxD,IAAI,CAACjM,gBAAgB,GAAGiM,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAAC9N,OAAO,CAAC+N,SAAS,EAAE5O,kBAAkB,CAAC;AAEjB;EAEjCa,OAAO,CAAC+N,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAgB,CAAC,CAAC;AAC1D;AAAC,IAAAC,QAAA,GAIcjO,OAAO;AAAAkO,OAAA,CAAAC,OAAA,GAAAF,QAAA;AAEtB,SAAS5C,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAACxI,SAAK,GAAI,CAAC;EACf,IAAI,CAACE,KAAK,CAAC,CAAC;AACd"}