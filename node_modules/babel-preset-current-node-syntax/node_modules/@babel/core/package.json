{"name": "@babel/core", "version": "7.12.3", "description": "Babel compiler core.", "main": "lib/index.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-core"}, "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}, "browser": {"./lib/config/files/index.js": "./lib/config/files/index-browser.js", "./lib/transform-file.js": "./lib/transform-file-browser.js", "./src/config/files/index.js": "./src/config/files/index-browser.js", "./src/transform-file.js": "./src/transform-file-browser.js"}, "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/generator": "^7.12.1", "@babel/helper-module-transforms": "^7.12.1", "@babel/helpers": "^7.12.1", "@babel/parser": "^7.12.3", "@babel/template": "^7.10.4", "@babel/traverse": "^7.12.1", "@babel/types": "^7.12.1", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.1", "json5": "^2.1.2", "lodash": "^4.17.19", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}, "devDependencies": {"@babel/helper-transform-fixture-test-runner": "^7.12.1"}}