{"version": 3, "names": ["_highlight", "require", "_chalk", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "chalkWithForcedColor", "undefined", "getChalk", "forceColor", "_chalkWithForcedColor", "chalk", "constructor", "enabled", "level", "deprecationWarningShown", "getDefs", "gutter", "grey", "marker", "red", "bold", "message", "NEWLINE", "getMarkerLines", "loc", "source", "opts", "startLoc", "assign", "column", "line", "start", "endLoc", "end", "linesAbove", "linesBelow", "startLine", "startColumn", "endLine", "endColumn", "Math", "max", "min", "length", "lineDiff", "markerLines", "i", "lineNumber", "sourceLength", "codeFrameColumns", "rawLines", "highlighted", "highlightCode", "should<PERSON><PERSON><PERSON>", "defs", "maybe<PERSON><PERSON><PERSON>", "chalkFn", "string", "lines", "split", "hasColumns", "numberMaxWidth", "String", "highlightedLines", "highlight", "frame", "slice", "map", "index", "number", "paddedNumber", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkerLine", "markerLine", "Array", "isArray", "markerSpacing", "replace", "numberOfMarkers", "repeat", "join", "reset", "_default", "colNumber", "process", "emitWarning", "deprecationError", "Error", "name", "console", "warn", "location"], "sources": ["../src/index.ts"], "sourcesContent": ["import highlight, { shouldHighlight } from \"@babel/highlight\";\n\nimport chalk, { Chalk as ChalkClass, type ChalkInstance as Chalk } from \"chalk\";\n\nlet chalkWithForcedColor: Chalk = undefined;\nfunction getChalk(forceColor: boolean) {\n  if (forceColor) {\n    chalkWithForcedColor ??= process.env.BABEL_8_BREAKING\n      ? new ChalkClass({ level: 1 })\n      : // @ts-expect-error .Instance was .constructor in chalk 2\n        new chalk.constructor({ enabled: true, level: 1 });\n    return chalkWithForcedColor;\n  }\n  return chalk;\n}\n\nlet deprecationWarningShown = false;\n\ntype Location = {\n  column: number;\n  line: number;\n};\n\ntype NodeLocation = {\n  end?: Location;\n  start: Location;\n};\n\nexport interface Options {\n  /** Syntax highlight the code as JavaScript for terminals. default: false */\n  highlightCode?: boolean;\n  /**  The number of lines to show above the error. default: 2 */\n  linesAbove?: number;\n  /**  The number of lines to show below the error. default: 3 */\n  linesBelow?: number;\n  /**\n   * Forcibly syntax highlight the code as JavaScript (for non-terminals);\n   * overrides highlightCode.\n   * default: false\n   */\n  forceColor?: boolean;\n  /**\n   * Pass in a string to be displayed inline (if possible) next to the\n   * highlighted location in the code. If it can't be positioned inline,\n   * it will be placed above the code frame.\n   * default: nothing\n   */\n  message?: string;\n}\n\n/**\n * Chalk styles for code frame token types.\n */\nfunction getDefs(chalk: Chalk) {\n  return {\n    gutter: chalk.grey,\n    marker: chalk.red.bold,\n    message: chalk.red.bold,\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * Extract what lines should be marked and highlighted.\n */\n\ntype MarkerLines = Record<number, true | [number, number]>;\n\nfunction getMarkerLines(\n  loc: NodeLocation,\n  source: Array<string>,\n  opts: Options,\n): {\n  start: number;\n  end: number;\n  markerLines: MarkerLines;\n} {\n  const startLoc: Location = {\n    column: 0,\n    line: -1,\n    ...loc.start,\n  };\n  const endLoc: Location = {\n    ...startLoc,\n    ...loc.end,\n  };\n  const { linesAbove = 2, linesBelow = 3 } = opts || {};\n  const startLine = startLoc.line;\n  const startColumn = startLoc.column;\n  const endLine = endLoc.line;\n  const endColumn = endLoc.column;\n\n  let start = Math.max(startLine - (linesAbove + 1), 0);\n  let end = Math.min(source.length, endLine + linesBelow);\n\n  if (startLine === -1) {\n    start = 0;\n  }\n\n  if (endLine === -1) {\n    end = source.length;\n  }\n\n  const lineDiff = endLine - startLine;\n  const markerLines: MarkerLines = {};\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine;\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true;\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length;\n\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn];\n      } else {\n        const sourceLength = source[lineNumber - i].length;\n\n        markerLines[lineNumber] = [0, sourceLength];\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0];\n      } else {\n        markerLines[startLine] = true;\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn];\n    }\n  }\n\n  return { start, end, markerLines };\n}\n\nexport function codeFrameColumns(\n  rawLines: string,\n  loc: NodeLocation,\n  opts: Options = {},\n): string {\n  const highlighted =\n    (opts.highlightCode || opts.forceColor) && shouldHighlight(opts);\n  const chalk = getChalk(opts.forceColor);\n  const defs = getDefs(chalk);\n  const maybeHighlight = (chalkFn: Chalk, string: string) => {\n    return highlighted ? chalkFn(string) : string;\n  };\n  const lines = rawLines.split(NEWLINE);\n  const { start, end, markerLines } = getMarkerLines(loc, lines, opts);\n  const hasColumns = loc.start && typeof loc.start.column === \"number\";\n\n  const numberMaxWidth = String(end).length;\n\n  const highlightedLines = highlighted ? highlight(rawLines, opts) : rawLines;\n\n  let frame = highlightedLines\n    .split(NEWLINE, end)\n    .slice(start, end)\n    .map((line, index) => {\n      const number = start + 1 + index;\n      const paddedNumber = ` ${number}`.slice(-numberMaxWidth);\n      const gutter = ` ${paddedNumber} |`;\n      const hasMarker = markerLines[number];\n      const lastMarkerLine = !markerLines[number + 1];\n      if (hasMarker) {\n        let markerLine = \"\";\n        if (Array.isArray(hasMarker)) {\n          const markerSpacing = line\n            .slice(0, Math.max(hasMarker[0] - 1, 0))\n            .replace(/[^\\t]/g, \" \");\n          const numberOfMarkers = hasMarker[1] || 1;\n\n          markerLine = [\n            \"\\n \",\n            maybeHighlight(defs.gutter, gutter.replace(/\\d/g, \" \")),\n            \" \",\n            markerSpacing,\n            maybeHighlight(defs.marker, \"^\").repeat(numberOfMarkers),\n          ].join(\"\");\n\n          if (lastMarkerLine && opts.message) {\n            markerLine += \" \" + maybeHighlight(defs.message, opts.message);\n          }\n        }\n        return [\n          maybeHighlight(defs.marker, \">\"),\n          maybeHighlight(defs.gutter, gutter),\n          line.length > 0 ? ` ${line}` : \"\",\n          markerLine,\n        ].join(\"\");\n      } else {\n        return ` ${maybeHighlight(defs.gutter, gutter)}${\n          line.length > 0 ? ` ${line}` : \"\"\n        }`;\n      }\n    })\n    .join(\"\\n\");\n\n  if (opts.message && !hasColumns) {\n    frame = `${\" \".repeat(numberMaxWidth + 1)}${opts.message}\\n${frame}`;\n  }\n\n  if (highlighted) {\n    return chalk.reset(frame);\n  } else {\n    return frame;\n  }\n}\n\n/**\n * Create a code frame, adding line numbers, code highlighting, and pointing to a given position.\n */\n\nexport default function (\n  rawLines: string,\n  lineNumber: number,\n  colNumber?: number | null,\n  opts: Options = {},\n): string {\n  if (!deprecationWarningShown) {\n    deprecationWarningShown = true;\n\n    const message =\n      \"Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.\";\n\n    if (process.emitWarning) {\n      // A string is directly supplied to emitWarning, because when supplying an\n      // Error object node throws in the tests because of different contexts\n      process.emitWarning(message, \"DeprecationWarning\");\n    } else {\n      const deprecationError = new Error(message);\n      deprecationError.name = \"DeprecationWarning\";\n      console.warn(new Error(message));\n    }\n  }\n\n  colNumber = Math.max(colNumber, 0);\n\n  const location: NodeLocation = {\n    start: { column: colNumber, line: lineNumber },\n  };\n\n  return codeFrameColumns(rawLines, location, opts);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAgF,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAEhF,IAAIW,oBAA2B,GAAGC,SAAS;AAC3C,SAASC,QAAQA,CAACC,UAAmB,EAAE;EACrC,IAAIA,UAAU,EAAE;IAAA,IAAAC,qBAAA;IACd,CAAAA,qBAAA,GAAAJ,oBAAoB,YAAAI,qBAAA,GAApBJ,oBAAoB,GAGhB,IAAIK,cAAK,CAACC,WAAW,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IACtD,OAAOR,oBAAoB;EAC7B;EACA,OAAOK,cAAK;AACd;AAEA,IAAII,uBAAuB,GAAG,KAAK;AAqCnC,SAASC,OAAOA,CAACL,KAAY,EAAE;EAC7B,OAAO;IACLM,MAAM,EAAEN,KAAK,CAACO,IAAI;IAClBC,MAAM,EAAER,KAAK,CAACS,GAAG,CAACC,IAAI;IACtBC,OAAO,EAAEX,KAAK,CAACS,GAAG,CAACC;EACrB,CAAC;AACH;AAMA,MAAME,OAAO,GAAG,yBAAyB;AAQzC,SAASC,cAAcA,CACrBC,GAAiB,EACjBC,MAAqB,EACrBC,IAAa,EAKb;EACA,MAAMC,QAAkB,GAAA/B,MAAA,CAAAgC,MAAA;IACtBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;EAAC,GACLN,GAAG,CAACO,KAAK,CACb;EACD,MAAMC,MAAgB,GAAApC,MAAA,CAAAgC,MAAA,KACjBD,QAAQ,EACRH,GAAG,CAACS,GAAG,CACX;EACD,MAAM;IAAEC,UAAU,GAAG,CAAC;IAAEC,UAAU,GAAG;EAAE,CAAC,GAAGT,IAAI,IAAI,CAAC,CAAC;EACrD,MAAMU,SAAS,GAAGT,QAAQ,CAACG,IAAI;EAC/B,MAAMO,WAAW,GAAGV,QAAQ,CAACE,MAAM;EACnC,MAAMS,OAAO,GAAGN,MAAM,CAACF,IAAI;EAC3B,MAAMS,SAAS,GAAGP,MAAM,CAACH,MAAM;EAE/B,IAAIE,KAAK,GAAGS,IAAI,CAACC,GAAG,CAACL,SAAS,IAAIF,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACrD,IAAID,GAAG,GAAGO,IAAI,CAACE,GAAG,CAACjB,MAAM,CAACkB,MAAM,EAAEL,OAAO,GAAGH,UAAU,CAAC;EAEvD,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBL,KAAK,GAAG,CAAC;EACX;EAEA,IAAIO,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBL,GAAG,GAAGR,MAAM,CAACkB,MAAM;EACrB;EAEA,MAAMC,QAAQ,GAAGN,OAAO,GAAGF,SAAS;EACpC,MAAMS,WAAwB,GAAG,CAAC,CAAC;EAEnC,IAAID,QAAQ,EAAE;IACZ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,QAAQ,EAAEE,CAAC,EAAE,EAAE;MAClC,MAAMC,UAAU,GAAGD,CAAC,GAAGV,SAAS;MAEhC,IAAI,CAACC,WAAW,EAAE;QAChBQ,WAAW,CAACE,UAAU,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAID,CAAC,KAAK,CAAC,EAAE;QAClB,MAAME,YAAY,GAAGvB,MAAM,CAACsB,UAAU,GAAG,CAAC,CAAC,CAACJ,MAAM;QAElDE,WAAW,CAACE,UAAU,CAAC,GAAG,CAACV,WAAW,EAAEW,YAAY,GAAGX,WAAW,GAAG,CAAC,CAAC;MACzE,CAAC,MAAM,IAAIS,CAAC,KAAKF,QAAQ,EAAE;QACzBC,WAAW,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAER,SAAS,CAAC;MAC1C,CAAC,MAAM;QACL,MAAMS,YAAY,GAAGvB,MAAM,CAACsB,UAAU,GAAGD,CAAC,CAAC,CAACH,MAAM;QAElDE,WAAW,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAEC,YAAY,CAAC;MAC7C;IACF;EACF,CAAC,MAAM;IACL,IAAIX,WAAW,KAAKE,SAAS,EAAE;MAC7B,IAAIF,WAAW,EAAE;QACfQ,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAE,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLQ,WAAW,CAACT,SAAS,CAAC,GAAG,IAAI;MAC/B;IACF,CAAC,MAAM;MACLS,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAEE,SAAS,GAAGF,WAAW,CAAC;IACjE;EACF;EAEA,OAAO;IAAEN,KAAK;IAAEE,GAAG;IAAEY;EAAY,CAAC;AACpC;AAEO,SAASI,gBAAgBA,CAC9BC,QAAgB,EAChB1B,GAAiB,EACjBE,IAAa,GAAG,CAAC,CAAC,EACV;EACR,MAAMyB,WAAW,GACf,CAACzB,IAAI,CAAC0B,aAAa,IAAI1B,IAAI,CAAClB,UAAU,KAAK,IAAA6C,0BAAe,EAAC3B,IAAI,CAAC;EAClE,MAAMhB,KAAK,GAAGH,QAAQ,CAACmB,IAAI,CAAClB,UAAU,CAAC;EACvC,MAAM8C,IAAI,GAAGvC,OAAO,CAACL,KAAK,CAAC;EAC3B,MAAM6C,cAAc,GAAGA,CAACC,OAAc,EAAEC,MAAc,KAAK;IACzD,OAAON,WAAW,GAAGK,OAAO,CAACC,MAAM,CAAC,GAAGA,MAAM;EAC/C,CAAC;EACD,MAAMC,KAAK,GAAGR,QAAQ,CAACS,KAAK,CAACrC,OAAO,CAAC;EACrC,MAAM;IAAES,KAAK;IAAEE,GAAG;IAAEY;EAAY,CAAC,GAAGtB,cAAc,CAACC,GAAG,EAAEkC,KAAK,EAAEhC,IAAI,CAAC;EACpE,MAAMkC,UAAU,GAAGpC,GAAG,CAACO,KAAK,IAAI,OAAOP,GAAG,CAACO,KAAK,CAACF,MAAM,KAAK,QAAQ;EAEpE,MAAMgC,cAAc,GAAGC,MAAM,CAAC7B,GAAG,CAAC,CAACU,MAAM;EAEzC,MAAMoB,gBAAgB,GAAGZ,WAAW,GAAG,IAAAa,kBAAS,EAACd,QAAQ,EAAExB,IAAI,CAAC,GAAGwB,QAAQ;EAE3E,IAAIe,KAAK,GAAGF,gBAAgB,CACzBJ,KAAK,CAACrC,OAAO,EAAEW,GAAG,CAAC,CACnBiC,KAAK,CAACnC,KAAK,EAAEE,GAAG,CAAC,CACjBkC,GAAG,CAAC,CAACrC,IAAI,EAAEsC,KAAK,KAAK;IACpB,MAAMC,MAAM,GAAGtC,KAAK,GAAG,CAAC,GAAGqC,KAAK;IAChC,MAAME,YAAY,GAAI,IAAGD,MAAO,EAAC,CAACH,KAAK,CAAC,CAACL,cAAc,CAAC;IACxD,MAAM7C,MAAM,GAAI,IAAGsD,YAAa,IAAG;IACnC,MAAMC,SAAS,GAAG1B,WAAW,CAACwB,MAAM,CAAC;IACrC,MAAMG,cAAc,GAAG,CAAC3B,WAAW,CAACwB,MAAM,GAAG,CAAC,CAAC;IAC/C,IAAIE,SAAS,EAAE;MACb,IAAIE,UAAU,GAAG,EAAE;MACnB,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;QAC5B,MAAMK,aAAa,GAAG9C,IAAI,CACvBoC,KAAK,CAAC,CAAC,EAAE1B,IAAI,CAACC,GAAG,CAAC8B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CACvCM,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;QACzB,MAAMC,eAAe,GAAGP,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzCE,UAAU,GAAG,CACX,KAAK,EACLlB,cAAc,CAACD,IAAI,CAACtC,MAAM,EAAEA,MAAM,CAAC6D,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EACvD,GAAG,EACHD,aAAa,EACbrB,cAAc,CAACD,IAAI,CAACpC,MAAM,EAAE,GAAG,CAAC,CAAC6D,MAAM,CAACD,eAAe,CAAC,CACzD,CAACE,IAAI,CAAC,EAAE,CAAC;QAEV,IAAIR,cAAc,IAAI9C,IAAI,CAACL,OAAO,EAAE;UAClCoD,UAAU,IAAI,GAAG,GAAGlB,cAAc,CAACD,IAAI,CAACjC,OAAO,EAAEK,IAAI,CAACL,OAAO,CAAC;QAChE;MACF;MACA,OAAO,CACLkC,cAAc,CAACD,IAAI,CAACpC,MAAM,EAAE,GAAG,CAAC,EAChCqC,cAAc,CAACD,IAAI,CAACtC,MAAM,EAAEA,MAAM,CAAC,EACnCc,IAAI,CAACa,MAAM,GAAG,CAAC,GAAI,IAAGb,IAAK,EAAC,GAAG,EAAE,EACjC2C,UAAU,CACX,CAACO,IAAI,CAAC,EAAE,CAAC;IACZ,CAAC,MAAM;MACL,OAAQ,IAAGzB,cAAc,CAACD,IAAI,CAACtC,MAAM,EAAEA,MAAM,CAAE,GAC7Cc,IAAI,CAACa,MAAM,GAAG,CAAC,GAAI,IAAGb,IAAK,EAAC,GAAG,EAChC,EAAC;IACJ;EACF,CAAC,CAAC,CACDkD,IAAI,CAAC,IAAI,CAAC;EAEb,IAAItD,IAAI,CAACL,OAAO,IAAI,CAACuC,UAAU,EAAE;IAC/BK,KAAK,GAAI,GAAE,GAAG,CAACc,MAAM,CAAClB,cAAc,GAAG,CAAC,CAAE,GAAEnC,IAAI,CAACL,OAAQ,KAAI4C,KAAM,EAAC;EACtE;EAEA,IAAId,WAAW,EAAE;IACf,OAAOzC,KAAK,CAACuE,KAAK,CAAChB,KAAK,CAAC;EAC3B,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;AAMe,SAAAiB,SACbhC,QAAgB,EAChBH,UAAkB,EAClBoC,SAAyB,EACzBzD,IAAa,GAAG,CAAC,CAAC,EACV;EACR,IAAI,CAACZ,uBAAuB,EAAE;IAC5BA,uBAAuB,GAAG,IAAI;IAE9B,MAAMO,OAAO,GACX,qGAAqG;IAEvG,IAAI+D,OAAO,CAACC,WAAW,EAAE;MAGvBD,OAAO,CAACC,WAAW,CAAChE,OAAO,EAAE,oBAAoB,CAAC;IACpD,CAAC,MAAM;MACL,MAAMiE,gBAAgB,GAAG,IAAIC,KAAK,CAAClE,OAAO,CAAC;MAC3CiE,gBAAgB,CAACE,IAAI,GAAG,oBAAoB;MAC5CC,OAAO,CAACC,IAAI,CAAC,IAAIH,KAAK,CAAClE,OAAO,CAAC,CAAC;IAClC;EACF;EAEA8D,SAAS,GAAG3C,IAAI,CAACC,GAAG,CAAC0C,SAAS,EAAE,CAAC,CAAC;EAElC,MAAMQ,QAAsB,GAAG;IAC7B5D,KAAK,EAAE;MAAEF,MAAM,EAAEsD,SAAS;MAAErD,IAAI,EAAEiB;IAAW;EAC/C,CAAC;EAED,OAAOE,gBAAgB,CAACC,QAAQ,EAAEyC,QAAQ,EAAEjE,IAAI,CAAC;AACnD"}