import axios from 'axios';
import { applyFilters } from '@appmaker-xyz/core';
import { appmaker, appSettings, emitEvent } from '@appmaker-xyz/core';
import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { settings } from '@appmaker-xyz/app-config/newConfig';
import { getApiBaseUrl, getProjectId } from '@appmaker-xyz/core';
import { appStorageApi } from '@appmaker-xyz/core';
import { getFilterString, cleanIt } from '../helper/filterHelper';
import { Alert, Dimensions } from 'react-native';
import { getLocalAddress, setLocalAddress } from './LocalAddressHelper';
import { isEmpty } from 'lodash';
import { convertResponse, handlesToQuery } from './handleHelper';
import { appPluginStoreApi, getImageSize, analytics } from '@appmaker-xyz/core';
import {
  getMetaObjectQuery,
  MetaObjectArgs,
  MetaObjectsParams,
} from './shopify/metaobject';
import { FragmentRegistry } from './shopify/fragments';
import { getFragmentsFor } from './fragments';
import { multipleProductParams } from '../helper/analytics';
import { runTanstackQuery } from '../datasource/fetcher';
import { CurrentCartQuery, CurrentCartQueryVariables, useCurrentCartQuery } from '../datasource/api-react-query';
import { deleteAccount } from '../actions/helper/deleteAccount';

const gql = String.raw;
const COLLECTION_FRAGMENTS = {
  BASE: gql`
    fragment baseCollectionFragment on Collection {
      id
      title
      handle
      description
      image {
        url(transform: { maxWidth: 800 })
        width
        height
      }
    }
  `,
};
let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;

export function convertToNodes(response) {
  const { data } = response;
  const { nodes } = data.data;
  // return ;
  const filteredNodes = nodes.filter((v) => !!v);
  const finalNodes = filteredNodes.map((node) => {
    if (node?.node === undefined) {
      return { node };
    } else {
      return node;
    }
  });
  response.data.data.products = {
    edges: finalNodes,
    pageInfo: {
      hasNextPage: false,
      hasPreviousPage: false,
    },
  };
  return response;
}
function getCheckoutFromResponse(cartResponse) {
  const {
    data: { data: response },
  } = cartResponse;
  let cartData;
  if (response.node) {
    cartData = response.node;
  } else  if (response.checkoutCreate?.checkout) {
    cartData = response.checkoutCreate.checkout;
  } else if (response.checkoutDiscountCodeRemove?.checkout) {
    cartData = response.checkoutDiscountCodeRemove.checkout;
  } else if (response.checkoutDiscountCodeApplyV2?.checkout) {
    cartData = response.checkoutDiscountCodeApplyV2.checkout;
  } else if (response.checkoutAttributesUpdateV2?.checkout) {
    cartData = response.checkoutAttributesUpdateV2.checkout;
  }  else if (response.checkoutEmailUpdateV2?.checkout) {
    cartData = response.checkoutEmailUpdateV2.checkout;
  }  else if (response.checkoutShippingAddressUpdateV2?.checkout) {
    cartData = response.checkoutShippingAddressUpdateV2.checkout;
  }   else if (response.checkoutLineItemsRemove?.checkout) {
    cartData = response.checkoutLineItemsRemove.checkout;
  } else if (response.checkoutLineItemsUpdate?.checkout) {
    cartData = response.checkoutLineItemsUpdate.checkout;
  } else if (response.checkoutLineItemsAdd?.checkout) {
    cartData = response.checkoutLineItemsAdd.checkout;
  }
  return cartData;
}
// export function convertCartResponse(response) {
//   // return response;
//   if (response?.data?.data?.node?.lineItems?.edges) {
//     response.data.data.node.lineItems.edges =
//       response.data.data.node.lineItems.edges.filter((obj) => {
//         return obj.node.variant !== null;
//       });
//   }
//   return response;
// }

const cartFields = {
  checkoutUrl: true,
  createdAt: true,
  attributes: {
    key: true,
    value: true,
  },
  discountCodes: {
    applicable: true,
    code: true,
  },
  id: true,
  note: true,
  lines: {
    __args: {
      first: 250,
    },
    edges: {
      node: {
        attributes: {
          key: true,
          value: true,
        },
        discountAllocations: {
          discountedAmount: {
            amount: true,
            currencyCode: true,
          },
        },
        id: true,
        estimatedCost: {
          totalAmount: {
            amount: true,
            currencyCode: true,
          },
          subtotalAmount: {
            amount: true,
            currencyCode: true,
          },
        },
        merchandise: {
          __on: {
            __typeName: 'ProductVariant',
            id: true,
            sku: true,
            image: {
              src: {
                __aliasFor: 'url',
                __args: {
                  transform: {},
                },
              },
            },
            price: {
              amount: true,
              currencyCode: true,
            },
            product: {
              id: true,
              title: true,
            },
            title: true,
          },
        },
        quantity: true,
      },
    },
  },
  estimatedCost: {
    subtotalAmount: {
      amount: true,
      currencyCode: true,
    },
    totalAmount: {
      currencyCode: true,
      amount: true,
    },
    totalDutyAmount: {
      amount: true,
      currencyCode: true,
    },
    totalTaxAmount: {
      amount: true,
      currencyCode: true,
    },
  },
};

/*
          handle
          id
          title
          description
          onlineStoreUrl
          image {
            url(transform: {maxWidth: 800})
            width
            height
          }
*/
const collectionFields = {
  handle: true,
  id: true,
  title: true,
  description: true,
  image: {
    url: {
      __args: {
        transform: {
          maxWidth: 800,
        },
      },
    },
    width: true,
    height: true,
  },
};

const checkoutVarientFields = {
  variant: {
    id: true,
    sku: true,
    quantityAvailable: true,
    image: {
      url: {
        __args: {
          transform: {
            maxWidth: 250,
          },
        },
      },
    },
    product: {
      title: true,
      id: true,
      onlineStoreUrl: true,
      vendor: true,
      handle: true,
      productType: true,
      tags: true,
      product_sku: {
        __aliasFor: 'metafield',
        __args: {
          namespace: 'my_fields',
          key: 'sku',
        },
        value: true,
      },
    },
    compareAtPrice: {
      amount: true,
      currencyCode: true,
    },
    price: {
      amount: true,
      currencyCode: true,
    },
    title: true,
  },
};

function getCheckoutVarientFields() {
  const extraFields = appmaker.applyFilters(
    'shopify-gql-checkout-varient-extra-fields',
    checkoutVarientFields,
  );
  const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
  return fields;
}
function getCheckoutFields() {
  const checkoutFields = `
  id
  webUrl
  createdAt
  requiresShipping
  email
  note
  order {
    name
    id
    orderNumber
    phone
    email
    shippingAddress {
      firstName
      lastName
      name
      phone
    }
  }
  customAttributes {
    key
    value
  }
  shippingLine{
    handle
    price {
      amount
      currencyCode
    }
    title
  }
  shippingAddress {
    address1
    address2
    city
    company
    country
    countryCodeV2
    firstName
    id
    lastName
    latitude
    longitude
    name
    phone
    province
    provinceCode
    zip
  }
  lineItems(first: 250) {
    edges {
      node {
        id
        title
        customAttributes {
          key
          value
        }
        unitPrice {
          amount
          currencyCode
        }
        ${getCheckoutVarientFields()}
        quantity
        discountAllocations {
          allocatedAmount {
            amount
            currencyCode
          }
          discountApplication {
            ... on ScriptDiscountApplication {
              __typename
              title
              targetType
              targetSelection
              allocationMethod
              value {
                ... on MoneyV2 {
                  __typename
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  __typename
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              description
              allocationMethod
              targetSelection
              targetType
              title
              value {
                ... on MoneyV2 {
                  __typename
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  __typename
                  percentage
                }
              }
            }
            ... on DiscountCodeApplication {
              __typename
              allocationMethod
              applicable
              code
              targetSelection
              targetType
              value {
                ... on MoneyV2 {
                  __typename
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  __typename
                  percentage
                }
              }
            }
            ... on AutomaticDiscountApplication {
              __typename
              allocationMethod
              targetSelection
              targetType
              title
              value {
                ... on MoneyV2 {
                  __typename
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  __typename
                  percentage
                }
              }
            }
            allocationMethod
            targetSelection
            targetType
            value {
              ... on MoneyV2 {
                __typename
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                __typename
                percentage
              }
            }
          }
        }
      }
    }
  }
  discountApplications(first: 100) {
    edges {
      node {
        allocationMethod
        targetSelection
        targetType
        ... on AutomaticDiscountApplication {
          __typename
          allocationMethod
          targetSelection
          targetType
          title
          value {
            ... on MoneyV2 {
              __typename
              amount
              currencyCode
            }
          }
        }
        ... on DiscountCodeApplication {
          __typename
          allocationMethod
          applicable
          code
          targetSelection
          targetType
          value
        }
        value {
          ... on MoneyV2 {
            __typename
            amount
            currencyCode
          }
          ... on PricingPercentageValue {
            __typename
            percentage
          }
        }
        ... on ManualDiscountApplication {
          description
          value {
            ... on MoneyV2 {
              __typename
              amount
            }
          }
          title
        }
        ... on ScriptDiscountApplication {
          __typename
          title
          value {
            ... on MoneyV2 {
              __typename
              amount
              currencyCode
            }
          }
        }
      }
    }
  }
  appliedGiftCards {
    amountUsed {
      amount
      currencyCode
    }
    id
    lastCharacters
    presentmentAmountUsed {
      amount
      currencyCode
    }
    amountUsed {
      amount
      currencyCode
    }
    amountUsed {
      amount
      currencyCode
    }
    balance {
      amount
      currencyCode
    }
  }
  lineItemsSubtotalPrice {
    amount
    currencyCode
  }
  subtotalPrice {
    amount
    currencyCode
  }
  totalPrice {
    amount
    currencyCode
  }
  `;
  return checkoutFields;
}

const orderFields = `
              name
              orderNumber
              id
              processedAt
              financialStatus
              cancelReason
              fulfillmentStatus
              shippingAddress {
                id
                firstName
                lastName
                address1
                address2
                city
                country
                zip
              }
              totalPrice {
                amount
                currencyCode
              }
              successfulFulfillments {
                successfulFulfillments {
                fulfillmentLineItems(first: 50) {
                  nodes {
                    lineItem {
                      variant {
                        id
                        product {
                          id
                          vendor
                          productType
                        }
                      }
                    }
                  }
                }
                trackingCompany
                trackingInfo(first: 10) {
                  number
                  url
                }
              }
              currencyCode
              lineItems(first:200) {
                edges {
                  node {
                    currentQuantity
                    originalTotalPrice {
                      amount
                      currencyCode
                    }
                    customAttributes {
                      key
                      value
                    }
                    title
                    discountedTotalPrice {
                      amount
                      currencyCode
                    }
                    variant {
                      title
                      sku
                      price {
                        amount
                        currencyCode
                      }
                      image {
                        url(transform: {maxHeight: 300})
                      }
                      product {
                        id
                        vendor
                        productType
                      }
                    }
                    quantity
                  }
                }
              }
              id
`;

const getOrdersField = () => {
  const singleOrderProductFields = {
    id: true,
    vendor: true,
    productType: true,
  };
  const extraFieldsProducts = appmaker.applyFilters(
    'shopify-gql-order-product-extra-fields',
    singleOrderProductFields,
  );

  const orderDiscountApplications = {
    discountApplications: {
      __args: {
        first: 100,
      },
      edges: {
        node: {
          allocationMethod: true,
          targetSelection: true,
          targetType: true,
          __on: [
            {
              __typeName: 'AutomaticDiscountApplication',
              allocationMethod: true,
              targetSelection: true,
              targetType: true,
              title: true,
              value: {
                __on: [
                  {
                    __typeName: 'MoneyV2',
                    amount: true,
                    currencyCode: true,
                  },
                  {
                    __typeName: 'PricingPercentageValue',
                    percentage: true,
                  },
                ],
              },
            },
            {
              __typeName: 'DiscountCodeApplication',
              allocationMethod: true,
              applicable: true,
              code: true,
              targetSelection: true,
              targetType: true,
              value: {
                __on: [
                  {
                    __typeName: 'MoneyV2',
                    amount: true,
                    currencyCode: true,
                  },
                  {
                    __typeName: 'PricingPercentageValue',
                    percentage: true,
                  },
                ],
              },
            },
            {
              __typeName: 'ManualDiscountApplication',
              description: true,
              value: {
                __on: [
                  {
                    __typeName: 'MoneyV2',
                    amount: true,
                    currencyCode: true,
                  },
                  {
                    __typeName: 'PricingPercentageValue',
                    percentage: true,
                  },
                ],
              },
              title: true,
            },
            {
              __typeName: 'ScriptDiscountApplication',
              title: true,
              value: {
                __on: [
                  {
                    __typeName: 'MoneyV2',
                    amount: true,
                    currencyCode: true,
                  },
                  {
                    __typeName: 'PricingPercentageValue',
                    percentage: true,
                  },
                ],
              },
            },
          ],
        },
      },
    },
  };
  const orderFields = {
    orderNumber: true,
    name: true,
    id: true,
    processedAt: true,
    financialStatus: true,
    cancelReason: true,
    fulfillmentStatus: true,
    totalShippingPrice: {
      amount: true,
      currencyCode: true,
    },
    email: true,
    billingAddress: {
      firstName: true,
      lastName: true,
      address1: true,
      address2: true,
      phone: true,
      city: true,
      country: true,
      zip: true,
    },
    subtotalPrice: {
      amount: true,
      currencyCode: true,
    },
    totalTax: {
      amount: true,
      currencyCode: true,
    },
    shippingAddress: {
      id: true,
      firstName: true,
      lastName: true,
      address1: true,
      address2: true,
      phone: true,
      city: true,
      country: true,
      zip: true,
    },
    totalPrice: {
      amount: true,
      currencyCode: true,
    },
    successfulFulfillments: {
      __args: {
        first: 250,
      },
      fulfillmentLineItems: {
        __args: {
          first: 50,
        },
        nodes: {
          quantity: true,
          lineItem: {
            title: true,
            currentQuantity: true,
            quantity: true,
            discountedTotalPrice: {
              amount: true,
              currencyCode: true,
            },
            originalTotalPrice: {
              amount: true,
              currencyCode: true,
            },
            variant: {
              id: true,
              image: {
                url: {
                  __args: {
                    transform: {
                      maxHeight: 300,
                    },
                  },
                },
              },
              product: {
                ...extraFieldsProducts,
              },
            },
          },
        },
      },
      trackingCompany: true,
      trackingInfo: {
        __args: {
          first: 10,
        },
        number: true,
        url: true,
      },
    },
    currencyCode: true,
    ...orderDiscountApplications,
    lineItems: {
      __args: {
        first: 200,
      },
      edges: {
        node: {
          currentQuantity: true,
          originalTotalPrice: {
            amount: true,
            currencyCode: true,
          },
          customAttributes: {
            key: true,
            value: true,
          },
          title: true,
          discountedTotalPrice: {
            amount: true,
            currencyCode: true,
          },
          discountAllocations: {
            allocatedAmount: {
              amount: true,
              currencyCode: true,
            },
            discountApplication: {
              __on: [
                {
                  __typeName: 'ScriptDiscountApplication',
                  title: true,
                  value: {
                    __on: [
                      {
                        __typeName: 'MoneyV2',
                        amount: true,
                        currencyCode: true,
                      },
                    ],
                  },
                },
                {
                  __typeName: 'ManualDiscountApplication',
                  description: true,
                  value: {
                    __on: [
                      {
                        __typeName: 'MoneyV2',
                        amount: true,
                        currencyCode: true,
                      },
                    ],
                  },
                  title: true,
                },
                {
                  __typeName: 'DiscountCodeApplication',
                  code: true,
                  value: {
                    __on: [
                      {
                        __typeName: 'MoneyV2',
                        amount: true,
                        currencyCode: true,
                      },
                    ],
                  },
                },
                {
                  __typeName: 'AutomaticDiscountApplication',
                  title: true,
                  value: {
                    __on: [
                      {
                        __typeName: 'MoneyV2',
                        amount: true,
                        currencyCode: true,
                      },
                    ],
                  },
                },
              ],
            },
          },
          variant: {
            id: true,
            title: true,
            sku: true,
            price: {
              amount: true,
              currencyCode: true,
            },
            compareAtPrice: {
              amount: true,
              currencyCode: true,
            },
            image: {
              url: {
                __args: {
                  transform: {
                    maxHeight: 300,
                  },
                },
              },
            },
            product: {
              ...extraFieldsProducts,
            },
          },
          quantity: true,
        },
      },
    },
  };

  const additionalOrderFields = appmaker.applyFilters(
    'shopify-gql-order-item-extra-fields',
    orderFields,
  );
  const fields = jsonToGraphQLQuery(additionalOrderFields, { pretty: true });
  return fields;
};

const getSingleProductField = () => {
  const imageSize = getImageSize({ type: 'collection-page-product' });
  let defaultHeight = 511;
  let singleProductTransform = {
    ...(imageSize?.height && { maxHeight: imageSize.height }),
    ...(imageSize?.width && { maxWidth: imageSize.width }),
    ...(!imageSize?.height &&
      !imageSize?.width && { maxHeight: defaultHeight }),
  };

  return {
    id: true,
    title: true,
    tags: true,
    handle: true,
    totalInventory: true,
    description: true,
    descriptionHtml: true,
    onlineStoreUrl: true,
    productType: true,
    vendor: true,
    availableForSale: true,
    media: {
      __args: {
        first: 250,
      },
      edges: {
        node: {
          mediaContentType: true,
          __on: [
            {
              __typeName: 'Video',
              id: true,
              mediaContentType: true,
              sources: {
                url: true,
                height: true,
                format: true,
                width: true,
              },
              previewImage: {
                originalSrc: {
                  __aliasFor: 'url',
                  __args: {
                    transform: {},
                  },
                },
              },
            },
            {
              __typeName: 'MediaImage',
              id: true,
              mediaContentType: true,
              image: {
                height: true,
                id: true,
                // originalSrc: true,
                // src: true,
                originalSrc: {
                  __aliasFor: 'url',
                  __args: {
                    transform: {},
                  },
                },
                width: true,
                url: {
                  __args: {
                    transform: {
                      maxHeight: deviceRatio > 1.6 ? 500 : 800,
                      maxWidth: deviceRatio > 1.6 ? 500 : 800,
                    },
                  },
                },
              },
            },
          ],
        },
      },
    },
    // requiresSellingPlan: true,
    // sellingPlanGroups: {
    //   __args: {
    //     first: 1,
    //   },
    //   edges: {
    //     node: {
    //       name: true,
    //       options: {
    //         name: true,
    //         values: true,
    //       },
    //       sellingPlans: {
    //         __args: {
    //           first: 1,
    //         },
    //         edges: {
    //           node: {
    //             id: true,
    //             name: true,
    //             description: true,
    //             recurringDeliveries: true,
    //             options: {
    //               name: true,
    //               value: true,
    //             },
    //           },
    //         },
    //       },
    //     },
    //   },
    // },
    priceRange: {
      maxVariantPrice: {
        amount: true,
        currencyCode: true,
      },
      minVariantPrice: {
        amount: true,
        currencyCode: true,
      },
    },
    compareAtPriceRange: {
      maxVariantPrice: {
        amount: true,
        currencyCode: true,
      },
      minVariantPrice: {
        amount: true,
        currencyCode: true,
      },
    },
    options: {
      id: true,
      name: true,
      values: true,
    },
    appmaker_hide_product_status: {
      __aliasFor: 'metafield',
      __args: {
        namespace: 'appmaker',
        key: 'hide_product_status',
      },
      value: true,
    },
    variants: {
      __args: {
        first: 250,
      },
      pageInfo: {
        hasNextPage: true,
        hasPreviousPage: true,
      },
      edges: {
        node: {
          availableForSale: true,
          quantityAvailable: true,
          sku: true,
          id: true,
          title: true,
          selectedOptions: {
            name: true,
            value: true,
          },
          // sellingPlanAllocations: {
          //   __args: {
          //     first: 10,
          //   },
          //   edges: {
          //     node: {
          //       sellingPlan: {
          //         id: true,
          //         description: true,
          //         name: true,
          //         options: {
          //           name: true,
          //           value: true,
          //         },
          //       },
          //       priceAdjustments: {
          //         price: {
          //           amount: true,
          //           currencyCode: true,
          //         },
          //         compareAtPrice: {
          //           amount: true,
          //           currencyCode: true,
          //         },
          //         perDeliveryPrice: {
          //           amount: true,
          //           currencyCode: true,
          //         },
          //         unitPrice: {
          //           amount: true,
          //           currencyCode: true,
          //         },
          //       },
          //     },
          //   },
          // },
          image: {
            src: {
              __aliasFor: 'url',
              __args: {
                transform: {},
              },
            },
            url: {
              __args: {
                transform: singleProductTransform,
              },
            },
          },
          price: {
            amount: true,
            currencyCode: true,
          },
          compareAtPrice: {
            currencyCode: true,
            amount: true,
          },
        },
      },
    },
    images: {
      __args: {
        first: 250,
      },
      pageInfo: {
        hasNextPage: true,
        hasPreviousPage: true,
      },
      edges: {
        node: {
          altText: true,
          src: {
            __aliasFor: 'url',
            __args: {
              transform: {},
            },
          },
          height: true,
          width: true,
          url: {
            __args: {
              transform: singleProductTransform,
            },
          },
        },
      },
    },
  };
};

const getContextString = (values: Record<string, string>): string => {
  const filteredEntries = Object.entries(values).filter(
    ([key, value]) => value,
  );

  if (filteredEntries.length === 0) {
    return '';
  }

  const contextString = filteredEntries
    .map(([key, value]) => `${key}:${value}`)
    .join(',');

  return `@inContext(${contextString})`;
};

class ShopifyDataSource {
  constructor(config) {
    this.config = config;
    global._shopifyConfig = config;
    this.baseUrl = config.baseUrl;
    this.apiVersion = '2025-01';
    this.contextString = getContextString({
      country: appStorageApi().getState().country,
    });
    this.apiVersion = appmaker.applyFilters(
      'appmaker-shopify-api-version',
      this.apiVersion,
    );
    this.apiURL = `${this.baseUrl}/api/${this.apiVersion}/graphql.json`;
    this.axios = axios.create({
      // withCredentials: true,
      baseURL: this.apiURL,
      headers: {
        'x-shopify-storefront-access-token': config.accessToken,
      },
    });
  }
  static configSchema() {
    return {
      type: 'object',
      properties: {
        baseUrl: {
          type: 'string',
        },
        accessToken: {
          type: 'string',
        },
      },
    };
  }

  static formSchema() {
    return {
      type: 'object',
      properties: {
        methodName: {
          type: 'string',
          enum: ['products', 'product', 'collections', 'productsByCollection'],
        },
        params: {
          type: 'string',
        },
      },
    };
  }
  async gqlQuery(query, { transformFn, method, params } = {}) {
    let resp = await this.axios.post('', query, {
      headers: {
        'Accept-Language':
          appStorageApi().getState().language ||
          appSettings.getOption('defaultLanguage', 'en'),
      },
    });
    if (transformFn) {
      resp = await transformFn(resp);
    }

    if (resp.headers['x-shopify-api-deprecated-reason']) {
      analytics.trackSystemEvents('shopify_api_deprecated');
      analytics.trackSystemEvents('shopify_api_deprecated_with_query', {
        query,
      });
      if (process.env.NODE_ENV === 'development' && process.env.EXPO_PUBLIC_DISABLE_SHOPIFY_API_DEPRECATION_WARNING !== 'true') {
        // console.log(
        //   `Shopify API Deprecated: ${resp.headers['x-shopify-api-deprecated-reason']}`,
        //   query,
        // );
        // Truncate query to first 50 characters
        const queryString = JSON.stringify(query);
        const truncatedQuery = queryString.length > 250 
          ? queryString.substring(0, 250) + '...' 
          : query;
        
        // Alert.alert(
        //   `Shopify API Deprecated: ${resp.headers['x-shopify-api-deprecated-reason']}`, 
        //   `Query: ${truncatedQuery}\nPlease Check Console for more`
        // );
      }
    }

    resp = await appmaker.applyFilters('shopify-response-data', resp, {
      method,
      params,
    });
    return resp;
  }
  async getNavigationMenu() {
    return axios.get(
      `${getApiBaseUrl().replace('v2', 'v1')}/menu?language=${
        appStorageApi().getState().language ||
        appSettings.getOption('defaultLanguage', 'en')
      }`,
    );
  }

  async checkoutCreate({ lineItems, customAttributes }) {
    const query = `mutation checkoutCreate($input: CheckoutCreateInput!) {
      checkoutCreate(input: $input) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      input: {
        lineItems,
        ...(customAttributes && { customAttributes: customAttributes }),
      },
    };
    return this.gqlQuery({ query, variables });
  }
  async validateCart(cartResponse, isGetCart = false) {
    // const {cart }
    const currentCart = getCheckoutFromResponse(cartResponse);
    let invalidLineItemsToRemove = [];
    currentCart?.lineItems?.edges.forEach((lineItem) => {
      if (!lineItem.node.variant) {
        invalidLineItemsToRemove.push(lineItem.node.id);
      }
    });

    const validatedCart =
      (await applyFilters(
        'cart-response-validate',
        {
          lineItemsToRemove: invalidLineItemsToRemove,
          lineItemsToAdd: [],
          lineItemsToUpdate: [],
        },
        {
          cart: currentCart,
          isGetCart,
        },
      )) || {};

    const {
      lineItemsToAdd = [],
      lineItemsToUpdate = [],
      lineItemsToRemove = [],
      cartCustomAttributes = [],
      applyDiscountCode,
      removeDiscountCode,
    } = validatedCart;

    if (
      isEmpty(lineItemsToAdd) &&
      isEmpty(lineItemsToUpdate) &&
      isEmpty(lineItemsToRemove) &&
      isEmpty(cartCustomAttributes) &&
      !applyDiscountCode &&
      !removeDiscountCode
    ) {
      return cartResponse;
    } else {
      const newCart = await this.manageCheckout({
        checkoutId: currentCart.id,
        lineItemsToAdd,
        lineItemsToRemove,
        lineItemsToUpdate,
        cartCustomAttributes,
        applyDiscountCode,
        removeDiscountCode,
        isValidatingCart: true,
      });
      if (isGetCart) {
        const newCartGet = getCheckoutFromResponse(newCart);
        return { data: { data: { node: newCartGet } } };
      } else {
        return newCart;
      }
    }
  }
  async manageCheckout({
    checkoutId,
    lineItemsToAdd,
    lineItemsToUpdate,
    lineItemsToRemove,
    cartCustomAttributes = [],
    shippingAddress = false,
    email = false,
    isValidatingCart = false,
    applyDiscountCode = false,
    removeDiscountCode = false,
  }) {
    function isNewCheckout(_checkoutId) {
      return _checkoutId === undefined || _checkoutId === null;
    }
    const createCheckoutQueryWithoutLineItems = `
    checkoutCreate(
      input: {customAttributes: $customAttributes}
    ) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }
    `;
    const createCheckoutQuery = `
    checkoutCreate(
      input: {lineItems: $lineItemsToAdd, customAttributes: $customAttributes}
    ) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }
    `;
    const addItemsQuery = `
    checkoutLineItemsAdd(checkoutId: $checkoutId, lineItems: $lineItemsToAdd) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;
    const updateItemsQuery = `checkoutLineItemsUpdate(checkoutId: $checkoutId, lineItems: $lineItemsToUpdate) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;
    // lineItemsToRemove
    const removeItemsQuery = `checkoutLineItemsRemove(checkoutId: $checkoutId, lineItemIds: $lineItemsToRemove) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;

    const updateShippingAddressQuery = `checkoutShippingAddressUpdateV2(checkoutId: $checkoutId, shippingAddress: $shippingAddress) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;

    const updateEmailQuery = `checkoutEmailUpdateV2(checkoutId: $checkoutId, email: $email) {
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;

    const cartCustomAttributesUpdateQuery = `checkoutAttributesUpdateV2(
      checkoutId: $checkoutId
      input: {customAttributes: $customAttributes}
    ){
      checkout {
        ...CheckoutFragment
      }
      checkoutUserErrors {
        ...CheckoutErrorsFragment
      }
    }`;
    // mutation checkoutDiscountCodeApplyV2($discountCode: String!, $checkoutId: ID!) {
    const applyCouponQuery = `
      checkoutDiscountCodeApplyV2(
        discountCode: $discountCode
        checkoutId: $checkoutId
      ) {
        checkout {
          ...CheckoutFragment
        }
        checkoutUserErrors {
          ...CheckoutErrorsFragment
        }
      }`;
    const removeCouponQuery = `
    checkoutDiscountCodeRemove(checkoutId: $checkoutId) {
        checkout {
          ...CheckoutFragment
        }
        checkoutUserErrors {
          ...CheckoutErrorsFragment
        }
      }`;

      const query = `
      mutation 
      updateCart(
        ${!isNewCheckout(checkoutId) ? '$checkoutId: ID!,' : ''}
        ${
          lineItemsToAdd?.length > 0
            ? '$lineItemsToAdd: [CheckoutLineItemInput!]! ,'
            : ''
        } 
        ${
          lineItemsToUpdate?.length > 0
            ? '$lineItemsToUpdate: [CheckoutLineItemUpdateInput!]!'
            : ''
        }
        ${lineItemsToRemove?.length > 0 ? '$lineItemsToRemove: [ID!]!' : ''} 
        ${
          isNewCheckout(checkoutId) ? '$customAttributes: [AttributeInput!]!' : ''
        }
        ${
          !isNewCheckout(checkoutId) && cartCustomAttributes?.length > 0
            ? '$customAttributes: [AttributeInput!]!'
            : ''
        }
        ${shippingAddress ? '$shippingAddress: MailingAddressInput!' : ''}
        ${email ? '$email: String!' : ''}
        ${applyDiscountCode ? '$discountCode: String!' : ''}
        ) {
        ${isNewCheckout(checkoutId) && lineItemsToAdd?.length > 0 ? createCheckoutQuery : ''}
        ${isNewCheckout(checkoutId) && !lineItemsToAdd?.length ? createCheckoutQueryWithoutLineItems : ''} 
        ${
          !isNewCheckout(checkoutId) && lineItemsToAdd?.length > 0
            ? addItemsQuery
            : ''
        }
        ${
          !isNewCheckout(checkoutId) && lineItemsToUpdate?.length > 0
            ? updateItemsQuery
            : ''
        }
        ${
          !isNewCheckout(checkoutId) && lineItemsToRemove?.length > 0
            ? removeItemsQuery
            : ''
        }
        ${shippingAddress ? updateShippingAddressQuery : ''}
        ${email ? updateEmailQuery : ''}
        ${
          !isNewCheckout(checkoutId) && cartCustomAttributes?.length > 0
            ? cartCustomAttributesUpdateQuery
            : ''
        }
        ${applyDiscountCode ? applyCouponQuery : ''}
        ${removeDiscountCode ? removeCouponQuery : ''}
    }
    
    fragment CheckoutErrorsFragment on CheckoutUserError {
      code
      field
      message
    }
    
    fragment CheckoutFragment on Checkout {
      ${getCheckoutFields()}
    }    
    `;
    const variables = {
      checkoutId,
      lineItemsToAdd,
      lineItemsToUpdate,
      customAttributes: cartCustomAttributes,
      shippingAddress,
      email,
      discountCode: applyDiscountCode,
      lineItemsToRemove,
    };
    let checkoutResponse = await this.gqlQuery({ query, variables });
    if (!isValidatingCart) {
      checkoutResponse = await this.validateCart(checkoutResponse, false);
    }

    return checkoutResponse;
  }
  async checkoutShippingAddressUpdateV2({ shippingAddress, checkoutId }) {
    const query = `mutation checkoutShippingAddressUpdateV2($shippingAddress: MailingAddressInput!, $checkoutId: ID!) {
      checkoutShippingAddressUpdateV2(shippingAddress: $shippingAddress, checkoutId: $checkoutId) {
        checkoutUserErrors {
          code
          field
          message
        }
        checkout {
          ${getCheckoutFields()}
        }
      }
    }
    
    `;
    const variables = {
      shippingAddress,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async customerDefaultAddressUpdate({ addressId, customerAccessToken }) {
    const query = `
    mutation checkoutShippingAddressUpdateV2($customerAccessToken: String!, $addressId: ID!) {
      customerDefaultAddressUpdate(
        customerAccessToken: $customerAccessToken
        addressId: $addressId
      ) {
        customerUserErrors {
          code
          field
          message
        }
        customer {
          id
        }
      }
    }
    `;
    const variables = {
      customerAccessToken,
      addressId,
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutEmailUpdateV2({ email, checkoutId }) {
    const query = `
    mutation checkoutEmailUpdateV2($email: String!,$checkoutId: ID!) {
      checkoutEmailUpdateV2(checkoutId: $checkoutId, email: $email){
      checkoutUserErrors {
          code
          field
          message
        }
        checkout {
          ${getCheckoutFields()}
        }
      }
    }
    
    `;
    const variables = {
      email,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartCreate({ lines, attributes }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `mutation createCart($cartInput: CartInput) {
      cartCreate(input: $cartInput) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }    
    `;
    const variables = {
      cartInput: { lines, attributes },
    };
    // const lineItems = {"lines": [
    //   {
    //     "quantity": 1,
    //     "merchandiseId": "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8yODYyMDE5NjE1MTQwMQ"
    //   }
    // ]}
    return this.gqlQuery({ query, variables });
  }

  async checkoutLineItemsAdd({ lineItems, checkoutId }) {
    const query = `
    mutation checkoutLineItemsAdd($lineItems: [CheckoutLineItemInput!]!, $checkoutId: ID!) {
      checkoutLineItemsAdd(lineItems: $lineItems, checkoutId: $checkoutId) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
    
    `;
    const variables = {
      lineItems,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartLinesAdd({ lines, cartId }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `
    mutation cartLinesAdd($lines: [CartLineInput!]!, $cartId: ID!) {
      cartLinesAdd(lines: $lines, cartId: $cartId) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    
    `;
    const variables = {
      lines,
      cartId,
    };
    /*
    {
      "lines": [
        {
          "merchandiseId": "Z2lkOi8vU2hvcGlmeS9FeGFtcGxlLzE=",
          quantity
        }
      ],
      "cartId": "Z2lkOi8vU2hvcGlmeS9FeGFtcGxlLzE="
    }
    */
    return this.gqlQuery({ query, variables });
  }

  async checkoutLineItemsRemove({ checkoutId, lineItemIds }) {
    const query = `
    mutation checkoutLineItemsRemove($checkoutId: ID!, $lineItemIds: [ID!]!) {
      checkoutLineItemsRemove(checkoutId: $checkoutId, lineItemIds: $lineItemIds) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      lineItemIds,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartLinesRemove({ cartId, lineIds }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `
    mutation cartLinesRemove($cartId: ID!, $lineIds: [ID!]!) {
      cartLinesRemove(cartId: $cartId, lineIds: $lineIds) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      lineIds,
      cartId,
    };
    /*
    {
      "cartId": "Z2lkOi8vU2hvcGlmeS9FeGFtcGxlLzE=",
      "lineIds": [
        "Z2lkOi8vU2hvcGlmeS9FeGFtcGxlLzE="
      ]
    }
    */
    return this.gqlQuery({ query, variables });
  }
  async cartSetAttributes({ cartId, attributes }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = ` 
    mutation MyMutation($cartId: ID!,$attributes: [AttributeInput!]!) {
      cartAttributesUpdate(attributes: $attributes, cartId: $cartId) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    
    `;
    const variables = {
      cartId,
      attributes,
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutSetAttributes({ checkoutId, attributes, note }) {
    const query = `
    mutation checkoutAttributesUpdateV2($checkoutId: ID!, $input: CheckoutAttributesUpdateV2Input!) {
      checkoutAttributesUpdateV2(checkoutId: $checkoutId, input: $input) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      checkoutId,
      input: {
        allowPartialAddresses: true,
        ...(attributes && { customAttributes: attributes }),
        ...(note && { note: note }),
      },
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutLineItemsUpdate({ checkoutId, lineItems }) {
    const query = `
    mutation checkoutLineItemsUpdate($checkoutId: ID!, $lineItems: [CheckoutLineItemUpdateInput!]!) {
      checkoutLineItemsUpdate(checkoutId: $checkoutId, lineItems: $lineItems) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      lineItems,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartLinesUpdate({ cartId, lines }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `
    mutation cartLinesUpdate($cartId: ID!, $lines: [CartLineUpdateInput!]!) {
      cartLinesUpdate(cartId: $cartId, lines: $lines) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    
     `;
    const variables = {
      lines,
      cartId,
    };
    /*
    {
      "cartId": "Z2lkOi8vc2hvcGlmeS9DYXJ0LzI5ZDQyNWZhMTQ1YTQ0ZWQ5ZGUyOTQ3YWMxMjhiMGM1",
      "lines": {
        "id": "Z2lkOi8vc2hvcGlmeS9DYXJ0TGluZS9kNzY0YTliZTJhMTViY2Y1YTNjODU5YzAwMjljNWMzOD9jYXJ0PTI5ZDQyNWZhMTQ1YTQ0ZWQ5ZGUyOTQ3YWMxMjhiMGM1",
        "quantity": 3
      }
    }
    */
    return this.gqlQuery({ query, variables });
  }

  async checkoutDiscountCodeApplyV2({ discountCode, checkoutId }) {
    const query = `
    mutation checkoutDiscountCodeApplyV2($discountCode: String!, $checkoutId: ID!) {
      checkoutDiscountCodeApplyV2(
        discountCode: $discountCode
        checkoutId: $checkoutId
      ) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      discountCode,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutDiscountCodeRemove({ checkoutId }) {
    const query = `
    mutation checkoutDiscountCodeRemove($checkoutId: ID!) {
      checkoutDiscountCodeRemove(
        checkoutId: $checkoutId
      ) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutGiftCardsAppend({ giftCardCodes, checkoutId }) {
    const query = `
    mutation checkoutDiscountCodeApplyV2($giftCardCodes: [String!]!, $checkoutId: ID!) {
      checkoutGiftCardsAppend(
        giftCardCodes: $giftCardCodes
        checkoutId: $checkoutId
      ) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
     `;
    const variables = {
      giftCardCodes,
      checkoutId,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartDiscountCodesUpdate({ discountCodes, cartId }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `
    mutation cartDiscountCodesUpdate($cartId: ID!,$discountCodes: [String!]) {
      cartDiscountCodesUpdate(cartId: $cartId,discountCodes: $discountCodes) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }    
     `;
    const variables = {
      discountCodes,
      cartId,
    };
    return this.gqlQuery({ query, variables });
  }
  async cartNoteUpdate({ note, cartId }) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });

    const query = `
    mutation cartNoteUpdate($cartId: ID!,$note: String!) {
      cartNoteUpdate(cartId: $cartId,note: $note) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }    
     `;
    const variables = {
      note,
      cartId,
    };
    return this.gqlQuery({ query, variables });
  }

  async customerRecover({ email }) {
    const query = `
    mutation customerRecover($email: String!) {
      customerRecover(email: $email) {
        customerUserErrors {
          code
          field
          message
        }
      }
    }    
     `;
    const variables = {
      email,
    };
    return this.gqlQuery({ query, variables });
  }
  async checkoutCustomerAssociateV2(
    { checkoutId, customerAccessToken },
    { appStorageState },
  ) {
    const query = `
    mutation checkoutCustomerAssociateV2($checkoutId: ID!, $customerAccessToken: String!) {
      checkoutCustomerAssociateV2(
        checkoutId: $checkoutId
        customerAccessToken: $customerAccessToken
      ) {
        checkout {
          ${getCheckoutFields()}
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      checkoutId,
      customerAccessToken,
    };
    return this.gqlQuery({ query, variables });
  }

  async cartBuyerIdentityUpdate(
    { cartId, customerAccessToken },
    { appStorageState },
  ) {
    const fields = jsonToGraphQLQuery(cartFields, { pretty: true });
    const query = `
    mutation cartBuyerIdentityUpdate($cartId: ID!, $buyerIdentity: CartBuyerIdentityInput!) {
      cartBuyerIdentityUpdate(cartId: $cartId, buyerIdentity: $buyerIdentity) {
        cart {
          ${fields}
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      cartId,
      buyerIdentity: {
        customerAccessToken,
      },
    };
    return this.gqlQuery({ query, variables });
  }

  async order({ accessToken, checkoutId, ...props }, { appStorageState }) {
    // Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kYTk4YTlmYjBiNzBlZTczYTE0ZjJjODI5YmU5ZDdiNT9rZXk9YTk2Nzg5NDQ3NTZjYWRlMGNlNjk0YTJiMzQxZDAxZGY=
    const query = `
    query getOrder ${this.contextString} {
      node(
        id: "${checkoutId}"
      ) {
        ... on Checkout {
          id
          order {
            ${orderFields}
          }
        }
      }
    }
    `;

    const variables = {};
    return this.gqlQuery({ query, variables });
  }

  async recentOrder(params, { appStorageState }) {
    const query = `
    query customerQuery ${this.contextString} {
      customer(customerAccessToken: "${params?.pageData.accessToken}") {
        orders(first: 1, reverse: true) {
          edges {
            node {
              ${orderFields}
            }
          }
        }
      }
    }
    `;
    const variables = {};
    return this.gqlQuery({ query, variables });
  }

  async orders({ accessToken, ordersLimit = 100 }, dependencies) {
    const { appStorageState, prevRawResponse, page } = dependencies;
    const orderFields = getOrdersField();
    let after = '';
    if (prevRawResponse && page > 1) {
      const orders = prevRawResponse.data.data.customer.orders.edges;
      const lastOrder = orders[orders.length - 1];
      after = `after: "${lastOrder.cursor}"`;
    }

    const query = `
    query customerQuery ${this.contextString} {
      customer(customerAccessToken: "${accessToken}") {
        orders(
          first: ${ordersLimit},
          reverse: true
          ${after}
          ) {
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
          edges {
            cursor
            node {
              ${orderFields}
            }
          }
        }
      }
    }
    `;
    const variables = {};
    // return this.gqlQuery({ query, variables });
    const response = await this.gqlQuery({ query, variables });
    const data = applyFilters('shopify-orders-response', response, dependencies);
    return data;
  }

  async orderById({ orderId }: { orderId: string }) {
    const query = `
    query getOrderById ${this.contextString} {
      node(
        id: "${orderId}"
      ) {
        ... on Order {
          ${getOrdersField()}
        }
      }
    }
    `;
    const variables = {};

    let orderResponse = await this.gqlQuery({ query, variables });
    if (orderResponse?.data?.data?.node) {
      orderResponse.data.data.node = await applyFilters(
        'shopify-order-item-response',
        orderResponse?.data?.data?.node,
      );
    }
    return orderResponse;
  }

  async getCustomerInfo({ accessToken }, { appStorageState }) {
    const query = `
    query customerQuery {
      customer(customerAccessToken: "${accessToken}") {
        email
        firstName
        lastName
        id
        tags
        displayName
        phone,
        createdAt,
        numberOfOrders,
        updatedAt
        defaultAddress {
          address1,
          address2
          city,
          country,
          company,
          countryCode
          firstName
          lastName
          province,
          provinceCode
          zip,
          phone
        }
      }
    }
    `;
    const variables = {};
    return this.gqlQuery({ query, variables });
  }

  async customerCreate({ email, password, firstName, lastName, phone }) {
    const query = `
  mutation customerCreate($input: CustomerCreateInput!) {
    customerCreate(input: $input) {
      customer {
        email
        firstName
        lastName
        id
        tags
        displayName
        phone
        defaultAddress {
          address1
        }
      }
      customerUserErrors {
        code
        field
        message
      }
    }
  }
  `;
    const variables = {
      input: {
        ...(firstName && { firstName }),
        ...(lastName && { lastName }),
        ...(phone && { phone }),
        email: email,
        password: password,
      },
    };
    return this.gqlQuery({ query, variables });
  }

  async customerUpdate({
    email,
    password,
    firstName,
    lastName,
    phone,
    accessToken,
  }) {
    const query = `
    mutation customerUpdate($customer: CustomerUpdateInput!, $customerAccessToken: String!) {
      customerUpdate(customer: $customer, customerAccessToken: $customerAccessToken) {
        customer {
          email
          firstName
          lastName
          id
          tags
          displayName
          phone
          defaultAddress {
            address1
          }
        }
        customerAccessToken {
          accessToken
          expiresAt
        }
        customerUserErrors {
          code
          field
          message
        }
      }
    }
  `;
    const variables = {
      customer: {
        ...(firstName && { firstName }),
        ...(lastName && { lastName }),
        ...(phone && { phone }),
        ...(email && { email }),
        ...(password && { password }),
      },
      customerAccessToken: accessToken,
    };
    return this.gqlQuery({ query, variables });
  }

  async resetPassword({ id, resetToken, password }) {
    const query = `
    mutation customerReset($id: ID!, $input: CustomerResetInput!) {
      customerReset(id: $id, input: $input) {
        customer {
          email
          firstName
          lastName
          id
          tags
          displayName
          phone
          defaultAddress {
            address1
          }
        }
        customerAccessToken {
          accessToken
          expiresAt
        }
        customerUserErrors {
          code
          field
          message
        }
      }
    }
  `;

    const variables = {
      id,
      input: {
        resetToken,
        password: password,
      },
    };

    return this.gqlQuery({ query, variables });
  }

  async customerAccessTokenCreate({ email, password }) {
    const query = `
    mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
      customerAccessTokenCreate(input: $input) {
        customerAccessToken {
          accessToken
          expiresAt
        }
        customerUserErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      input: {
        email: email ? email : '<EMAIL>',
        password: password ? password : 'HiZqFuDvDdQ7',
      },
    };
    return this.gqlQuery({ query, variables });
  }

  async customerAccessTokenRenew({ accessToken }) {
    const query = `
   mutation MyMutation($customerAccessToken: String = "") {
    customerAccessTokenRenew(customerAccessToken: $customerAccessToken) {
      customerAccessToken {
        accessToken
        expiresAt
      }
      userErrors {
        field
        message
      }
  }
  }`;
    const variables = {
      customerAccessToken: accessToken,
    };
    return this.gqlQuery({ query, variables });
  }

  async customerAccessTokenCreateWithMultipass({ token }) {
    const query = `
    mutation customerAccessTokenCreateWithMultipass($multipassToken: String!) {
      customerAccessTokenCreateWithMultipass(multipassToken: $multipassToken) {
        customerAccessToken {
          accessToken
          expiresAt
        }
        customerUserErrors {
          code
          field
          message
        }
      }
    }
    `;
    const variables = {
      multipassToken: token,
    };
    return this.gqlQuery({ query, variables });
  }

  async checkoutById(checkoutId) {
    const isCurrencyEnabledInCart =
      !appStorageApi().getState()?.settings?.disable_user_currency_in_cart;
    const query = `
    query getCheckoutById ${isCurrencyEnabledInCart ? this.contextString : ''} {
      node(
        id: "${checkoutId}"
      ) {
        ... on Checkout {
          ${getCheckoutFields()}
        }
      }
    }
    `;
    const variables = {};

    let checkoutResponse = await this.gqlQuery({ query, variables });
    if (checkoutResponse?.data?.data?.node) {
      checkoutResponse.data.data.node = await applyFilters(
        'shopify-checkout-cart-page-response',
        checkoutResponse?.data?.data?.node,
      );
    }
    checkoutResponse = await this.validateCart(checkoutResponse, true);
    return checkoutResponse;
  }

  async customerAddressList({ accessToken }) {
    const query = `
    {
      customer(customerAccessToken: "${accessToken}") {
        defaultAddress {
          id
        }
        addresses(first: 100) {
          edges {
            node {
              id
              firstName
              lastName
              address1
              address2
              city
              province
              provinceCode
              company
              country
              countryCodeV2
              phone
              zip
              formatted
            }
          }
        }
      }
    }
    `;
    const variables = {};
    return this.gqlQuery({ query, variables });
  }
  async getLocalAddress(addressId) {
    const address = await getLocalAddress();
    return { node: address };
  }
  async customerAddressCreate({ accessToken, address }) {
    if (!accessToken) {
      const resp = await setLocalAddress(address);
      return resp;
    }
    const query = `
      mutation customerAddressCreate($address: MailingAddressInput = {}, $customerAccessToken: String = "") {
        customerAddressCreate(
          customerAccessToken: $customerAccessToken
          address: $address
        ) {
          customerAddress {
            id
            address1
          }
          customerUserErrors {
            code
            field
            message
          }
        }
      }
      `;
    const variables = {
      address,
      customerAccessToken: accessToken,
    };
    return this.gqlQuery({ query, variables });
  }
  async checkoutShippingLineUpdate({ checkoutId, shippingRateHandle }) {
    const query = `
    mutation checkoutShippingLineUpdate($checkoutId: ID!, $shippingRateHandle: String!) {
      checkoutShippingLineUpdate(checkoutId: $checkoutId, shippingRateHandle: $shippingRateHandle) {
        checkout {
          id
        }
        checkoutUserErrors {
          code
          field
          message
        }
      }
    }
    
      `;
    const variables = {
      checkoutId,
      shippingRateHandle,
    };
    return this.gqlQuery({ query, variables });
  }
  async customerAddressUpdate({ accessToken, address }) {
    if (!accessToken) {
      const resp = await setLocalAddress(address);
      return resp;
    }
    const { id, ...addressFileds } = address;
    const query = `
    mutation customerAddressUpdate($address: MailingAddressInput = {}, $customerAccessToken: String = "") {
      customerAddressUpdate(
        customerAccessToken: $customerAccessToken
        address: $address
        id: "${id}"
      ) {
        customerAddress {
          address1
        }
        customerUserErrors {
          code
          field
          message
        }
      }
    }
    
    `;
    const variables = {
      address: addressFileds,
      customerAccessToken: accessToken,
    };
    return this.gqlQuery({ query, variables });
  }

  async customerAddressDelete({ accessToken, id }) {
    const query = `
    mutation customerAddressDelete($customerAccessToken: String!, $id: ID!) {
      customerAddressDelete(customerAccessToken: $customerAccessToken, id: $id) {
        customerUserErrors {
          code
          field
          message
        }
        deletedCustomerAddressId
      }
    }
    `;
    const variables = {
      id,
      customerAccessToken: accessToken,
    };
    return this.gqlQuery({ query, variables });
  }

  async availableShippingRates(checkoutId) {
    const query = `
    query getAvailableShippingRates ${this.contextString} {
      node(id: "${checkoutId}") {
        ... on Checkout {
          id
          webUrl
          availableShippingRates {
            ready
            shippingRates {
              handle
              price {
                amount
                currencyCode
              }
              title
              price {
                amount
                currencyCode
              }
            }
          }
        }
      }
    }
    `;
    const variables = {};
    const resp = await this.gqlQuery({ query, variables });
    if (resp?.data?.data?.node?.availableShippingRates?.ready) {
      return resp;
    } else {
      await new Promise((resolve) => setTimeout(resolve, 500));
      return this.availableShippingRates(checkoutId);
    }
  }
  async cartById(cartID) {
    const variables = {
      id: cartID,
    };
    const cart = await runTanstackQuery<
      CurrentCartQuery,
      CurrentCartQueryVariables
    >(
      useCurrentCartQuery.document,
      variables,
      useCurrentCartQuery.getKey(
        variables,
      )

    )

    emitEvent('shopify-cart-response', { cart });
    return {
      data: cart
    }


  }


  async shopDetails() {
    const query = `
    query getShopDetails ${this.contextString} {
      shop {
        name
        moneyFormat
        shipsToCountries
        id
        primaryDomain {
          host
          sslEnabled
          url
        }
      }
    }
    
    `;
    const variables = {};
    return this.gqlQuery({ query, variables });
  }

  // data.products.edges[0].node.images.edges[0].node.src
  async searchProduct(search, dependencies) {
    return this.products({ search }, dependencies);
  }
  async searchProductV2({ query, limit }, dependencies) {
    return this.products({ search: query, productsLimit: limit }, dependencies);
  }
  getSingleProductFields() {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    return fields;
  }
  async productsByHandles({ handles }, dependencies) {
    // return this.products({ search:"*" }, dependencies);
    const handesQuery = handlesToQuery(handles);
    const fields = this.getSingleProductFields();
    return this.gqlQuery(
      {
        query: `
      query getProductsByHandles ${this.contextString}
      {
         ${handesQuery} 
      }

      fragment productFields on Product {
        ${fields}
      }
        
      `,
        variables: null,
      },
      {
        transformFn: convertResponse,
      },
    );
  }

  async product({ productId, productHandle }, {}) {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );
    const nodeId = productId
      ? `id: "${productId}"`
      : `handle: "${productHandle}"`;
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    const fragmentQuery = getFragmentsFor('product');
    const query = `
    query getProduct ${this.contextString} {
      product(${nodeId}) {
 
          ${fields}
    
      }
    }
    ${fragmentQuery}
    `;
    const variables = {};
    return this.gqlQuery(
      { query, variables },
      {
        transformFn: (response) => {
          response.data.data.node = response.data.data.product;
          return response;
        },
      },
    );
  }

  async productRecommendations(params = {}, dependencies = {}) {
    dependencies = {
      ...dependencies,
      ...params?.legacyDependencies
    }
    const fragmentQuery = getFragmentsFor('product');
    const { productId, limit, filterFn, hasPages = true } = params;
    const productsResponse = await applyFilters(
      'shopify-custom-products-recommendation-response',
      null,
      params,
      dependencies,
    );
    if (productsResponse && productsResponse !== null) {
      return productsResponse;
    }
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    const query = `
    query getRecommendations ${this.contextString} {
      productRecommendations(
        productId: "${productId}"
      ) {
          ${fields}
        }
    }
    ${fragmentQuery}
    `;
    const variables = {};
    if (!hasPages && dependencies?.page > 1) {
      return {
        data: {
          data: {
            productRecommendations: [],
          },
        },
      };
    }
    return this.gqlQuery(
      { query, variables },
      {
        transformFn: (response) => {
          if (filterFn && typeof filterFn === 'function') {
            response.data.data.productRecommendations = filterFn(
              response?.data?.data?.productRecommendations,
            );
          }
          response.data.data.productRecommendations =
            response.data.data.productRecommendations.map((item) => ({
              node: item,
            }));
          if (
            (limit && typeof limit === 'number' && response.data.data.productRecommendations.length > limit)
          ) {
            response.data.data.productRecommendations =
              response.data.data.productRecommendations.splice(0, limit);
          }
          return response;
        },
      },
    );
  }

  async products(params = {}, dependencies = {}) {
    dependencies = {
      ...dependencies,
      ...params?.legacyDependencies,
    }
    const { headless } = params;
    const isHeadless = !!headless;
    const { prevRawResponse, page = 1, filterParams } = dependencies;
    const { blockItem } = dependencies;
    params = params?.pageData ? params.pageData : params;
    params.search = params?.search || filterParams?.searchQuery;
    if(page === 2 && params?.ids) {
      return {
        data: {
          data: {
            products: {
              edges: [],
            },
          },
        },
      };
    }
    if (
      params?.collectionId ||
      params?.search ||
      params?.collectionHandle ||
      params?.isSearch
    ) {
      const productsReponse = await applyFilters(
        'shopify-custom-products-response',
        null,
        params,
        dependencies,
      );
      if (productsReponse && productsReponse !== null) {
        if (page === 1) {
          if (!isHeadless && params?.search && params?.searchResult) {
            this.analyticsTrack?.(
              'product_search_result',
              multipleProductParams(
                productsReponse?.data?.data?.products?.edges,
                {
                  item_list_name: 'product_search_result',
                  list_query: params?.search,
                },
              ),
              {
                products: productsReponse?.data?.data?.products?.edges,
                query: params?.search,
              },
            );
          }
          // Created for analytics event 'view_item_list'
          if (!isHeadless) {
            applyFilters('shopify-custom-product-response-analytics', productsReponse, params, dependencies);
          }
        }
        return productsReponse;
      }
    }
    params = params?.pageData ? params.pageData : params;

    const shopifySettings =
      appPluginStoreApi().getState().plugins?.shopify?.settings;
    const productsLimitFromShopifyExtension =
      shopifySettings?.configure_number_of_products_in_plp == true
        ? parseInt(shopifySettings?.number_of_products_in_plp || '20', 10)
        : 20;
    const productsLimit =
      params.productsLimit || productsLimitFromShopifyExtension;
    // prevRawResponse.data.data.products.edges
    const fragmentQuery = getFragmentsFor('product');
    const fields = this.getSingleProductFields();

    const productFields = `pageInfo {
      hasNextPage
      hasPreviousPage
    }
    edges {
      cursor
      node {
        ${fields}
      }
    }`;
    let { collectionId, ids, search, collectionHandle, endCursor } = params;
    collectionId = blockItem?.sub_collection?.id || collectionId;
    let after = endCursor || '';
    let previousPageInfo = undefined;
    // console.log({ prevRawResponse });
    if (prevRawResponse && page > 1) {
      const products = prevRawResponse?.data?.data?.products?.edges;
      const lastProduct = products?.[products?.length - 1];
      // console.log(products.edges);
      // console.log(products, products.edges[products.length - 1]);
      after = `after: "${lastProduct?.cursor}"`;
      previousPageInfo = prevRawResponse?.data?.data?.products?.pageInfo
    }
    // if () {
    //   return this.gqlQuery(
    //     {
    //       query: `
    //     {
    //       collection(handle: "${collectionHandle}"){
    //       id,
    //         title,
    //         products(
    //           first: ${productsLimit}
    //           ${after}
    //           ) {
    //             ${productFields}

    //         }
    //       }
    //     }

    //    `,
    //       variables: null,
    //     },
    //     {
    //       method: 'productsByCollection',
    //       transformFn: (response) => {
    //         response.data.data.products = response.data.data.node.products;
    //         return response;
    //       },
    //     },
    //   );
    // }
    const searchQuery =
      search && typeof search === 'string' ? `query:"${search}",` : '';
    if (ids) {
      const req = await this.gqlQuery(
        {
          query: `
        query test($ids: [ID!]!) ${this.contextString} {
          nodes(ids: $ids) {
            ... on Product {
              ${fields}
            }
          
        }}
        ${fragmentQuery}
         `,
          variables: {
            ids: Object.values(ids),
          },
        },
        {
          transformFn: convertToNodes,
          params,
        },
      );
      return req;
    }
    let appliedFilters;
    if (params?.appliedFilters) {
      const newAppliedFilter = Object.values?.(params?.appliedFilters);
      appliedFilters = appmaker.applyFilters(
        'shopify-collection-filter-params',
        params?.appliedFilters,
        {
          filters: newAppliedFilter,
        },
      );
    }
    const filters =
      getFilterString(filterParams?.filter) ||
      getFilterString(params?.filters) ||
      getFilterString(appliedFilters);
    const filterParamsString = !isEmpty(filters)
      ? isEmpty(searchQuery)
        ? `filters :${filters}`
        : `productFilters :${filters}`
      : '';
    let sort = '';
    if (typeof filterParams?.sort === 'string') {
      sort = filterParams?.sort;
    } else if (typeof params?.sort?.value === 'string') {
      sort = filterParams?.sort?.value;
    }
    if ((collectionId || collectionHandle) && isEmpty(searchQuery)) {
      const selector = collectionId
        ? `id: "${collectionId}"`
        : `handle: "${collectionHandle}"`;
      const filterQuery = `
        filters {
                  id
                  label
                  type
                  values {
                    count
                    id
                    input
                    label
                  }
                }
        `;
        const extraFieldsCollection = appmaker.applyFilters(
          'shopify-gql-collection-extra-fields',
          collectionFields,
        );
        const gqlCollectionExtraFields = jsonToGraphQLQuery(extraFieldsCollection, { pretty: true });

        const collection_response = await  this.gqlQuery(
        {
          query: `
          query getCollection ${this.contextString} {
          collection(${selector}) {
            id
            ... on Collection {
              ${gqlCollectionExtraFields}
              title
              handle
              products(
                first:${productsLimit}
                ${after}
                ${filterParamsString}
                ${sort}
                ) {
                ${productFields}
              }
            }
          }
        }
        ${fragmentQuery}
       `,
          variables: null,
        },
        {
          method: 'productsByCollection',
          params: { filterParams },
          transformFn: (response) => {
            response.data.data.products =
              response?.data?.data?.collection?.products || [];
            return response;
          },
        },
      );
      return applyFilters('shopify-custom-collection-response', collection_response, params,dependencies);
    }
    if (!isEmpty(searchQuery)) {
      if(page > 1 && previousPageInfo?.hasNextPage === false) {
        return {
          data: {
            data: {
              products: {
                edges: [],
              },
            },
          },
        };
      }
      const search_results = await this.gqlQuery(
        {
          query: `
        query searchProducts ${this.contextString} {
        search(
          ${searchQuery}
          first:${productsLimit}
          ${filterParamsString}
          ${sort}
          types: PRODUCT
          ${after}
          ) {
            pageInfo {
              hasNextPage
              hasPreviousPage
            }
            totalCount
            edges {
              node {
                ... on Product {
                  ${fields}
                }
              }
              cursor
            }
        }
      }
      ${fragmentQuery}
      `,
          variables: null,
        },
        {
          transformFn: (response) => {
            const totalCount = response?.data?.data?.search?.totalCount;
            if (!isHeadless && dependencies?.page === 1) {
              this.analyticsTrack?.(
                'product_search_result',
                multipleProductParams(response?.data?.data?.search?.edges, {
                  item_list_name: 'product_search_result',
                  list_query: params?.search,
                }),
                {
                  products: response?.data?.data?.search?.edges,
                  query: params?.search,
                  totalCount,
                },
              );
            }
            return {
              ...response,
              data: {
                data: {
                  products: {
                    edges: response?.data?.data?.search?.edges,
                    pageInfo: response?.data?.data?.search?.pageInfo,
                    totalCount, 
                  },
                },
              },
            };
          },
        },
      );
      return applyFilters('shopify-custom-search-response-data', search_results, params,dependencies);
    }

    const data =  this.gqlQuery({
      query: `
          query getSearchResult ${this.contextString} {
        products(
          ${searchQuery}
          first:${productsLimit}
          ${after}
          ) {
          ${productFields}
        }
      }`,
      variables: null,
    });
    return applyFilters('shopify-custom-products-response-data', data, params,dependencies);
  }

  analyticsTrack = async (eventName, data, context) => {
    analytics.track(eventName, data, context);
  }

  async posts(params = {}, { prevRawResponse, page }) {
    params = params?.pageData ? params.pageData : params;
    const productsLimit = params.productsLimit || 100;

    const postFields = `edges {
      node {
        id
        title
      }
    }
    `;
    return this.gqlQuery({
      query: `
      {
        blogs(
          first:${productsLimit}
          ) {
          ${postFields}
        }
      }`,
      variables: null,
    });
  }

  async page(params) {
    // Z2lkOi8vc2hvcGlmeS9QYWdlLzg1NzMxNjcyMjQw
    const { id, handle } = params;
    const query = id ? `id: "${id}"` : `handle: "${handle}"`;
    return this.gqlQuery({
      query: `
      {
        page(${query}) {
          id
          handle
          title
          body
          bodySummary
          onlineStoreUrl
          handle
        }
      }`,
    });
  }
  async post(params = {}, { prevRawResponse, page }) {
    params = params?.pageData ? params.pageData : params;
    const productsLimit = params.productsLimit || 100;

    const postFields = `edges {
      node {
        onlineStoreUrl
        id
        title
        publishedAt
        author {
          name
        }
        image {
          src: url(transform:{})
        }
        url
        excerptHtml
        contentHtml
      }
    }
    `;
    return this.gqlQuery({
      query: `
      {
        blog(id: "${params?.id}") {
          id
          articles(first: ${productsLimit}, reverse: true) {
            ${postFields}
          }
        }
      }`,
      variables: null,
    });
  }

  async postByHandle(params) {
    params = params?.pageData ? params.pageData : params;
    const { handleBlog, handleArticle } = params;

    const fields = `
      onlineStoreUrl
      id
      title
      publishedAt
      author {
        name
      }
      image {
        src: url(transform:{})
      }
      url
      excerptHtml
      contentHtml`;
    return this.gqlQuery({
      query: `
      {
        blog(handle: "${handleBlog}") {
          id
          title
          articleByHandle(handle: "${handleArticle}") {
            ${fields}
          }
        }
      }`,
      variables: null,
    });
  }

  async productSort(params: { search?: string; context?: { type: string } }) {
    const search = params?.search;
    const context = params?.context;
    let Sort;
    if (isEmpty(search)) {
      Sort = [
        {
          label: 'Default',
          value: ' ',
        },
        {
          label: 'Featured',
          value: 'reverse: false, sortKey: RELEVANCE',
        },
        {
          label: 'Price low to high',
          value: 'reverse: false, sortKey: PRICE',
        },
        {
          label: 'Price high to low',
          value: 'reverse: true, sortKey: PRICE',
        },
        {
          label: 'Best Selling',
          value: 'reverse: false, sortKey: BEST_SELLING',
        },
        {
          label: 'Date, Old To New',
          value: 'reverse: false, sortKey: CREATED',
        },
        {
          label: 'Date, New To Old',
          value: 'reverse: true, sortKey: CREATED',
        },
      ];
    } else {
      Sort = [
        {
          label: 'Featured',
          value: 'reverse: false, sortKey: RELEVANCE',
        },
        {
          label: 'Price low to high',
          value: 'reverse: false, sortKey: PRICE',
        },
        {
          label: 'Price high to low',
          value: 'reverse: true, sortKey: PRICE',
        },
      ];
    }

    let finalSortOptions = appmaker.applyFilters(
      'shopify-custom-sort-options',
      Sort,
      context,
    );
    return finalSortOptions;
  }

  async productFilters(params = {}, dependencies = {}) {
    dependencies ={
      ...dependencies,
      ...params?.legacyDependencies
    }
    const { collectionId, id, handle, collectionHandle } = params;
    const search = params?.params?.search;
    const collectionHandleFinal = collectionHandle || handle;
    const collectionIdFinal = collectionId || id;
    const collectionSelector = collectionId
      ? `id: "${collectionIdFinal}"`
      : `handle: "${collectionHandleFinal}"`;

    const filtersReponse = await applyFilters(
      'shopify-custom-filters-response',
      null,
      params,
      dependencies,
    );

    if (filtersReponse && filtersReponse !== null) {
      return filtersReponse;
    }
    const FILTER_FRAGMENT = `#graphql
fragment FilterFragment on Filter {
  id
  label
  type
  values {
    count
    id
    input
    label
  }
}
`;
    let query;

    if (!isEmpty(search)) {
      query = `#graphql
  query SearchFilters {
    search(query: "${search}",first:10) {
      productFilters {
        ...FilterFragment
      }
    }
  }
  ${FILTER_FRAGMENT}`;
    } else {
      query = `
    query MyQuery ${this.contextString} {
      collection(${collectionSelector}) {
        products(first: 10) {
          filters {
            ...FilterFragment
          }
        }
      }
    }
    ${FILTER_FRAGMENT}    
    `;
    }
    const variables = {};
    return this.gqlQuery(
      { query, variables },
      {
        transformFn: (response) => {
          if (!isEmpty(search)) {
            return {
              ...response,
              data: {
                data: {
                  collection: {
                    products: {
                      filters: response?.data?.data?.search?.productFilters,
                    },
                  },
                },
              },
            };
          } else {
            return response;
          }
        },
        method: 'productFilters',
      },
    );
  }

  async productFiltersNext(params = {}, dependencies = {}) {
    dependencies ={
      ...dependencies,
      ...params?.legacyDependencies
    }
    const { collectionId, id, handle, collectionHandle, selectedFilters, searchQuery } =
      params;
    const search = params?.params?.search || searchQuery;
    const collectionHandleFinal = collectionHandle || handle;
    const collectionIdFinal = collectionId || id;
    const collectionSelector = collectionId
      ? `id: "${collectionIdFinal}"`
      : `handle: "${collectionHandleFinal}"`;

    const filtersReponse = await applyFilters(
      'shopify-custom-filters-response',
      null,
      { ...params, from: 'new' },
      dependencies,
    );

    if (filtersReponse && filtersReponse !== null) {
      return filtersReponse;
    }
    const filters = getFilterString(selectedFilters);
    const filterParamsString = !isEmpty(filters)
      ? isEmpty(search)
        ? `filters :${filters}`
        : `productFilters :${filters}`
      : '';

    const FILTER_FRAGMENT = `#graphql
    fragment FilterFragment on Filter {
      id
      label
      type
      values {
        count
        id
        input
        label
      }
    }
    `;

    let query;

    if (!isEmpty(search)) {
      query = `#graphql
      query SearchFilters {
        search(query: "${search}",first:10, ${filterParamsString}) {
          productFilters {
            ...FilterFragment
          }
        }
      }
      ${FILTER_FRAGMENT}`;
    } else {
      query = `
      query MyQuery ${this.contextString} {
        collection(${collectionSelector}) {
          products(first: 10 ${filterParamsString}) {
            filters {
              ...FilterFragment
            }
          }
        }
      }    
      ${FILTER_FRAGMENT}    
      `;
    }
    const variables = {};
    return this.gqlQuery(
      { query, variables },
      {
        transformFn: (response) => {
          if (!isEmpty(search)) {
            return {
              ...response,
              data: {
                data: {
                  collection: {
                    products: {
                      filters: response?.data?.data?.search?.productFilters,
                    },
                  },
                },
              },
            };
          } else {
            return response;
          }
        },
        method: 'productFiltersNext',
      },
    );
  }

  async collections(params = {}) {
    return this.gqlQuery({
      query: `
      {
        collections(first: 50) {
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
          edges {
            node {
              handle
              id
              title
              image {
                url(transform:{maxHeight: 500, maxWidth: 500})

              }
            }
          }
        }
      }
      
      `,
      variables: null,
    });
  }
  productsByCollection(handle) {
    return this.gqlQuery({
      query: `
      query getCollectionByHandle ${this.contextString} {
        collectionByHandle(handle: "${handle}") {
          id
          products(first: 10) {
            edges {
              node {
                id
                title
                priceRange {
                  maxVariantPrice {
                    amount
                    currencyCode
                  }
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
                compareAtPriceRange {
                  maxVariantPrice {
                    amount
                    currencyCode
                  }
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
                options(first: 10) {
                  id
                  name
                  values
                }
                images(first: 10, maxHeight: 500) {
                  edges {
                    node {
                      src: url(transform:{})
                    }
                  }
                }
                
              }
            }
          }
        }
      }
      
      `,
      variables: null,
    });
  }
  async productsByTag(tag) {
    return this.axios.get('/products', { params: { tag } });
  }
  async tabbedCategories() {
    return this.axios.get('/products/tabbed/categories');
  }
  // async product(id) {
  //   return this.axios.get(`/products/${id}`);
  // }
  async categories() {
    return axios.get(`${this.baseUrl}/wp-json/wp/v2/categories`);
  }

  async productByHandle({ handle }) {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );

    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    const query = `
    query getProductByHandle ${this.contextString} {
      product(handle: "${handle}") {
        ${fields}
      }
    }
    `;
    const variables = {};
    return this.gqlQuery({ query, variables });
  }

  async addToCart(postData) {
    var formData = new FormData();
    Object.keys(postData).map(function (key) {
      var value = postData[key] ? postData[key] : '';
      if (value instanceof Date) {
        value = value.toString();
      }
      formData.append(key, value);
    });
    return this.axios.post('/cart/add', formData);
  }
  async collection(params = {}, dependencies = {}) {
    dependencies = {
      page : 1,
      ...dependencies,
      ...params?.legacyDependencies
    }
    const { collectionId, collectionHandle } = params;
    const collectionResponse = await applyFilters(
      'shopify-custom-collection-response',
      null,
      params,
      dependencies,
    );
    // collectionFields

    if (collectionResponse && collectionResponse !== null) {
      return collectionResponse;
    }
    const extraFields = appmaker.applyFilters(
      'shopify-gql-collection-extra-fields',
      collectionFields,
    );
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    const selector = collectionId
      ? `id: "${collectionId}"`
      : `handle: "${collectionHandle}"`;
    return this.gqlQuery({
      query: `
      {
        collection(${selector}) {
          ${fields}
        }
      }
      `,
      variables: null,
    });
  }
  async collectionIdHandle(params = {}, dependencies = {}) {
    const { collectionId, collectionHandle } = params;
    const selector = collectionId
      ? `id: "${collectionId}"`
      : `handle: "${collectionHandle}"`;
    return this.gqlQuery({
      query: `
      {
        collection(${selector}) {
          handle
          id
          title
        }
      }
      `,
      variables: null,
    });
  }
  async deleteUserAccount({
    email,
    accessToken,
  }: {
    email: string;
    accessToken: string;
  }) {
    if (isEmpty(email) && isEmpty(accessToken)) {
      return Promise.reject('Email or Access Token is required');
    }

    return await deleteAccount({
      email,
      accessToken,
      storeUrl: this?.config?.baseUrl,
      storeAccessToken: this?.config?.accessToken,
    });
  }
  async microserviceGqlQuery(query, { transformFn, method, params } = {}) {
    let resp = await axios.post(
      'https://shopify-app-microservice.appmaker.xyz/v1',
      query,
      {
        headers: {
          'Accept-Language':
            appStorageApi().getState().language ||
            appSettings.getOption('defaultLanguage', 'en'),
          'x-shopify-storefront-access-token': this.config.accessToken,
          'x-appmaker-project-id': getProjectId(),
        },
      },
    );
    if (transformFn) {
      resp = await transformFn(resp);
    }
    resp = await appmaker.applyFilters(
      'shopify-microservice-response-data',
      resp,
      {
        method,
        params,
      },
    );
    return resp;
  }

  async updateCustomerViaMicroservice({
    customerAccessToken,
    tags,
    note,
    metafields,
  }) {
    const query = {
      mutation: {
        customerUpdate: {
          __args: {
            customerAccessToken: customerAccessToken,
            customer: {
              // tags: tags,
              // note: note,
              // metafields: metafields,
            },
          },
          id: true,
        },
      },
    };

    if (tags && Array.isArray(tags) && tags.length > 0) {
      query.mutation.customerUpdate.__args.customer.tags = tags;
    }
    if (note && typeof note === 'string') {
      query.mutation.customerUpdate.__args.customer.note = note;
    }
    if (metafields && Array.isArray(metafields) && metafields.length > 0) {
      query.mutation.customerUpdate.__args.customer.metafields = metafields;
    }

    const graphqlQuery = jsonToGraphQLQuery(query);
    return this.microserviceGqlQuery({ query: graphqlQuery, variables: null });
  }

  // Reviews API
  async getReviews(params = {}, dependencies = {}) {
    dependencies = {
      ...dependencies,
      ...params?.legacyDependencies
    }
    const { page = 1 } = dependencies;
    const productId = params?.productId;
    const perPage =
      params?.limit && typeof params?.limit === 'number' ? params?.limit : 10;
    const rating =
      params?.rating && typeof params?.rating === 'number'
        ? params?.rating
        : null;
    const query = `query {
      judgemeReviews(
        productId: "${productId}"
        perPage: ${perPage}
        currentPage: ${page}
        ${rating ? `rating: ${rating}` : ''}
      ) {
        current_page
        per_page
        reviews {
          title
          body
          rating
          source
          curated
          hidden
          verified
          featured
          created_at
          updated_at
          product_title
          reviewer {
            name
            tags
          }
          pictures {
            hidden
            urls {
              small
              compact
              huge
              original
            }
          }
        }
      }
    }`;
    // return this.gqlQuery({ query, variables });

    let data = await this.microserviceGqlQuery({ query, variables: null });

    return data;
  }

  async addReview(params = {}) {
    const { mutationData } = params;
    const query = {
      mutation: {
        judgemeReviews: {
          __args: {
            data: {
              ...mutationData,
            },
          },
        },
      },
    };
    const graphqlQuery = jsonToGraphQLQuery(query);
    const response = this.microserviceGqlQuery({
      query: graphqlQuery,
      variables: null,
    });
    return response;
  }
  async getReviewSummary(params = {}) {
    const productId = params?.productId;
    const query = `query {
      judgemeReviewCount(productId: "${productId}") {
        average_rating
        total_reviews
        total_questions
        ratings {
          rating
          percentage
          frequency
        }
      }
    }`;
    let data = await this.microserviceGqlQuery({ query, variables: null });
    return data;
  }

  async getProductDescription({ productId, productHandle }, { }) {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-description-extra-fields',
      { description: true, descriptionHtml: true },
    );
    const nodeId = productId
      ? `id: "${productId}"`
      : `handle: "${productHandle}"`;
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    const query = `
    {
      product(${nodeId}) {
          ${fields}
      }
    }
    `;
    const variables = {};
    return this.gqlQuery(
      { query, variables },
      {
        transformFn: (response) => {
          return response;
        },
      },
    );
  }

  getSingProductFields() {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    return fields;
  }
  getSingProductFragment() {
    const extraFields = appmaker.applyFilters(
      'shopify-gql-product-extra-fields',
      getSingleProductField(),
    );
    const fields = jsonToGraphQLQuery(extraFields, { pretty: true });
    return `
    fragment productAllFieldsFragment on Product {
      ${fields}
    }`;
  }

  async collectionsWithProducts(params = {}, dependencies = {}) {
    dependencies = {
      ...dependencies,
      ...params?.legacyDependencies
    }
    const { page = 1, filterParams = {} } = dependencies;
    const { limit = -1 } = params;
    const { selectedCollectionId } = filterParams;
    let collections = params?.collections
      ? Object.values(params?.collections)
      : [];
    if (selectedCollectionId) {
      collections = collections.filter(
        (collection) => collection.id === selectedCollectionId,
      );
      if (page > 1) {
        throw new Error('No more collections found');
      }
    } else if (limit !== -1) {
      collections = collections.splice(page - 1, limit);
    }
    if (collections.length === 0) {
      throw new Error('No collections found');
    }

    // if (collections.length > 0 && collections.length < page) {
    //   throw new Error('No more collections found');
    // }
    const finalCollectionFragments = appmaker.applyFilters(
      'collection-fragments',
      [
        {
          name: 'baseCollectionFragment',
          fragment: COLLECTION_FRAGMENTS.BASE,
        },
      ],
    );
    let finalCollectionFragmentsQuery = '';
    let finalCollectionFragmentNames = '';
    finalCollectionFragments.map(({ name, fragment }) => {
      finalCollectionFragmentNames = `${finalCollectionFragmentNames}
      ...${name}`;
      finalCollectionFragmentsQuery = `${finalCollectionFragmentsQuery} ${fragment}`;
    });
    const collectionQueries = [];
    const COLLECTION_QUERY = ({ id, productsCount, filters }) => {
      const filterString = filters
        ? ` filters: ${cleanIt(filters)}`
        : '';
      return gql`
      collection(id:"${id}"){
      ${finalCollectionFragmentNames}
         products(first: ${productsCount} ${filterString}) {
       edges{
        node {
          ...productAllFieldsFragment
         }
       }
    }
  }`;
    };

    collections.forEach((collection, index) => {
      collectionQueries.push({
        key: `id_${index}`,
        type: 'id',
        query: COLLECTION_QUERY(collection),
        productsCount: collection?.productsCount,
      });
    });

    let finalQuery = collectionQueries.reduce((acc, { query, key, type }) => {
      return gql`${acc}
        ${key}: ${query}
      `;
    }, '');
    finalQuery = gql`query getCollectionProducts ${this.contextString} {
      ${finalQuery}
    }
      ${finalCollectionFragmentsQuery}
      ${this.getSingProductFragment()}
    `;
    const variables = {};
    const response = await this.gqlQuery({ query: finalQuery, variables });
    const analyticsData = {
      data: {
        data: {
          products: response?.data?.data?.id_0?.products,
          collection: response?.data?.data?.id_0,
        }
      }
    }
    applyFilters('shopify-custom-product-response-analytics', analyticsData, params, dependencies);
    return response;
  }
  async metaObjects(params: MetaObjectsParams, dependencies: any = {}) {
    const endCursor =
      dependencies?.prevRawResponse?.data?.data?.metaobjects?.pageInfo
        ?.endCursor;
    const response = await this.gqlQuery({
      query: getMetaObjectQuery(params),
      variables: {
        type: params.type,
        perPage: params.perPage || 10,
        after: endCursor,
      },
    });
    return response;
  }
  async predictiveSearch({ query }: PredictiveSearchParams) {
    const predictiveSearchResponse = await applyFilters(
      'shopify-predictive-search-response',
      null,
      { query },
    );
    if (predictiveSearchResponse && predictiveSearchResponse !== null) {
      return predictiveSearchResponse;
    } else if (!predictiveSearchResponse) {
      const productsReponse = await applyFilters(
        'shopify-custom-products-response',
        null,
        { search: query, surface: 'predictive-search' },
      );
      if (productsReponse && productsReponse !== null) {
        const productEdges = productsReponse?.data?.data?.products?.edges;
        const convertedData = productEdges?.map?.((product) => product?.node);
        return {
          data: {
            data: {
              predictiveSearch: {
                products: convertedData,
              },
            },
          },
        };
      }
    }
    const gqlQuery = `#graphql
      query usePredictiveSearch ${this.contextString}{
        predictiveSearch(query: "${query}") {
          queries {
            text
            styledText
            trackingParameters
          }
          collections {
            title
            handle
            trackingParameters
            image {
              url(transform: {maxHeight: 100})
              altText
            }
          }
          products {
            id
            title
            handle
            tags
            trackingParameters
            images(first: 1) {
              edges {
                node {
                  url(transform: {maxHeight: 100})
                  altText
                }
              }
            }
          }
        }
  } 
    `;

    const predictiveSearchData = this.gqlQuery({ query: gqlQuery, variables: null });
    return applyFilters('shopify-custom-predictive-search-response', predictiveSearchData, { query });
  }
}
type PredictiveSearchParams = {
  query: string;
};
export default ShopifyDataSource;
