{"name": "url-join", "version": "5.0.0", "description": "Join urls and normalize as in path.join.", "type": "module", "main": "./lib/url-join.js", "exports": "./lib/url-join.js", "types": "./lib/url-join.d.ts", "sideEffects": false, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha --require should"}, "files": ["lib"], "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": "<PERSON> <<EMAIL>> (http://joseoncode.com)", "license": "MIT", "devDependencies": {"conventional-changelog": "^3.1.25", "mocha": "^9.2.2", "should": "~13.2.3"}}