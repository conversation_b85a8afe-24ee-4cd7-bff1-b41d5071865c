import GetLocation from 'react-native-get-location';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import { PERMISSIONS, request, check, RESULTS } from 'react-native-permissions';
import RNAndroidLocation<PERSON>nabler from 'react-native-android-location-enabler';
import { getSettings } from '../../config';
import { storePincode } from './Helper';

/**
 * Enable location services on Android
 * @returns {Promise<boolean>} - Whether location services are enabled
 */
export const enableLocationServices = async () => {
  if (Platform.OS !== 'android') {
    return true; // iOS handles this automatically
  }

  try {
    const enableResult = await RNAndroidLocationEnabler.promptForEnableLocationIfNeeded({
      interval: 10000,
      fastInterval: 5000,
    });
    return enableResult === 'enabled' || enableResult === 'already-enabled';
  } catch (error) {
    console.warn('Error enabling location services:', error);
    return false;
  }
};

/**
 * Request location permissions based on platform
 * @returns {Promise<boolean>} - Whether permission was granted
 */
export const requestLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      // First check if location services are enabled
      const locationEnabled = await enableLocationServices();
      if (!locationEnabled) {
        Alert.alert(
          'Location Services Disabled',
          'Please enable location services to use this feature.',
          [{ text: 'OK' }]
        );
        return false;
      }

      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location to provide accurate delivery estimates.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
  } catch (err) {
    console.warn('Error requesting location permission:', err);
    return false;
  }
};

/**
 * Check if location permission is granted
 * @returns {Promise<boolean>} - Whether permission is granted
 */
export const checkLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      const granted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      return granted;
    }
  } catch (err) {
    console.warn('Error checking location permission:', err);
    return false;
  }
};

/**
 * Get current location coordinates
 * @returns {Promise<{latitude: number, longitude: number} | null>} - Location coordinates or null
 */
export const getCurrentLocation = async () => {
  try {
    const location = await GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    });

    return {
      latitude: location.latitude,
      longitude: location.longitude,
    };
  } catch (error) {
    console.error('Error getting current location:', error);
    throw error;
  }
};

/**
 * Reverse geocode coordinates to get pincode using free service
 * @param {number} latitude - Latitude coordinate
 * @param {number} longitude - Longitude coordinate
 * @returns {Promise<string | null>} - Pincode or null
 */
export const reverseGeocode = async (latitude, longitude) => {
  try {
    // First try with Google Maps API if available
    const settings = getSettings();
    const googleApiKey = settings.google_maps_api_key;

    if (googleApiKey && googleApiKey !== 'YOUR_API_KEY') {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${googleApiKey}`
        );

        const data = await response.json();

        if (data.status === 'OK') {
          // Extract postal code from address components
          for (const result of data.results) {
            for (const component of result.address_components) {
              if (component.types.includes('postal_code')) {
                return component.long_name;
              }
            }
          }
        }
      } catch (error) {
        console.log('Google Maps API failed, trying fallback service');
      }
    }

    // Fallback to free reverse geocoding service
    const fallbackResponse = await fetch(
      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
    );

    const fallbackData = await fallbackResponse.json();

    if (fallbackData && fallbackData.postcode) {
      // Validate if it's a valid Indian pincode (6 digits)
      const pincode = fallbackData.postcode.toString();
      if (/^\d{6}$/.test(pincode)) {
        return pincode;
      }
    }

    console.log('No valid pincode found from reverse geocoding');
    return null;
  } catch (error) {
    console.error('Error in reverse geocoding:', error);
    return null;
  }
};

/**
 * Get pincode from current location
 * @returns {Promise<string | null>} - Pincode or null
 */
export const getPincodeFromLocation = async () => {
  try {
    console.log('🌍 Starting location fetch process...');

    // Check if we have permission
    const hasPermission = await checkLocationPermission();
    console.log('📍 Current permission status:', hasPermission);

    if (!hasPermission) {
      console.log('📍 Requesting location permission...');
      const granted = await requestLocationPermission();
      if (!granted) {
        console.log('❌ Location permission denied by user');
        Alert.alert(
          'Permission Required',
          'Location permission is required to fetch your current pincode. Please enable it in settings.',
          [{ text: 'OK' }]
        );
        return null;
      }
      console.log('✅ Location permission granted');
    }

    console.log('📍 Getting current location...');
    const location = await getCurrentLocation();
    if (!location) {
      console.log('❌ Failed to get current location');
      return null;
    }

    console.log('✅ Location obtained:', location);
    const { latitude, longitude } = location;

    console.log('🔍 Reverse geocoding coordinates...');
    const pincode = await reverseGeocode(latitude, longitude);

    if (pincode) {
      console.log('✅ Pincode found:', pincode);
      // Store the pincode for future use
      await storePincode(pincode);
    } else {
      console.log('❌ No pincode found from location');
      Alert.alert(
        'Location Error',
        'Unable to determine pincode from your current location. Please enter manually.',
        [{ text: 'OK' }]
      );
    }

    return pincode;
  } catch (error) {
    console.error('❌ Error getting pincode from location:', error);

    // Handle specific error types
    if (error.code === 'PERMISSION_DENIED') {
      Alert.alert(
        'Permission Denied',
        'Location permission was denied. Please enable it in settings to use this feature.',
        [{ text: 'OK' }]
      );
    } else if (error.code === 'POSITION_UNAVAILABLE') {
      Alert.alert(
        'Location Unavailable',
        'Unable to determine your location. Please check your GPS settings and try again.',
        [{ text: 'OK' }]
      );
    } else if (error.code === 'TIMEOUT') {
      Alert.alert(
        'Location Timeout',
        'Location request timed out. Please try again.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Location Error',
        'An error occurred while fetching your location. Please try again or enter pincode manually.',
        [{ text: 'OK' }]
      );
    }

    return null;
  }
};
