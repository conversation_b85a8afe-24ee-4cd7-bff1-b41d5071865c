{"name": "@appmaker-packages/theme-jockey-mobile", "version": "0.1.481", "main": "src/index.js", "description": "Appmaker", "dependencies": {"@react-navigation/material-top-tabs": "^6.6.14", "@twotalltotems/react-native-otp-input": "^1.3.11", "react-native-android-location-enabler": "^1.2.2", "react-native-circular-progress": "1.4.0", "react-native-collapsible": "^1.6.2", "react-native-event-listeners": "^1.0.7", "react-native-get-location": "^4.0.1", "react-native-material-ripple": "^0.9.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-responsive-screen": "^1.4.2", "react-native-shimmer-placeholder": "^2.0.9", "react-native-snap-carousel": "3.9.1", "react-native-snapmint3": "^1.0.49", "react-native-tab-view": "^3.5.2", "toggle-switch-react-native": "^3.3.0"}, "peerDependencies": {"@react-native-community/blur": "^4.4.1", "react-native-otp-verify": "^1.1.6"}, "expo": {"ios": {"infoPlist": {"NSPhotoLibraryUsageDescription": "This app needs access to your photo library to allow you to upload and share images.", "NSLocationWhenInUseUsageDescription": "This app uses your location to show nearby places and provide accurate delivery estimates.", "NSLocationAlwaysUsageDescription": "This app uses your location even when the app is in the background to track movement."}}, "android": {"permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "FOREGROUND_SERVICE"]}}, "expo_plugins": ["@appmaker-packages/theme-jockey-mobile/app.plugin.js"]}